module ApiMgmt::V4
  class ListingInGameMgmtController < ApplicationController
    skip_before_action :verify_authenticity_token

    def update
      @realty_game_listing = RealtyGameListing.find_by_uuid(
        params[:realty_game_listing_uuid]
      )
      @realty_game = @realty_game_listing&.realty_game
      @listing = @realty_game_listing&.sale_listing
      if @realty_game_listing
        rgl_data = params[:rgl_data] || {}
        # Update direct attributes
        @realty_game_listing.visible_in_game = rgl_data[:visible_in_game]
        @realty_game_listing.position_in_game = rgl_data[:position_in_game]
        @realty_game_listing.visible_photo_uuids = rgl_data[:visible_photo_uuids] if rgl_data[:visible_photo_uuids]
        @realty_game_listing.ordered_photo_uuids = rgl_data[:ordered_photo_uuids] if rgl_data[:ordered_photo_uuids]
        # Update store_attribute fields
        @realty_game_listing.gl_vicinity_atr = rgl_data[:gl_vicinity_atr] if rgl_data.key?(:gl_vicinity_atr)
        @realty_game_listing.gl_country_code_atr = rgl_data[:gl_country_code_atr] if rgl_data.key?(:gl_country_code_atr)
        @realty_game_listing.gl_image_url_atr = rgl_data[:gl_image_url_atr] if rgl_data.key?(:gl_image_url_atr)
        @realty_game_listing.gl_title_atr = rgl_data[:gl_title_atr] if rgl_data.key?(:gl_title_atr)
        @realty_game_listing.gl_description_atr = rgl_data[:gl_description_atr] if rgl_data.key?(:gl_description_atr)
        @realty_game_listing.save!
        render json: { realty_game_listing: @realty_game_listing }, status: :ok
      else
        render json: { error: 'realty_game_listing or listing not found' }, status: :not_found
      end
    rescue StandardError => e
      Rails.logger.error("Error in listing_visibility: #{e.message}")
      render json: { error: 'Internal server error' }, status: :internal_server_error
    end
  end
end
