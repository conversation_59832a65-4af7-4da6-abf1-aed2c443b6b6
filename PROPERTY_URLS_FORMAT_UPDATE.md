# Property URLs Format Update

## Overview

Updated the rake task `start_realty_game_from_url_generic_v3` and related services to support a new property URL format that includes contextual descriptions alongside URLs.

## Changes Made

### 1. Updated Input Format

**Old Format (still supported):**
```json
{
  "property_urls": [
    "https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/R5109379",
    "https://www.onthemarket.com/details/16670316/"
  ]
}
```

**New Format:**
```json
{
  "property_urls": [
    {
      "url": "https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/R5109379",
      "contextual_description": "Detached villa in Mijas, Costa del Sol - luxury Mediterranean living with private pool"
    },
    {
      "url": "https://www.onthemarket.com/details/16670316/",
      "contextual_description": "Premium estate in South Africa - 1.8 million luxury property with panoramic views"
    }
  ]
}
```

### 2. Files Modified

1. **`lib/tasks/2025_07_03_start_realty_game_from_url_generic_v3.rake`**
   - Added backward compatibility for both string and object formats
   - Enhanced logging to include contextual descriptions when available
   - Added property detail logging for better debugging

2. **`app/services/backend_callers/game_maker.rb`**
   - Updated `create_realty_game_with_pre_scraped_content` method
   - Updated `add_remaining_pre_scraped_listings` method
   - Added helper methods: `extract_url_from_property`, `extract_description_from_property`, `log_property_details`

3. **`app/services/realty_punts/realty_punt_creator.rb`**
   - Applied the same updates as GameMaker for consistency
   - Added the same helper methods for URL and description extraction

### 3. Example Files Created

1. **`db/realty_punts/latest_new_format_example.json`** - Example using new object format
2. **`db/realty_punts/latest_with_descriptions.json`** - Converted current data to new format
3. **`db/realty_punts/latest_backup.json`** - Backup of original file

### 4. Backward Compatibility

The implementation maintains full backward compatibility:
- Old format (array of strings) continues to work unchanged
- New format (array of objects) provides additional contextual information
- Both formats can be mixed within the same file

### 5. Enhanced Logging

The updated code now logs:
- Property URLs with their contextual descriptions (when available)
- Better debugging information for property processing
- Context information during listing addition process

## Usage

### Using the Rake Task

```bash
# With old format file (no changes needed)
rake h2c:start_realty_game_from_url_generic_v3

# With new format file (automatic detection)
rake h2c:start_realty_game_from_url_generic_v3
```

### Using the Services Directly

```ruby
# GameMaker service
service = BackendCallers::GameMaker.new(nil, logger)
result = service.create_realty_game_with_pre_scraped_content('db/realty_punts/latest_with_descriptions.json')

# RealtyPuntCreator service
creator = RealtyPunts::RealtyPuntCreator.new
result = creator.create_realty_game_with_pre_scraped_content('db/realty_punts/latest_with_descriptions.json')
```

## Benefits

1. **Enhanced Context**: Contextual descriptions provide better understanding of each property
2. **Better Logging**: More informative logs for debugging and monitoring
3. **Backward Compatibility**: Existing implementations continue to work
4. **Future Extensibility**: Object format allows for easy addition of more properties

## Migration Guide

To migrate from the old format to the new format:

1. Convert each URL string to an object with `url` and `contextual_description` properties
2. Add meaningful descriptions that help identify or categorize each property
3. Test with the new format to ensure everything works as expected

The system will automatically detect and handle both formats, so migration can be done gradually.
