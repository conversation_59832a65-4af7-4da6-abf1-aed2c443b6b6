require 'rails_helper'

RSpec.describe 'Cape Verde Property Integration', type: :request do
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:sample_url) { 'https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms' }
  
  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe 'End-to-end scraping workflow' do
    let(:sample_html) do
      File.read(Rails.root.join('spec', 'fixtures', 'files', 'capeverdeproperty_sample.html'))
    rescue Errno::ENOENT
      # Fallback to inline HTML if fixture file doesn't exist
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>CA OCEANO 2 BEDROOM APARTMENT</title>
          <link rel="canonical" href="https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms">
        </head>
        <body>
          <h1>CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA, SAL</h1>
          <h2>€125,000</h2>
          <h2>2 Bedroom Apartment For Sale</h2>
          
          <p>Sea Views, 2 bed first floor apartment located by the Budda Beach Hotel in the east of Santa Maria. Fully Furnished. Great location.</p>
          
          <img src="https://maps.googleapis.com/maps/api/staticmap?center=16.5963490599297,-22.8951838191986&zoom=13&size=2000x1000" alt="Map">
          
          <div class="main-features">
            <ul>
              <li>1 bathroom(s)</li>
              <li>Condition: Excellent</li>
              <li>Furnished</li>
              <li>Sea view</li>
              <li>Distance from the sea: Walking</li>
              <li>Distance from an airport: 15 mins</li>
            </ul>
          </div>
          
          <img src="https://wdcdn.co/Media/webp/l/6e70a3d7-8920-48e8-8409-46e1b1606436.jpg" alt="Property Image">
          <img src="https://wdcdn.co/Media/webp/l/4ef69a11-404b-4148-910f-0234126192ab.jpg" alt="Property Image">
          
          <h3>OFFICE DETAILS</h3>
          <div>
            <a href="/offices/estate-agents/cape-verde">Number 3, Residence Isla do Fogo, Rua 1 De Junho, Santa Maria, Sal, Cape Verde</a>
            <a href="tel:002382422041">00238 242 2041</a>
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </div>
        </body>
        </html>
      HTML
    end

    it 'completes full scraping workflow successfully' do
      # Step 1: Create scrape item
      scrape_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      expect(scrape_item).to be_persisted
      expect(scrape_item.scrape_is_capeverdeproperty).to be true

      # Step 2: Simulate content retrieval
      scrape_item.update!(
        full_content_before_js: sample_html,
        full_content_before_js_length: sample_html.length,
        is_valid_scrape: true,
        content_is_html: true
      )

      # Step 3: Extract property data
      property_data = scrape_item.property_hash_from_scrape_item
      expect(property_data).to be_a(Hash)
      expect(property_data['listing_data']).to be_present
      expect(property_data['asset_data']).to be_present

      # Step 4: Validate extracted data quality
      listing_data = property_data['listing_data']
      asset_data = property_data['asset_data']

      expect(listing_data['title']).to include('CA OCEANO')
      expect(listing_data['price_sale_current_cents']).to be > 0
      expect(listing_data['description']).to include('Sea Views')
      
      expect(asset_data['count_bedrooms']).to eq(2)
      expect(asset_data['count_bathrooms']).to eq(1.0)
      expect(asset_data['city']).to eq('SANTA MARIA')
      expect(asset_data['region']).to eq('SAL')
      expect(asset_data['country']).to eq('Cape Verde')

      # Step 5: Test pasarela transformation
      pasarela_result = Pasarelas::CapeverdepropertyPasarela.extract_listing_and_asset_from_scrape_item(scrape_item)
      expect(pasarela_result[:listing_data]).to be_a(Hash)
      expect(pasarela_result[:asset_data]).to be_a(Hash)
      expect(pasarela_result[:images]).to be_an(Array)
    end

    it 'handles missing or invalid content gracefully' do
      scrape_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      
      # Test with insufficient content
      scrape_item.update!(full_content_before_js: 'short content')
      
      expect {
        scrape_item.property_hash_from_scrape_item
      }.to raise_error(/full_content_before_js unavailable or too short/)
    end

    it 'extracts images correctly' do
      scrape_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      scrape_item.update!(
        full_content_before_js: sample_html,
        is_valid_scrape: true
      )

      property_data = scrape_item.property_hash_from_scrape_item
      image_urls = property_data['listing_image_urls']

      expect(image_urls).to be_an(Array)
      expect(image_urls).not_to be_empty
      
      image_urls.each do |url|
        expect(url).to include('wdcdn.co')
        expect(url).to end_with('.jpg') # Should be converted from webp
      end
    end
  end

  describe 'Portal configuration integration' do
    it 'has correct portal configuration' do
      config = RealtyScrapedItem.portal_config['capeverdeproperty']
      
      expect(config).to be_present
      expect(config[:scrape_class]).to eq('ScrapeItemFromCapeverdeproperty')
      expect(config[:connector]).to eq('ScraperConnectors::Regular')
      expect(config[:method]).to eq(:find_or_create_for_h2c_capeverdeproperty)
      expect(config[:pasarela]).to eq('Pasarelas::CapeverdepropertyPasarela')
      expect(config[:scraped_content_column_name]).to eq('full_content_before_js')
      expect(config[:include_trailing_slash]).to be false
    end

    it 'can instantiate all configured classes' do
      config = RealtyScrapedItem.portal_config['capeverdeproperty']
      
      # Test scrape class
      expect(config[:scrape_class].constantize).to eq(ScrapeItemFromCapeverdeproperty)
      
      # Test pasarela class
      expect(config[:pasarela].constantize).to eq(Pasarelas::CapeverdepropertyPasarela)
      
      # Test connector class
      expect(config[:connector].constantize).to eq(ScraperConnectors::Regular)
    end
  end

  describe 'Search functionality integration' do
    let(:search_url) { 'https://www.capeverdeproperty.co.uk/buy/property-for-sale/' }
    let(:search_html) do
      <<~HTML
        <!DOCTYPE html>
        <html>
        <body>
          <div class="search-results">
            <div class="property-item">
              <h3><a href="/property/test123/cv/sal/santa-maria/test-property/apartment/2-bedrooms">Test Property Santa Maria, Sal</a></h3>
              <div class="price">€100,000</div>
              <div class="beds">2 bedrooms</div>
              <div class="baths">1 bathroom</div>
              <img src="https://wdcdn.co/Media/webp/l/test-image.jpg" alt="Property">
            </div>
          </div>
        </body>
        </html>
      HTML
    end

    it 'processes search results correctly' do
      search_scrape = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(search_url)
      expect(search_scrape).to be_persisted
      expect(search_scrape.scrape_is_capeverdeproperty).to be true
      expect(search_scrape.is_realty_search_scrape).to be true

      search_scrape.update!(
        full_content_before_js: search_html,
        is_valid_scrape: true
      )

      doc = search_scrape.raw_contents_as_html_doc
      expect(doc).to be_a(Nokogiri::HTML::Document)
      expect(doc.css('.property-item')).not_to be_empty
    end
  end

  describe 'Parser service integration' do
    it 'extracts comprehensive property data' do
      parser_result = RealtyParsers::ParseCapeverdepropertyListingsHtml.property_hash_from_html(sample_html)
      
      expect(parser_result).to include(
        'title', 'description', 'price_raw', 'bedrooms', 'bathrooms',
        'property_type', 'features', 'images', 'agent_info'
      )
      
      expect(parser_result['title']).to include('CA OCEANO')
      expect(parser_result['price_raw']).to eq(125000)
      expect(parser_result['bedrooms']).to eq(2)
      expect(parser_result['bathrooms']).to eq(1)
      expect(parser_result['property_type']).to eq('apartment')
      expect(parser_result['features']).to include('Furnished', 'Sea view')
      expect(parser_result['city']).to eq('SANTA MARIA')
      expect(parser_result['region']).to eq('SAL')
    end
  end

  describe 'Error handling integration' do
    it 'handles network errors gracefully' do
      scrape_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      
      # Simulate network error by setting empty content
      scrape_item.update!(full_content_before_js: '')
      
      expect {
        scrape_item.property_hash_from_scrape_item
      }.to raise_error(/full_content_before_js unavailable or too short/)
    end

    it 'handles malformed HTML gracefully' do
      scrape_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      
      # Set malformed HTML
      malformed_html = '<html><body><h1>Broken HTML without closing tags'
      scrape_item.update!(full_content_before_js: malformed_html)
      
      # Should not raise error, but might return minimal data
      expect {
        scrape_item.property_hash_from_scrape_item
      }.not_to raise_error
    end

    it 'handles missing price information' do
      html_without_price = sample_html.gsub(/€125,000/, '')
      
      parser_result = RealtyParsers::ParseCapeverdepropertyListingsHtml.property_hash_from_html(html_without_price)
      
      expect(parser_result['price_raw']).to eq(0)
      expect(parser_result['price_display']).to eq('Price on request')
    end
  end

  describe 'Performance considerations' do
    it 'processes large HTML content efficiently' do
      # Create large HTML content
      large_html = sample_html + ('<!-- Extra content -->' * 1000)
      
      start_time = Time.current
      
      parser_result = RealtyParsers::ParseCapeverdepropertyListingsHtml.property_hash_from_html(large_html)
      
      end_time = Time.current
      processing_time = end_time - start_time
      
      expect(processing_time).to be < 5.seconds # Should process within 5 seconds
      expect(parser_result['title']).to be_present
    end
  end
end
