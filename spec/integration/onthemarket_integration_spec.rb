require 'rails_helper'

RSpec.describe 'OnTheMarket Integration', type: :integration do
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:sample_property_url) { 'https://www.onthemarket.com/details/12345678/' }
  let(:sample_search_url) { 'https://www.onthemarket.com/for-sale/property/london/' }

  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe 'property page scraping workflow' do
    context 'with successful scraping', :vcr do
      it 'completes full workflow from URL to property data' do
        # Step 1: Create scrape item
        scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(sample_property_url)
        
        expect(scrape_item).to be_persisted
        expect(scrape_item.scrapable_url).to eq(sample_property_url)
        expect(scrape_item.scrape_is_onthemarket).to be true
        
        # Step 2: Mock content retrieval (to avoid external calls in tests)
        mock_html = file_fixture('onthemarket_sample.html').read
        scrape_item.update!(
          full_content_before_js: mock_html,
          is_valid_scrape: true,
          full_content_before_js_length: mock_html.length
        )
        
        # Step 3: Extract property data
        property_hash = scrape_item.property_hash_from_scrape_item
        
        expect(property_hash).to be_a(Hash)
        expect(property_hash).to have_key('asset_data')
        expect(property_hash).to have_key('listing_data')
        expect(property_hash).to have_key('listing_image_urls')
        
        # Validate asset data structure
        asset_data = property_hash['asset_data']
        expect(asset_data['reference']).to eq('12345678')
        expect(asset_data['count_bedrooms']).to eq(3)
        expect(asset_data['count_bathrooms']).to eq(2.0)
        expect(asset_data['city']).to eq('Sample Town')
        expect(asset_data['description']).to include('stunning family home')
        
        # Validate listing data structure
        listing_data = property_hash['listing_data']
        expect(listing_data['reference']).to eq('12345678')
        expect(listing_data['price_sale_current_cents']).to eq(45000000) # £450,000 in cents
        expect(listing_data['currency']).to eq('GBP')
        expect(listing_data['title']).to eq('Beautiful 3-bedroom house')
        
        # Validate image extraction
        images = property_hash['listing_image_urls']
        expect(images).to be_an(Array)
        expect(images).not_to be_empty
        expect(images.first).to match(/^https?:\/\//)
      end
    end

    context 'with error scenarios' do
      it 'handles scraping failures gracefully' do
        scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(sample_property_url)
        
        # Simulate scraping failure
        scrape_item.update!(
          is_valid_scrape: false,
          scrape_failure_message: 'HTTP 404 Not Found',
          full_content_before_js: nil
        )
        
        expect {
          scrape_item.property_hash_from_scrape_item
        }.to raise_error('full_content_before_js unavailabl or too short')
      end

      it 'handles malformed content gracefully' do
        scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(sample_property_url)
        
        # Provide malformed HTML
        malformed_html = '<html><script id="__NEXT_DATA__">invalid json</script></html>' * 50
        scrape_item.update!(full_content_before_js: malformed_html)
        
        expect {
          scrape_item.property_hash_from_scrape_item
        }.to raise_error(JSON::ParserError)
      end
    end
  end

  describe 'search page scraping workflow' do
    context 'with successful search scraping' do
      it 'processes search results correctly' do
        # Step 1: Create search scrape item
        search_item = ScrapeItemFromOtmSearch.find_or_create_for_otm_search(sample_search_url)
        
        expect(search_item).to be_persisted
        expect(search_item.scrapable_url).to eq(sample_search_url)
        expect(search_item.is_realty_search_scrape).to be true
        expect(search_item.scrape_is_onthemarket).to be true
        
        # Step 2: Mock search results content
        mock_search_results = {
          'properties' => [
            {
              'id' => '11111111',
              'property-title' => 'Search Result Property 1',
              'price' => '£350,000',
              'bedrooms' => 2,
              'bathrooms' => 1,
              'display_address' => '789 Search Street, London',
              'postcode1' => 'SW1A',
              'location' => { 'lat' => 51.5074, 'lon' => -0.1278 },
              'images' => [{ 'default' => 'https://example.com/search1.jpg' }],
              'agent' => { 'name' => 'Search Agent 1' }
            },
            {
              'id' => '22222222',
              'property-title' => 'Search Result Property 2',
              'price' => '£275,000',
              'bedrooms' => 1,
              'bathrooms' => 1,
              'display_address' => '321 Another Street, London',
              'postcode1' => 'E1 6AN',
              'location' => { 'lat' => 51.5155, 'lon' => -0.0922 },
              'images' => [{ 'default' => 'https://example.com/search2.jpg' }],
              'agent' => { 'name' => 'Search Agent 2' }
            }
          ]
        }.to_json
        
        search_item.update!(full_content_before_js: mock_search_results)
        
        # Step 3: Extract summary listings
        listings = search_item.summary_sale_listings_from_scrape_item
        
        expect(listings).to be_an(Array)
        expect(listings.length).to eq(2)
        
        # Validate first listing
        first_listing = listings.first
        expect(first_listing[:summary_listing_reference]).to eq('11111111')
        expect(first_listing[:summary_listing_price]).to eq('350000')
        expect(first_listing[:summary_listing_bedrooms_count]).to eq(2)
        expect(first_listing[:full_listing_url]).to eq('https://www.onthemarket.com/details/11111111/')
        expect(first_listing[:summary_listing_latitude]).to eq(51.5074)
        expect(first_listing[:summary_listing_longitude]).to eq(-0.1278)
        
        # Validate second listing
        second_listing = listings.second
        expect(second_listing[:summary_listing_reference]).to eq('22222222')
        expect(second_listing[:summary_listing_price]).to eq('275000')
        expect(second_listing[:summary_listing_bedrooms_count]).to eq(1)
      end
    end
  end

  describe 'data mapping and validation' do
    let(:scrape_item) { ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(sample_property_url) }
    
    before do
      mock_html = file_fixture('onthemarket_sample.html').read
      scrape_item.update!(full_content_before_js: mock_html)
    end

    it 'produces schema-compliant asset data' do
      property_hash = scrape_item.property_hash_from_scrape_item
      asset_data = property_hash['asset_data']
      
      # Required fields
      required_fields = %w[
        reference title description count_bedrooms count_bathrooms
        city street_address latitude longitude categories
      ]
      
      required_fields.each do |field|
        expect(asset_data).to have_key(field), "Missing required asset field: #{field}"
      end
      
      # Data type validation
      expect(asset_data['count_bedrooms']).to be_a(Integer)
      expect(asset_data['count_bathrooms']).to be_a(Float).or be_a(Integer)
      expect(asset_data['latitude']).to be_a(Float).or be_a(Integer)
      expect(asset_data['longitude']).to be_a(Float).or be_a(Integer)
      expect(asset_data['categories']).to be_an(Array)
    end

    it 'produces schema-compliant listing data' do
      property_hash = scrape_item.property_hash_from_scrape_item
      listing_data = property_hash['listing_data']
      
      # Required fields
      required_fields = %w[
        reference title description price_sale_current_cents currency
        visible archived
      ]
      
      required_fields.each do |field|
        expect(listing_data).to have_key(field), "Missing required listing field: #{field}"
      end
      
      # Data type validation
      expect(listing_data['price_sale_current_cents']).to be_a(Integer)
      expect(listing_data['visible']).to be(true).or be(false)
      expect(listing_data['archived']).to be(true).or be(false)
      expect(listing_data['currency']).to eq('GBP')
    end

    it 'handles edge cases in data mapping' do
      # Test with minimal property data
      minimal_property = {
        'id' => '99999999',
        'priceRaw' => 100000
      }
      
      asset_data = scrape_item.send(:map_property_to_asset_schema, minimal_property, {})
      listing_data = scrape_item.send(:map_property_to_listing_schema, minimal_property)
      
      # Should not crash and should provide sensible defaults
      expect(asset_data['reference']).to eq('99999999')
      expect(asset_data['count_bedrooms']).to eq(0)
      expect(asset_data['count_bathrooms']).to eq(0.0)
      
      expect(listing_data['reference']).to eq('99999999')
      expect(listing_data['price_sale_current_cents']).to eq(10000000)
      expect(listing_data['visible']).to be true
    end
  end

  describe 'performance and reliability' do
    it 'handles multiple simultaneous scrapes without conflicts' do
      urls = [
        'https://www.onthemarket.com/details/11111111/',
        'https://www.onthemarket.com/details/22222222/',
        'https://www.onthemarket.com/details/33333333/'
      ]
      
      scrape_items = urls.map do |url|
        ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(url)
      end
      
      expect(scrape_items.map(&:id).uniq.length).to eq(3)
      expect(scrape_items.all?(&:scrape_is_onthemarket)).to be true
    end

    it 'maintains data integrity across multiple operations' do
      scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(sample_property_url)
      original_id = scrape_item.id
      
      # Multiple find_or_create calls should return same item
      5.times do
        item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(sample_property_url)
        expect(item.id).to eq(original_id)
      end
    end
  end

  describe 'error recovery and resilience' do
    it 'recovers gracefully from network timeouts' do
      scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(sample_property_url)
      
      # Simulate network timeout
      allow_any_instance_of(ScraperConnectors::Regular)
        .to receive(:noko_doc_string_from)
        .and_raise(Timeout::Error.new('Network timeout'))
      
      expect {
        scrape_item.retrieve_and_set_content_object('ScraperConnectors::Regular')
      }.to raise_error(Timeout::Error)
      
      # Item should still be valid for retry
      expect(scrape_item).to be_persisted
      expect(scrape_item.scrapable_url).to be_present
    end

    it 'handles content structure changes gracefully' do
      scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(sample_property_url)
      
      # HTML with different structure (e.g., future OnTheMarket changes)
      changed_structure_html = <<~HTML
        <html>
          <head><title>Changed Structure</title></head>
          <body>
            <script id="__NEXT_DATA__">
            {
              "props": {
                "newStructure": {
                  "propertyData": {
                    "id": "12345678",
                    "title": "Test Property"
                  }
                }
              }
            }
            </script>
            #{'<p>Content</p>' * 200}
          </body>
        </html>
      HTML
      
      scrape_item.update!(full_content_before_js: changed_structure_html)
      
      # Should handle missing expected structure without crashing
      expect {
        scrape_item.property_hash_from_scrape_item
      }.to raise_error # Expected for now, but should be graceful in production
    end
  end
end
