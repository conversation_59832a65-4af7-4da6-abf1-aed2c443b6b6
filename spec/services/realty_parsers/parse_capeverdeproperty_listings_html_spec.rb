require 'rails_helper'

RSpec.describe RealtyParsers::ParseCapeverdepropertyListingsHtml do
  let(:sample_html) do
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <title>CA OCEANO 2 BEDROOM APARTMENT</title>
        <link rel="canonical" href="https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms">
      </head>
      <body>
        <h1>CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA, SAL</h1>
        <h2>€125,000</h2>
        <h2>2 Bedroom Apartment For Sale</h2>
        <h3>Tenure: Freehold</h3>
        
        <p>Sea Views, 2 bed first floor apartment located by the Budda Beach Hotel in the east of Santa Maria. Fully Furnished. Great location.</p>
        
        <img src="https://maps.googleapis.com/maps/api/staticmap?center=16.5963490599297,-22.8951838191986&zoom=13&size=2000x1000" alt="Map">
        
        <div class="main-features">
          <ul>
            <li>1 bathroom(s)</li>
            <li>Condition: Excellent</li>
            <li>Furnished</li>
            <li>Sea view</li>
            <li>Distance from the sea: Walking</li>
            <li>Distance from an airport: 15 mins</li>
          </ul>
        </div>
        
        <img src="https://wdcdn.co/Media/webp/l/6e70a3d7-8920-48e8-8409-46e1b1606436.jpg" alt="Property Image">
        <img src="https://wdcdn.co/Media/webp/l/4ef69a11-404b-4148-910f-0234126192ab.jpg" alt="Property Image">
        
        <h3>OFFICE DETAILS</h3>
        <div>
          <a href="/offices/estate-agents/cape-verde">Number 3, Residence Isla do Fogo, Rua 1 De Junho, Santa Maria, Sal, Cape Verde</a>
          <a href="tel:002382422041">00238 242 2041</a>
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>
      </body>
      </html>
    HTML
  end

  describe '.property_hash_from_html' do
    it 'extracts property data from HTML content' do
      result = described_class.property_hash_from_html(sample_html)
      
      expect(result).to be_a(Hash)
      expect(result['title']).to eq('CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA, SAL')
      expect(result['price_raw']).to eq(125000)
      expect(result['bedrooms']).to eq(2)
      expect(result['bathrooms']).to eq(1)
    end
  end

  describe '#extract_property_data' do
    let(:parser) { described_class.new(sample_html) }

    it 'returns complete property data hash' do
      result = parser.extract_property_data
      
      expect(result).to include('title', 'price_raw', 'bedrooms', 'bathrooms', 'features', 'images')
      expect(result['title']).to be_present
      expect(result['price_raw']).to be_a(Integer)
      expect(result['features']).to be_an(Array)
    end
  end

  describe 'private extraction methods' do
    let(:parser) { described_class.new(sample_html) }

    describe '#extract_title' do
      it 'extracts title from H1 element' do
        title = parser.send(:extract_title)
        expect(title).to eq('CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA, SAL')
      end
    end

    describe '#extract_description' do
      it 'extracts meaningful description text' do
        description = parser.send(:extract_description)
        expect(description).to include('Sea Views')
        expect(description).to include('Fully Furnished')
      end

      it 'filters out unwanted content' do
        description = parser.send(:extract_description)
        expect(description).not_to include('©')
        expect(description).not_to include('cookie')
      end
    end

    describe '#extract_property_type' do
      it 'identifies apartment from title' do
        property_type = parser.send(:extract_property_type)
        expect(property_type).to eq('apartment')
      end

      context 'with villa in title' do
        let(:villa_html) { sample_html.gsub('APARTMENT', 'VILLA') }
        let(:parser) { described_class.new(villa_html) }

        it 'identifies villa' do
          property_type = parser.send(:extract_property_type)
          expect(property_type).to eq('villa')
        end
      end

      context 'with plot in title' do
        let(:plot_html) { sample_html.gsub('APARTMENT', 'PLOT') }
        let(:parser) { described_class.new(plot_html) }

        it 'identifies plot' do
          property_type = parser.send(:extract_property_type)
          expect(property_type).to eq('plot')
        end
      end
    end

    describe '#extract_bedrooms' do
      it 'extracts bedroom count from title' do
        bedrooms = parser.send(:extract_bedrooms)
        expect(bedrooms).to eq(2)
      end

      context 'when bedrooms not in title' do
        let(:no_bedroom_html) { sample_html.gsub('2 BEDROOM', '') }
        let(:parser) { described_class.new(no_bedroom_html) }

        it 'looks in features section' do
          # This would find "2 bed" in the description
          bedrooms = parser.send(:extract_bedrooms)
          expect(bedrooms).to eq(2) # From "2 bed first floor apartment" in description
        end
      end
    end

    describe '#extract_bathrooms' do
      it 'extracts bathroom count from features' do
        bathrooms = parser.send(:extract_bathrooms)
        expect(bathrooms).to eq(1)
      end
    end

    describe '#extract_reference' do
      it 'extracts reference from canonical URL' do
        reference = parser.send(:extract_reference)
        expect(reference).to eq('cv353caoceanocvp4')
      end

      context 'when no canonical URL' do
        let(:no_canonical_html) { sample_html.gsub(/<link.*canonical.*>/, '') }
        let(:parser) { described_class.new(no_canonical_html) }

        it 'generates reference from title' do
          reference = parser.send(:extract_reference)
          expect(reference).to start_with('cvp_')
          expect(reference).to include('caoceanoapar')
        end
      end
    end

    describe '#extract_price_info' do
      it 'extracts price information' do
        price_info = parser.send(:extract_price_info)
        
        expect(price_info['price_raw']).to eq(125000)
        expect(price_info['price_display']).to eq('€125,000')
        expect(price_info['currency']).to eq('EUR')
      end

      context 'when no price found' do
        let(:no_price_html) { sample_html.gsub('€125,000', '') }
        let(:parser) { described_class.new(no_price_html) }

        it 'returns default price info' do
          price_info = parser.send(:extract_price_info)
          
          expect(price_info['price_raw']).to eq(0)
          expect(price_info['price_display']).to eq('Price on request')
          expect(price_info['currency']).to eq('EUR')
        end
      end
    end

    describe '#extract_location_info' do
      it 'extracts location from title' do
        location_info = parser.send(:extract_location_info)
        
        expect(location_info['city']).to eq('SANTA MARIA')
        expect(location_info['region']).to eq('SAL')
        expect(location_info['country']).to eq('Cape Verde')
      end

      it 'extracts coordinates from Google Maps' do
        location_info = parser.send(:extract_location_info)
        
        expect(location_info['latitude']).to eq(16.5963490599297)
        expect(location_info['longitude']).to eq(-22.8951838191986)
      end
    end

    describe '#extract_features' do
      it 'extracts features from list items' do
        features_info = parser.send(:extract_features)
        features = features_info['features']
        
        expect(features).to include('1 bathroom(s)')
        expect(features).to include('Condition: Excellent')
        expect(features).to include('Furnished')
        expect(features).to include('Sea view')
      end

      it 'detects furnished and sea view flags' do
        features_info = parser.send(:extract_features)
        
        expect(features_info['furnished']).to be true
        expect(features_info['sea_view']).to be true
      end

      it 'adds features from description' do
        features_info = parser.send(:extract_features)
        features = features_info['features']
        
        expect(features).to include('Sea view')
        expect(features).to include('Furnished')
      end
    end

    describe '#extract_agent_info' do
      it 'extracts agent contact details' do
        agent_info = parser.send(:extract_agent_info)
        
        expect(agent_info['company_name']).to eq('Cape Verde Property')
        expect(agent_info['office_address']).to include('Number 3, Residence Isla do Fogo')
        expect(agent_info['phone']).to eq('00238 242 2041')
        expect(agent_info['email']).to eq('<EMAIL>')
      end

      it 'provides default values when office details missing' do
        no_office_html = sample_html.gsub(/OFFICE DETAILS.*?<\/div>/m, '')
        parser = described_class.new(no_office_html)
        
        agent_info = parser.send(:extract_agent_info)
        
        expect(agent_info['company_name']).to eq('Cape Verde Property')
        expect(agent_info['email']).to eq('<EMAIL>')
      end
    end

    describe '#extract_images' do
      it 'extracts image URLs' do
        image_info = parser.send(:extract_images)
        images = image_info['images']
        
        expect(images).to be_an(Array)
        expect(images.length).to eq(2)
        expect(images.first).to include('wdcdn.co')
        expect(images.first).to end_with('.jpg') # Converted from .webp
      end

      it 'converts webp to jpg format' do
        image_info = parser.send(:extract_images)
        images = image_info['images']
        
        images.each do |image_url|
          expect(image_url).not_to include('.webp')
          expect(image_url).to include('/l/') # High quality path
        end
      end

      it 'provides image count' do
        image_info = parser.send(:extract_images)
        
        expect(image_info['image_count']).to eq(2)
      end
    end
  end

  describe 'utility methods' do
    let(:parser) { described_class.new(sample_html) }

    describe '#clean_text' do
      it 'cleans and normalizes text' do
        dirty_text = "  Text   with\nmultiple   spaces\tand special chars!@#  "
        cleaned = parser.send(:clean_text, dirty_text)
        
        expect(cleaned).to eq('Text with multiple spaces and special chars!')
      end

      it 'handles nil input' do
        cleaned = parser.send(:clean_text, nil)
        expect(cleaned).to eq('')
      end
    end

    describe '#extract_number' do
      it 'extracts first number from text' do
        number = parser.send(:extract_number, 'Price: €125,000 for this property')
        expect(number).to eq(125)
      end

      it 'returns 0 for no numbers' do
        number = parser.send(:extract_number, 'No numbers here')
        expect(number).to eq(0)
      end

      it 'handles nil input' do
        number = parser.send(:extract_number, nil)
        expect(number).to eq(0)
      end
    end
  end

  context 'with minimal HTML' do
    let(:minimal_html) do
      <<~HTML
        <html>
        <body>
          <h1>Basic Property</h1>
          <p>Simple description</p>
        </body>
        </html>
      HTML
    end

    it 'handles minimal content gracefully' do
      result = described_class.property_hash_from_html(minimal_html)
      
      expect(result['title']).to eq('Basic Property')
      expect(result['description']).to eq('Simple description')
      expect(result['bedrooms']).to eq(0)
      expect(result['bathrooms']).to eq(0)
      expect(result['features']).to eq([])
      expect(result['images']).to eq([])
    end
  end

  context 'with malformed HTML' do
    let(:malformed_html) { '<html><body><h1>Broken' }

    it 'handles malformed HTML without errors' do
      expect {
        described_class.property_hash_from_html(malformed_html)
      }.not_to raise_error
    end
  end
end
