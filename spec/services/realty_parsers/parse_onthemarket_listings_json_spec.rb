require 'rails_helper'

RSpec.describe RealtyParsers::ParseOnthemarketListingsJson do
  let(:parser) { described_class.new }
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:mock_scrape_item) { create(:scrape_item) }

  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe '#property_hash_from_scrape_item' do
    context 'with valid content' do
      before do
        mock_scrape_item.update!(
          full_content_before_js: sample_onthemarket_html,
          scrapable_url: 'https://www.onthemarket.com/details/12345678/'
        )
      end

      it 'returns property hash when content is present' do
        result = parser.property_hash_from_scrape_item(mock_scrape_item)
        
        expect(result).to be_a(Hash)
        expect(result).to have_key('asset_data')
        expect(result).to have_key('listing_data')
      end

      it 'extracts canonical URL' do
        result = parser.property_hash_from_scrape_item(mock_scrape_item)
        expect(result['import_url']).to be_present
      end

      it 'processes dataLayer information when available' do
        html_with_datalayer = sample_onthemarket_html_with_datalayer
        mock_scrape_item.update!(full_content_before_js: html_with_datalayer)
        
        result = parser.property_hash_from_scrape_item(mock_scrape_item)
        expect(result[:postal_code]).to be_present
      end
    end

    context 'without valid content' do
      it 'returns nil when full_content_before_js is missing' do
        mock_scrape_item.update!(full_content_before_js: nil)
        
        result = parser.property_hash_from_scrape_item(mock_scrape_item)
        expect(result).to be_nil
      end

      it 'returns nil when full_content_before_js is empty' do
        mock_scrape_item.update!(full_content_before_js: '')
        
        result = parser.property_hash_from_scrape_item(mock_scrape_item)
        expect(result).to be_nil
      end
    end
  end

  describe '#get_property_hash_from_html' do
    let(:top_level_url) { 'https://www.onthemarket.com' }

    context 'with complete HTML' do
      it 'extracts property data successfully' do
        result = parser.get_property_hash_from_html(sample_onthemarket_html, top_level_url)
        
        expect(result).to be_a(Hash)
        expect(result).to have_key(:asset_data)
        expect(result).to have_key(:listing_data)
        expect(result).to have_key(:listing_image_urls)
      end

      it 'extracts images from CSS selectors' do
        result = parser.get_property_hash_from_html(sample_onthemarket_html, top_level_url)
        images = result[:listing_image_urls]
        
        expect(images).to be_an(Array)
      end

      it 'processes related URLs from property data' do
        result = parser.get_property_hash_from_html(sample_onthemarket_html, top_level_url)
        asset_data = result[:asset_data]
        
        expect(asset_data['related_urls_from_otm']).to have_key(:property_documents)
        expect(asset_data['related_urls_from_otm']).to have_key(:other_otm_urls)
      end
    end

    context 'with dataLayer script' do
      it 'extracts postal code from dataLayer' do
        html_with_datalayer = sample_onthemarket_html_with_datalayer
        result = parser.get_property_hash_from_html(html_with_datalayer, top_level_url)
        
        expect(result[:postal_code]).to be_present
      end

      it 'handles malformed dataLayer JSON gracefully' do
        html_with_bad_datalayer = sample_onthemarket_html_with_malformed_datalayer
        
        expect {
          parser.get_property_hash_from_html(html_with_bad_datalayer, top_level_url)
        }.not_to raise_error
      end
    end

    context 'with missing or invalid JSON' do
      it 'raises error when __NEXT_DATA__ script is missing' do
        html_without_json = '<html><body>No JSON script here</body></html>'
        
        expect {
          parser.get_property_hash_from_html(html_without_json, top_level_url)
        }.to raise_error
      end

      it 'raises error when JSON is malformed' do
        html_with_bad_json = <<~HTML
          <html>
            <body>
              <script id="__NEXT_DATA__">{"invalid": json syntax}</script>
            </body>
          </html>
        HTML
        
        expect {
          parser.get_property_hash_from_html(html_with_bad_json, top_level_url)
        }.to raise_error(JSON::ParserError)
      end
    end
  end

  describe 'private mapping methods' do
    let(:sample_property_data) do
      {
        'id' => '12345678',
        'propertyTitle' => 'Test Property',
        'description' => 'A test property description',
        'bedrooms' => 3,
        'bathrooms' => 2,
        'priceRaw' => 350000,
        'displayAddress' => '123 Test Street',
        'addressLocality' => 'Test Town',
        'humanisedPropertyType' => 'terraced-house',
        'images' => [
          { 'largeUrl' => 'https://example.com/img1.jpg' },
          { 'url' => 'https://example.com/img2.jpg' }
        ],
        'features' => [
          { 'id' => 'garden', 'feature' => 'Garden' }
        ],
        'location' => {
          'lat' => 51.5074,
          'lon' => -0.1278
        },
        'agent' => {
          'ukCountry' => 'england'
        },
        'rooms' => {
          'descriptions' => [
            { 'name' => 'living_room', 'description' => 'Spacious living room' }
          ]
        }
      }
    end

    describe '#map_property_to_asset_schema' do
      let(:related_urls) { { property_documents: [], other_otm_urls: [] } }

      it 'maps all required fields correctly' do
        asset_data = parser.send(:map_property_to_asset_schema, sample_property_data, related_urls)
        
        expect(asset_data['count_bedrooms']).to eq(3)
        expect(asset_data['count_bathrooms']).to eq(2.0)
        expect(asset_data['city']).to eq('Test Town')
        expect(asset_data['city_search_key']).to eq('test-town')
        expect(asset_data['latitude']).to eq(51.5074)
        expect(asset_data['longitude']).to eq(-0.1278)
        expect(asset_data['description']).to eq('A test property description')
        expect(asset_data['street_address']).to eq('123 Test Street')
        expect(asset_data['title']).to eq('Test Property')
        expect(asset_data['reference']).to eq('12345678')
      end

      it 'handles missing optional fields gracefully' do
        minimal_data = { 'id' => '123' }
        asset_data = parser.send(:map_property_to_asset_schema, minimal_data, related_urls)
        
        expect(asset_data['count_bedrooms']).to eq(0)
        expect(asset_data['count_bathrooms']).to eq(0.0)
        expect(asset_data['city']).to be_nil
        expect(asset_data['city_search_key']).to eq('')
      end

      it 'processes features into categories' do
        asset_data = parser.send(:map_property_to_asset_schema, sample_property_data, related_urls)
        categories = asset_data['categories']
        
        expect(categories).to be_an(Array)
        expect(categories.first).to eq({ 'id' => 'garden', 'name' => 'Garden' })
      end

      it 'infers garage count from description' do
        data_with_garage = sample_property_data.merge('description' => 'Property with garage space')
        asset_data = parser.send(:map_property_to_asset_schema, data_with_garage, related_urls)
        
        expect(asset_data['count_garages']).to eq(1)
      end

      it 'processes room details' do
        asset_data = parser.send(:map_property_to_asset_schema, sample_property_data, related_urls)
        details = asset_data['details']
        
        expect(details).to be_a(Hash)
        expect(details['living_room']).to eq({ 'name' => 'living_room', 'description' => 'Spacious living room' })
      end
    end

    describe '#map_property_to_listing_schema' do
      it 'maps all required fields correctly' do
        listing_data = parser.send(:map_property_to_listing_schema, sample_property_data)
        
        expect(listing_data['title']).to eq('Test Property')
        expect(listing_data['description']).to eq('A test property description')
        expect(listing_data['price_sale_current_cents']).to eq(35000000) # 350000 * 100
        expect(listing_data['currency']).to eq('GBP')
        expect(listing_data['reference']).to eq('12345678')
        expect(listing_data['listing_slug']).to eq('12345678')
      end

      it 'sets appropriate boolean defaults' do
        listing_data = parser.send(:map_property_to_listing_schema, sample_property_data)
        
        expect(listing_data['archived']).to be false
        expect(listing_data['visible']).to be true
        expect(listing_data['furnished']).to be false
        expect(listing_data['reserved']).to be false
      end

      it 'processes sale listing features' do
        listing_data = parser.send(:map_property_to_listing_schema, sample_property_data)
        features = listing_data['sale_listing_features']
        
        expect(features).to be_a(Hash)
        expect(features['garden']).to eq('Garden')
      end

      it 'handles video tour URL' do
        data_with_video = sample_property_data.merge(
          'videotours' => [{ 'url' => 'https://example.com/video.mp4' }]
        )
        listing_data = parser.send(:map_property_to_listing_schema, data_with_video)
        
        expect(listing_data['main_video_url']).to eq('https://example.com/video.mp4')
      end

      it 'processes room details' do
        listing_data = parser.send(:map_property_to_listing_schema, sample_property_data)
        room_details = listing_data['details_of_rooms']
        
        expect(room_details).to be_a(Hash)
        expect(room_details['living_room']).to eq({ 'name' => 'living_room', 'description' => 'Spacious living room' })
      end
    end
  end

  private

  def sample_onthemarket_html
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <title>Test Property - OnTheMarket</title>
        <link rel="canonical" href="https://www.onthemarket.com/details/12345678/" />
      </head>
      <body>
        <script id="__NEXT_DATA__">
        {
          "props": {
            "pageProps": {
              "property": {
                "id": "12345678",
                "propertyTitle": "Test Property",
                "description": "A lovely test property",
                "bedrooms": 3,
                "bathrooms": 2,
                "priceRaw": 350000,
                "displayAddress": "123 Test Street",
                "addressLocality": "Test Town",
                "images": [
                  {"largeUrl": "https://example.com/img1.jpg"}
                ],
                "features": [
                  {"id": "garden", "feature": "Garden"}
                ],
                "location": {"lat": 51.5, "lon": -0.1},
                "documents": [],
                "moreLikeThis": []
              }
            }
          }
        }
        </script>
        <div class="swiper-slide">
          <picture>
            <img src="https://example.com/image1.jpg" alt="Property image" />
          </picture>
        </div>
      </body>
      </html>
    HTML
  end

  def sample_onthemarket_html_with_datalayer
    sample_onthemarket_html.gsub('</body>', <<~HTML.chomp + '</body>')
      <script id="dataLayerContainer">
        window.dataLayer.push({"postcode": "SW1A 1AA", "property-id": "12345678"})
      </script>
    HTML
  end

  def sample_onthemarket_html_with_malformed_datalayer
    sample_onthemarket_html.gsub('</body>', <<~HTML.chomp + '</body>')
      <script id="dataLayerContainer">
        window.dataLayer.push({invalid json)
      </script>
    HTML
  end
end
