require 'rails_helper'

RSpec.describe RealtyPunts::RealtyPuntCreator, type: :service do
  let(:creator) { described_class.new }
  let(:sample_input_data) do
    {
      'api_prefix' => 'http://localhost:3000/api_mgmt/v4',
      'scoot_subdomain' => 'test-subdomain',
      'vendor_name' => 'buenavista',
      'retrieval_portal' => 'buenavista',
      'property_refs' => ['12345'],
      'realty_game_slug' => 'test-game'
    }
  end

  describe '#serialize_scrape_item' do
    let(:scrape_item) do
      instance_double(
        'ScrapeItemFromBuenavista',
        class: ScrapeItemFromBuenavista,
        scrapable_url: 'https://example.com/property/123',
        scrape_unique_url: 'https://example.com/property/123#h2c',
        full_content_before_js: '{"property": {"Price": 250000}}',
        full_content_after_js: nil,
        title: 'Test Property',
        description: 'A test property',
        page_locale_code: 'en',
        is_valid_scrape: true,
        content_is_html: false,
        content_is_json: true,
        content_is_xml: false,
        all_page_images: [],
        script_json: {}
      )
    end

    before do
      allow(scrape_item).to receive(:respond_to?).with(:scrape_is_buenavista).and_return(true)
      allow(scrape_item).to receive(:scrape_is_buenavista).and_return(true)
      allow(scrape_item).to receive(:respond_to?).with(:scrape_is_onthemarket).and_return(false)
      allow(scrape_item).to receive(:respond_to?).with(:scrape_is_zoopla).and_return(false)
      allow(scrape_item).to receive(:respond_to?).with(:scrape_is_rightmove).and_return(false)
      allow(scrape_item).to receive(:respond_to?).with(:scrape_is_purplebricks).and_return(false)
    end

    it 'serializes scrape item data correctly' do
      result = creator.send(:serialize_scrape_item, scrape_item)

      expect(result).to include(
        scrape_class: 'ScrapeItemFromBuenavista',
        scrapable_url: 'https://example.com/property/123',
        scrape_unique_url: 'https://example.com/property/123#h2c',
        full_content_before_js: '{"property": {"Price": 250000}}',
        title: 'Test Property',
        description: 'A test property',
        is_valid_scrape: true,
        content_is_json: true,
        scrape_is_buenavista: true,
        scrape_is_onthemarket: false
      )
    end
  end

  describe '#create_scrape_item_locally' do
    let(:mock_scrape_item) { instance_double('ScrapeItemFromBuenavista') }

    before do
      allow(ScrapeItemFromBuenavista).to receive(:find_or_create_for_h2c_buenavista)
        .and_return(mock_scrape_item)
      allow(mock_scrape_item).to receive(:retrieve_and_set_content_object)
    end

    it 'creates scrape item using correct portal configuration' do
      url = 'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/12345'
      portal = 'buenavista'

      result = creator.send(:create_scrape_item_locally, url, portal)

      expect(ScrapeItemFromBuenavista).to have_received(:find_or_create_for_h2c_buenavista)
        .with(url)
      expect(mock_scrape_item).to have_received(:retrieve_and_set_content_object)
        .with('ScraperConnectors::Json', include_trailing_slash: false, force_retrieval: false)
      expect(result).to eq(mock_scrape_item)
    end

    it 'raises error for unknown portal' do
      expect {
        creator.send(:create_scrape_item_locally, 'https://example.com', 'unknown_portal')
      }.to raise_error('Unknown portal: unknown_portal')
    end
  end

  describe 'portal configuration' do
    it 'has correct configuration for supported portals' do
      portal_config = creator.send(:create_scrape_item_locally, 'https://example.com', 'buenavista') rescue nil
      
      # Test that the method attempts to use the correct configuration
      expect(ScrapeItemFromBuenavista).to receive(:find_or_create_for_h2c_buenavista)
      allow_any_instance_of(ScrapeItemFromBuenavista).to receive(:retrieve_and_set_content_object)
      
      creator.send(:create_scrape_item_locally, 'https://example.com', 'buenavista')
    end
  end
end
