require 'rails_helper'

RSpec.describe ScraperConnectors::Regular do
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:scrape_item) { create(:scrape_item, scrapable_url: 'https://www.onthemarket.com/details/12345678/') }
  let(:connector) { described_class.new(scrape_item) }
  let(:test_uri) { URI('https://www.onthemarket.com/details/12345678/') }

  before do
    ActsAsTenant.current_tenant = agency_tenant
    
    # Mock the scraper_host method on scrape_item
    allow(scrape_item).to receive(:scraper_host).and_return(
      double(
        'scraper_host',
        user_agent: 'Mozilla/5.0 (Test Browser)',
        accept: 'text/html,application/xhtml+xml',
        accept_encoding: 'gzip, deflate, br',
        cache_control: 'no-cache'
      )
    )
  end

  describe '#initialize' do
    it 'requires a scrape_instance parameter' do
      expect {
        described_class.new(nil)
      }.to raise_error(ArgumentError, 'Please provide scrape_instance to ScraperConnectors::Regular')
    end

    it 'accepts a valid scrape_instance' do
      expect {
        described_class.new(scrape_item)
      }.not_to raise_error
    end

    it 'sets the scrape_instance attribute' do
      connector = described_class.new(scrape_item)
      expect(connector.scrape_instance).to eq(scrape_item)
    end
  end

  describe '#retrieve_data_from_connector' do
    let(:mock_html_response) { '<html><head><title>Test</title></head><body>Test content</body></html>' * 50 }

    before do
      # Mock the noko_doc_string_from method to avoid actual HTTP calls
      allow(connector).to receive(:noko_doc_string_from).and_return(mock_html_response)
    end

    it 'returns content with correct structure' do
      result = connector.retrieve_data_from_connector(test_uri)
      
      expect(result).to be_a(Hash)
      expect(result).to have_key(:returned_content)
      expect(result).to have_key(:scrape_item_target)
      expect(result[:scrape_item_target]).to eq('full_content_before_js')
    end

    it 'passes through include_trailing_slash parameter' do
      expect(connector).to receive(:noko_doc_string_from)
        .with(test_uri, scrape_item.scraper_host, include_trailing_slash: true)
        .and_return(mock_html_response)
      
      connector.retrieve_data_from_connector(test_uri, include_trailing_slash: true)
    end

    it 'handles the is_search_scrape parameter' do
      expect {
        connector.retrieve_data_from_connector(test_uri, is_search_scrape: true)
      }.not_to raise_error
    end
  end

  describe '#noko_doc_string_from', :vcr do
    let(:mock_response_body) { '<html><head><title>Test Property</title></head><body>' + 'Content ' * 500 + '</body></html>' }
    let(:mock_response) { double('response', body: mock_response_body) }
    
    before do
      # Mock Net::HTTP to avoid real HTTP calls in most tests
      allow(Net::HTTP).to receive(:start).and_return(mock_response)
      
      # Mock Nokogiri parsing
      allow(Nokogiri::HTML).to receive(:new).and_call_original
    end

    context 'with successful HTTP response' do
      it 'fetches content successfully' do
        result = connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        
        expect(result).to be_a(String)
        expect(result.length).to be > 1000
      end

      it 'adds trailing slash when include_trailing_slash is true' do
        uri_without_slash = URI('https://www.onthemarket.com/details/12345678')
        
        expect(Net::HTTP).to receive(:start) do |hostname, port, options, &block|
          request = double('request')
          allow(block).to receive(:call).and_return(request)
          
          # The request should be made to URL with trailing slash
          expect(request).to receive(:[]=).with('Authority', anything).at_least(:once)
          expect(request).to receive(:[]=).with('User-Agent', anything).at_least(:once)
          expect(request).to receive(:[]=).with('Accept', anything).at_least(:once)
          expect(request).to receive(:[]=).with('Accept-Language', anything).at_least(:once)
          
          mock_response
        end

        connector.send(:noko_doc_string_from, uri_without_slash, scrape_item.scraper_host, include_trailing_slash: true)
      end

      it 'updates scrape_item with success data' do
        connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        
        scrape_item.reload
        expect(scrape_item.is_valid_scrape).to be true
        expect(scrape_item.title).to be_present
        expect(scrape_item.full_content_before_js).to be_present
        expect(scrape_item.full_content_before_js_length).to be > 0
        expect(scrape_item.request_object).to be_present
      end
    end

    context 'with special site handling' do
      context 'for Zillow' do
        before do
          allow(scrape_item).to receive(:scraper_mapping_name).and_return('zillow')
        end

        it 'adds specific headers for Zillow' do
          expect(Net::HTTP::Get).to receive(:new) do |uri|
            request = double('request')
            expect(request).to receive(:[]=).with('Authority', 'www.zillow.com')
            expect(request).to receive(:[]=).with('User-Agent', anything)
            request
          end

          allow(Net::HTTP).to receive(:start).and_return(mock_response)
          
          connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        end
      end

      context 'for Idealista' do
        before do
          allow(scrape_item).to receive(:scraper_mapping_name).and_return('idealista')
        end

        it 'adds trailing slash for Idealista' do
          uri_without_slash = URI('https://www.idealista.com/inmueble/12345')
          
          # Should add trailing slash and set Authority header
          expect(Net::HTTP::Get).to receive(:new) do |uri|
            expect(uri.to_s).to end_with('/')
            request = double('request')
            expect(request).to receive(:[]=).with('Authority', 'www.idealista.com')
            allow(request).to receive(:[]=)
            request
          end

          allow(Net::HTTP).to receive(:start).and_return(mock_response)
          
          connector.send(:noko_doc_string_from, uri_without_slash, scrape_item.scraper_host)
        end
      end
    end

    context 'with HTTP errors' do
      it 'handles OpenURI::HTTPRedirect' do
        allow(Net::HTTP).to receive(:start).and_raise(
          OpenURI::HTTPRedirect.new('redirect', URI('https://example.com/new-url'))
        )
        
        result = connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        
        expect(result).to eq('')
        scrape_item.reload
        expect(scrape_item.is_valid_scrape).to be false
        expect(scrape_item.scrape_failure_message).to eq('redirect error')
      end

      it 'handles OpenURI::HTTPError' do
        allow(Net::HTTP).to receive(:start).and_raise(
          OpenURI::HTTPError.new('404 Not Found', nil)
        )
        
        result = connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        
        expect(result).to eq('')
        scrape_item.reload
        expect(scrape_item.is_valid_scrape).to be false
        expect(scrape_item.scrape_failure_message).to include('HTTPError')
      end

      it 'handles general exceptions' do
        allow(Net::HTTP).to receive(:start).and_raise(StandardError.new('Network error'))
        
        result = connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        
        expect(result).to eq('')
        scrape_item.reload
        expect(scrape_item.is_valid_scrape).to be false
        expect(scrape_item.scrape_failure_message).to eq('Network error')
      end
    end

    context 'with content validation' do
      it 'raises error when content is too short' do
        short_response = double('response', body: 'short')
        allow(Net::HTTP).to receive(:start).and_return(short_response)
        
        expect {
          connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        }.to raise_error('page_to_parse unavailable or suspiciously short')
        
        scrape_item.reload
        expect(scrape_item.is_valid_scrape).to be false
      end

      it 'raises error when content is empty' do
        empty_response = double('response', body: nil)
        allow(Net::HTTP).to receive(:start).and_return(empty_response)
        
        expect {
          connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        }.to raise_error('page_to_parse unavailable or suspiciously short')
      end
    end

    context 'with image extraction' do
      let(:html_with_images) do
        <<~HTML
          <html>
            <head><title>Test</title></head>
            <body>
              #{'<p>Content paragraph</p>' * 100}
              <img src="https://example.com/image1.jpg" />
              <img src="/relative-image.png" />
              <div style="background-image: url('https://example.com/bg.jpg')"></div>
            </body>
          </html>
        HTML
      end

      before do
        response_with_images = double('response', body: html_with_images)
        allow(Net::HTTP).to receive(:start).and_return(response_with_images)
      end

      it 'extracts and stores all page images' do
        connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
        
        scrape_item.reload
        expect(scrape_item.all_page_images).to be_an(Array)
        expect(scrape_item.all_page_images_length).to be > 0
      end
    end
  end

  describe 'URL handling' do
    it 'removes trailing slash by default' do
      uri_with_slash = URI('https://www.onthemarket.com/details/12345678/')
      allow(connector).to receive(:noko_doc_string_from) do |uri, *args|
        # Should receive URI without trailing slash
        expect(uri.to_s).not_to end_with('/')
        '<html></html>' * 100
      end
      
      connector.retrieve_data_from_connector(uri_with_slash)
    end

    it 'preserves trailing slash when include_trailing_slash is true' do
      uri_with_slash = URI('https://www.onthemarket.com/details/12345678/')
      allow(connector).to receive(:noko_doc_string_from) do |uri, scraper_host, options|
        if options[:include_trailing_slash]
          expect(uri.to_s).to end_with('/')
        end
        '<html></html>' * 100
      end
      
      connector.retrieve_data_from_connector(uri_with_slash, include_trailing_slash: true)
    end
  end

  describe 'integration with shared scraper helpers' do
    it 'includes RealtyScrapers::SharedScraperHelpers' do
      expect(connector).to respond_to(:retrieve_all_images_array)
    end

    it 'uses retrieve_all_images_array for image extraction' do
      html_with_images = '<html><body>' + 'content ' * 200 + '<img src="test.jpg"/></body></html>'
      response = double('response', body: html_with_images)
      allow(Net::HTTP).to receive(:start).and_return(response)
      
      expect(connector).to receive(:retrieve_all_images_array).and_call_original
      
      connector.send(:noko_doc_string_from, test_uri, scrape_item.scraper_host)
    end
  end
end
