require 'rails_helper'

RSpec.describe ParticipantAnalyticsService, type: :service do
  let(:date_range) { 30.days.ago..Time.current }
  let(:service) { described_class.new(date_range: date_range) }

  before do
    # Create test participants with different characteristics
    create(:participant, :high_engagement, first_visit_at: 20.days.ago, last_visit_at: 1.day.ago)
    create(:participant, :explorer, first_visit_at: 15.days.ago, last_visit_at: 2.days.ago)
    create(:participant, :engaged, first_visit_at: 10.days.ago, last_visit_at: 3.days.ago)
    create(:participant, :casual, first_visit_at: 5.days.ago, last_visit_at: 4.days.ago)
    create(:participant, :returning, first_visit_at: 25.days.ago, last_visit_at: 1.day.ago)
  end

  describe '#overview_metrics' do
    it 'returns comprehensive overview metrics' do
      metrics = service.overview_metrics

      expect(metrics).to include(
        :total_participants,
        :active_participants,
        :new_participants,
        :returning_participants,
        :average_visits_per_participant,
        :average_events_per_participant,
        :average_session_duration,
        :high_engagement_count
      )

      expect(metrics[:total_participants]).to be > 0
      expect(metrics[:active_participants]).to be > 0
    end
  end

  describe '#participants_over_time_data' do
    it 'returns time series data for new participants' do
      data = service.participants_over_time_data

      expect(data).to be_an(Array)
      expect(data.first).to be_an(Array) if data.any?
    end
  end

  describe '#visit_distribution_data' do
    it 'returns visit distribution data' do
      data = service.visit_distribution_data

      expect(data).to be_a(Hash)
      expect(data.keys).to include('1 visit', '2-3 visits', '4-10 visits')
    end
  end

  describe '#engagement_score_distribution' do
    it 'returns engagement score distribution' do
      data = service.engagement_score_distribution

      expect(data).to be_a(Hash)
      expect(data.keys).to include('Low (0-25)', 'Medium (26-50)', 'High (51-75)')
    end
  end

  describe '#behavior_categories_data' do
    it 'returns behavior categories data' do
      data = service.behavior_categories_data

      expect(data).to be_a(Hash)
      # Should have at least some of the behavior categories
      possible_categories = %w[explorer engaged regular casual inactive]
      expect(data.keys).to include(*possible_categories.select { |cat| data[cat] })
    end
  end

  describe '#device_type_distribution' do
    it 'returns device type distribution' do
      data = service.device_type_distribution

      expect(data).to be_a(Hash)
      # Should include device types from our test data
      expect(data.keys).to include('Desktop')
    end
  end

  describe '#traffic_sources_data' do
    it 'returns traffic sources data' do
      data = service.traffic_sources_data

      expect(data).to be_a(Hash)
      expect(data.keys).to include('Search Engine', 'Direct')
    end
  end

  describe '#geographic_distribution' do
    it 'returns geographic distribution data' do
      data = service.geographic_distribution

      expect(data).to be_an(Array)
      # Should be array of [country, count] pairs
      expect(data.first).to be_an(Array) if data.any?
    end
  end

  describe '#top_participants' do
    it 'returns top participants by engagement score' do
      participants = service.top_participants(3)

      expect(participants).to be_an(Array)
      expect(participants.size).to be <= 3
      
      if participants.any?
        expect(participants.first).to include(
          :visitor_token,
          :engagement_score,
          :total_visits,
          :total_events,
          :behavior_category
        )
        
        # Should be sorted by engagement score (descending)
        scores = participants.map { |p| p[:engagement_score] }
        expect(scores).to eq(scores.sort.reverse)
      end
    end
  end

  describe '#cohort_analysis' do
    it 'returns cohort analysis data' do
      cohorts = service.cohort_analysis(:week)

      expect(cohorts).to be_an(Array)
      
      if cohorts.any?
        cohort = cohorts.first
        expect(cohort).to include(
          :period,
          :total_participants,
          :returning_participants,
          :retention_rate
        )
      end
    end
  end

  describe 'caching' do
    it 'caches results for performance' do
      # First call should hit the database
      expect(Rails.cache).to receive(:fetch).and_call_original
      service.overview_metrics

      # Second call should use cache
      expect(Rails.cache).to receive(:fetch).and_return({})
      service.overview_metrics
    end
  end

  describe 'private methods' do
    describe '#categorize_traffic_source' do
      let(:participant_direct) { create(:participant, first_referrer: nil) }
      let(:participant_google) { create(:participant, first_referrer: 'https://google.com/search') }
      let(:participant_facebook) { create(:participant, first_referrer: 'https://facebook.com/page') }
      let(:participant_email) { create(:participant, :from_email) }

      it 'categorizes traffic sources correctly' do
        expect(service.send(:categorize_traffic_source, participant_direct)).to eq('Direct')
        expect(service.send(:categorize_traffic_source, participant_google)).to eq('Search Engine')
        expect(service.send(:categorize_traffic_source, participant_facebook)).to eq('Social Media')
        expect(service.send(:categorize_traffic_source, participant_email)).to eq('Email')
      end
    end

    describe '#format_pie_chart_data' do
      it 'removes zero values' do
        data = { 'Category A' => 5, 'Category B' => 0, 'Category C' => 3 }
        result = service.send(:format_pie_chart_data, data)
        
        expect(result).to eq({ 'Category A' => 5, 'Category C' => 3 })
      end
    end
  end
end
