require 'rails_helper'

RSpec.describe Pasarelas::ZooplaPasarela, type: :service do
  let(:agency_tenant) { create(:agency_tenant) }
  
  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe '#call' do
    context 'with valid Zoopla HTML content containing __NEXT_DATA__' do
      let(:scrape_item) do
        create(:scrape_item,
          scrapable_url: 'https://www.zoopla.co.uk/for-sale/details/70751312/',
          scrape_uri_host: 'www.zoopla.co.uk',
          full_content_before_js: zoopla_html_content,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      let(:pasarela) { described_class.new(scrape_item) }

      it 'extracts property data successfully' do
        pasarela.call
        scrape_item.reload
        
        extracted_data = scrape_item.extra_scrape_item_details
        expect(extracted_data).to be_present
        expect(extracted_data['extracted_asset_data']).to be_present
        expect(extracted_data['extracted_listing_data']).to be_present
        expect(extracted_data['extracted_image_urls']).to be_present
      end

      it 'extracts correct asset data' do
        pasarela.call
        scrape_item.reload
        
        asset_data = scrape_item.extra_scrape_item_details['extracted_asset_data']
        
        expect(asset_data['title']).to eq('2 bed apartment for sale')
        expect(asset_data['country']).to eq('Cape Verde')
        expect(asset_data['count_bedrooms']).to eq(2)
        expect(asset_data['count_bathrooms']).to eq(2.0)
        expect(asset_data['prop_type_key']).to eq('flat')
        expect(asset_data['host_on_create']).to eq('zoopla')
        expect(asset_data['reference']).to eq('70751312')
        expect(asset_data['constructed_area']).to eq(941.0)
        expect(asset_data['city']).to eq('Melia Tortuga')
        expect(asset_data['city_search_key']).to eq('melia-tortuga')
      end

      it 'extracts correct listing data' do
        pasarela.call
        scrape_item.reload
        
        listing_data = scrape_item.extra_scrape_item_details['extracted_listing_data']
        
        expect(listing_data['title']).to eq('2 bed apartment for sale')
        expect(listing_data['price_sale_current_cents']).to eq(8241800)
        expect(listing_data['price_sale_current_currency']).to eq('EUR')
        expect(listing_data['reference']).to eq('70751312')
        expect(listing_data['host_on_create']).to eq('zoopla')
        expect(listing_data['currency']).to eq('EUR')
        expect(listing_data['visible']).to be true
        expect(listing_data['archived']).to be false
      end

      it 'extracts correct image URLs' do
        pasarela.call
        scrape_item.reload
        
        image_urls = scrape_item.extra_scrape_item_details['extracted_image_urls']
        
        expect(image_urls).to be_an(Array)
        expect(image_urls.length).to eq(2)
        expect(image_urls.all? { |url| url.include?('zoocdn.com') }).to be true
        expect(image_urls.first).to eq('https://lid.zoocdn.com/1024/768/053ebb6a0e19d420a3c86262ba56d93c5c33c947.jpg')
      end

      it 'extracts property features correctly' do
        pasarela.call
        scrape_item.reload
        
        asset_data = scrape_item.extra_scrape_item_details['extracted_asset_data']
        listing_data = scrape_item.extra_scrape_item_details['extracted_listing_data']
        
        expect(asset_data['categories']).to include(
          { 'id' => 1, 'name' => 'Communal garden' },
          { 'id' => 2, 'name' => 'Balcony' },
          { 'id' => 3, 'name' => 'Public swimming pool' }
        )
        
        expect(listing_data['sale_listing_features']).to include(
          '0' => 'Communal garden',
          '1' => 'Balcony',
          '2' => 'Public swimming pool'
        )
      end

      it 'extracts room details correctly' do
        pasarela.call
        scrape_item.reload
        
        listing_data = scrape_item.extra_scrape_item_details['extracted_listing_data']
        room_details = listing_data['details_of_rooms']
        
        expect(room_details['bedrooms']).to eq(2)
        expect(room_details['bathrooms']).to eq(2)
        expect(room_details['living_rooms']).to eq(0)
      end
    end

    context 'with script_json data' do
      let(:script_json_data) do
        {
          'props' => {
            'pageProps' => {
              'listingDetails' => {
                'listingId' => '12345678',
                'title' => 'Test Property from JSON',
                'detailedDescription' => 'Test description',
                'displayAddress' => 'Test Address, UK',
                'propertyType' => 'House',
                'section' => 'for-sale',
                'adTargeting' => {
                  'currencyCode' => 'GBP',
                  'priceActual' => 500000,
                  'listingStatus' => 'for_sale'
                },
                'counts' => {
                  'numBedrooms' => 3,
                  'numBathrooms' => 2
                },
                'propertyImage' => [
                  { 'filename' => 'test1.jpg' },
                  { 'filename' => 'test2.jpg' }
                ]
              }
            }
          }
        }
      end

      let(:scrape_item) do
        create(:scrape_item,
          scrapable_url: 'https://www.zoopla.co.uk/for-sale/details/12345678/',
          scrape_uri_host: 'www.zoopla.co.uk',
          script_json: script_json_data,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'processes script_json data correctly' do
        pasarela = described_class.new(scrape_item)
        pasarela.call
        scrape_item.reload
        
        extracted_data = scrape_item.extra_scrape_item_details
        expect(extracted_data).to be_present
        
        asset_data = extracted_data['extracted_asset_data']
        expect(asset_data['title']).to eq('Test Property from JSON')
        expect(asset_data['reference']).to eq('12345678')
        expect(asset_data['count_bedrooms']).to eq(3)
        
        listing_data = extracted_data['extracted_listing_data']
        expect(listing_data['price_sale_current_cents']).to eq(50000000) # 500000 * 100
        expect(listing_data['price_sale_current_currency']).to eq('GBP')
      end
    end

    context 'with invalid or missing data' do
      let(:scrape_item) do
        create(:scrape_item,
          scrapable_url: 'https://www.zoopla.co.uk/invalid/',
          scrape_uri_host: 'www.zoopla.co.uk',
          full_content_before_js: '<html><body>Invalid content</body></html>',
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'handles missing __NEXT_DATA__ gracefully' do
        pasarela = described_class.new(scrape_item)
        
        expect { pasarela.call }.not_to raise_error
        
        scrape_item.reload
        # When no valid data is found, extra_scrape_item_details should be nil or empty
        expect(scrape_item.extra_scrape_item_details).to be_nil.or be_empty
      end
    end

    context 'with malformed JSON in __NEXT_DATA__' do
      let(:malformed_html) do
        <<~HTML
          <!DOCTYPE html>
          <html>
          <body>
            <script id="__NEXT_DATA__" type="application/json">
            { invalid json content
            </script>
          </body>
          </html>
        HTML
      end

      let(:scrape_item) do
        create(:scrape_item,
          scrapable_url: 'https://www.zoopla.co.uk/malformed/',
          scrape_uri_host: 'www.zoopla.co.uk',
          full_content_before_js: malformed_html,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'handles malformed JSON gracefully' do
        pasarela = described_class.new(scrape_item)
        
        expect { pasarela.call }.not_to raise_error
        
        scrape_item.reload
        # When malformed JSON is found, extra_scrape_item_details should be nil or empty
        expect(scrape_item.extra_scrape_item_details).to be_nil.or be_empty
      end
    end
  end

  private

  def zoopla_html_content
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <title>2 bed apartment for sale</title>
      </head>
      <body>
        <script id="__NEXT_DATA__" type="application/json">
        {
          "props": {
            "pageProps": {
              "listingDetails": {
                "listingId": "70751312",
                "title": "2 bed apartment for sale",
                "detailedDescription": "An ideal holiday or winter home, this 2 bedroom penthouse in Melia Tortuga has wonderful views of the resort and west coast of Sal.",
                "displayAddress": "455, Melia Tortuga, Cape Verde",
                "propertyType": "Flat",
                "section": "overseas",
                "category": "residential",
                "publishedOn": "2025-07-11T15:55:35",
                "uuid": "7C384208-5E6F-11F0-A27D-D21BFCBDAA15",
                "pricing": {
                  "label": "€82,418",
                  "priceQualifierLabel": null,
                  "isAuction": false
                },
                "counts": {
                  "numBedrooms": 2,
                  "numBathrooms": 2,
                  "numLivingRooms": 0
                },
                "floorArea": {
                  "value": 941,
                  "unitsLabel": "sq. ft",
                  "label": "941 sq. ft"
                },
                "location": {
                  "postalCode": null,
                  "coordinates": {
                    "latitude": null,
                    "longitude": null,
                    "isApproximate": false
                  }
                },
                "branch": {
                  "branchId": "162686",
                  "name": "Casa Verde Homes LDA",
                  "phone": "0330 098 0144",
                  "address": "Unit 5 (Inside Arcade), CV, R. Amilcar Cabral",
                  "postcode": null,
                  "logoUrl": "https://st.zoocdn.com/zoopla_static_agent_logo_(765633).png",
                  "memberType": "agent"
                },
                "features": {
                  "bullets": ["Communal garden", "Balcony", "Public swimming pool"],
                  "flags": {
                    "furnishedState": null,
                    "studentFriendly": false,
                    "tenure": null
                  }
                },
                "propertyImage": [
                  {"filename": "053ebb6a0e19d420a3c86262ba56d93c5c33c947.jpg"},
                  {"filename": "79cba19ac82365008b22c55f88c8882e693afc3b.jpg"}
                ],
                "adTargeting": {
                  "currencyCode": "EUR",
                  "countryCode": "CV",
                  "priceActual": 82418,
                  "priceMin": 82418,
                  "priceMax": 82418,
                  "sizeSqFeet": 941,
                  "listingCondition": "resale",
                  "listingStatus": "for_sale",
                  "chainFree": false,
                  "hasEpc": false,
                  "hasFloorplan": false,
                  "isRetirementHome": false,
                  "isSharedOwnership": false
                }
              }
            }
          }
        }
        </script>
      </body>
      </html>
    HTML
  end
end
