require 'rails_helper'

RSpec.describe Pasarelas::CapeverdepropertyPasarela do
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:sample_html) do
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <title>CA OCEANO 2 BEDROOM APARTMENT</title>
        <link rel="canonical" href="https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms">
      </head>
      <body>
        <h1>CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA, SAL</h1>
        <h2>€125,000</h2>
        <h2>2 Bedroom Apartment For Sale</h2>
        
        <p>Sea Views, 2 bed first floor apartment located by the Budda Beach Hotel in the east of Santa Maria. Fully Furnished. Great location.</p>
        
        <img src="https://maps.googleapis.com/maps/api/staticmap?center=16.5963490599297,-22.8951838191986&zoom=13&size=2000x1000" alt="Map">
        
        <div class="main-features">
          <ul>
            <li>Furnished</li>
            <li>Sea view</li>
            <li>Excellent condition</li>
            <li>Garage</li>
          </ul>
        </div>
        
        <img src="https://wdcdn.co/Media/webp/l/image1.jpg" alt="Property Image">
        <img src="https://wdcdn.co/Media/webp/l/image2.jpg" alt="Property Image">
        
        <h3>OFFICE DETAILS</h3>
        <div>
          <a href="/offices/estate-agents/cape-verde">Cape Verde Office Address</a>
          <a href="tel:002382422041">00238 242 2041</a>
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>
      </body>
      </html>
    HTML
  end

  let(:scrape_item) do
    ActsAsTenant.current_tenant = agency_tenant
    item = ScrapeItem.create!(
      scrapable_url: 'https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms',
      scrape_unique_url: 'https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms#hpg',
      full_content_before_js: sample_html,
      agency_tenant_uuid: agency_tenant.uuid,
      scrape_is_capeverdeproperty: true
    )
    item
  end

  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe '.extract_listing_and_asset_from_scrape_item' do
    it 'extracts complete listing and asset data' do
      result = described_class.extract_listing_and_asset_from_scrape_item(scrape_item)
      
      expect(result).to be_a(Hash)
      expect(result).to have_key(:listing_data)
      expect(result).to have_key(:asset_data)
      expect(result).to have_key(:images)
      expect(result).to have_key(:raw_data)
    end

    it 'raises error when content is insufficient' do
      scrape_item.update!(full_content_before_js: 'short content')
      
      expect {
        described_class.extract_listing_and_asset_from_scrape_item(scrape_item)
      }.to raise_error(/Insufficient content/)
    end
  end

  describe '#extract_listing_and_asset' do
    let(:pasarela) { described_class.new(scrape_item) }

    it 'returns structured data with all required components' do
      result = pasarela.extract_listing_and_asset
      
      expect(result[:listing_data]).to be_a(Hash)
      expect(result[:asset_data]).to be_a(Hash)
      expect(result[:images]).to be_an(Array)
      expect(result[:raw_data]).to be_a(Hash)
    end

    it 'includes property images' do
      result = pasarela.extract_listing_and_asset
      
      expect(result[:images]).not_to be_empty
      expect(result[:images].first).to include('wdcdn.co')
    end
  end

  describe 'schema mapping' do
    let(:pasarela) { described_class.new(scrape_item) }
    let(:sample_property_data) do
      {
        'title' => 'Test Property Santa Maria, Sal',
        'description' => 'Beautiful sea view apartment',
        'price_raw' => 125000,
        'currency' => 'EUR',
        'bedrooms' => 2,
        'bathrooms' => 1,
        'property_type' => 'apartment',
        'features' => ['Furnished', 'Sea view', 'Garage'],
        'furnished' => true,
        'sea_view' => true,
        'city' => 'Santa Maria',
        'region' => 'Sal',
        'country' => 'Cape Verde',
        'latitude' => 16.59,
        'longitude' => -22.89,
        'reference' => 'CVP123',
        'image_count' => 3,
        'agent_info' => {
          'company_name' => 'Cape Verde Property',
          'email' => '<EMAIL>',
          'phone' => '00238 242 2041'
        }
      }
    end

    describe '#map_to_listing_schema' do
      it 'maps property data to listing schema correctly' do
        listing_data = pasarela.send(:map_to_listing_schema, sample_property_data)
        
        expect(listing_data['title']).to eq('Test Property Santa Maria, Sal')
        expect(listing_data['description']).to eq('Beautiful sea view apartment')
        expect(listing_data['price_sale_current_cents']).to eq(12_500_000) # €125,000 in cents
        expect(listing_data['price_sale_current_currency']).to eq('EUR')
        expect(listing_data['currency']).to eq('EUR')
        expect(listing_data['furnished']).to be true
        expect(listing_data['reference']).to eq('CVP123')
        expect(listing_data['sl_photos_count']).to eq(3)
      end

      it 'includes extra sale details' do
        listing_data = pasarela.send(:map_to_listing_schema, sample_property_data)
        extra_details = listing_data['extra_sale_details']
        
        expect(extra_details['agent_info']).to be_present
        expect(extra_details['features']).to include('Furnished', 'Sea view')
        expect(extra_details['furnished']).to be true
        expect(extra_details['sea_view']).to be true
        expect(extra_details['source_website']).to eq('capeverdeproperty.co.uk')
      end

      it 'formats features as hash' do
        listing_data = pasarela.send(:map_to_listing_schema, sample_property_data)
        features = listing_data['sale_listing_features']
        
        expect(features).to be_a(Hash)
        expect(features['furnished']).to eq('Furnished')
        expect(features['sea_view']).to eq('Sea view')
        expect(features['garage']).to eq('Garage')
      end

      it 'sets default values for missing data' do
        minimal_data = { 'title' => 'Basic Property' }
        listing_data = pasarela.send(:map_to_listing_schema, minimal_data)
        
        expect(listing_data['price_sale_current_cents']).to eq(0)
        expect(listing_data['currency']).to eq('EUR')
        expect(listing_data['furnished']).to be false
        expect(listing_data['visible']).to be true
        expect(listing_data['archived']).to be false
      end
    end

    describe '#map_to_asset_schema' do
      it 'maps property data to asset schema correctly' do
        asset_data = pasarela.send(:map_to_asset_schema, sample_property_data)
        
        expect(asset_data['title']).to eq('Test Property Santa Maria, Sal')
        expect(asset_data['count_bedrooms']).to eq(2)
        expect(asset_data['count_bathrooms']).to eq(1.0)
        expect(asset_data['city']).to eq('Santa Maria')
        expect(asset_data['region']).to eq('Sal')
        expect(asset_data['country']).to eq('Cape Verde')
        expect(asset_data['latitude']).to eq(16.59)
        expect(asset_data['longitude']).to eq(-22.89)
        expect(asset_data['reference']).to eq('CVP123')
      end

      it 'formats categories correctly' do
        asset_data = pasarela.send(:map_to_asset_schema, sample_property_data)
        categories = asset_data['categories']
        
        expect(categories).to be_an(Array)
        expect(categories.first).to have_key('name')
        expect(categories.map { |c| c['name'] }).to include('Furnished', 'Sea view', 'Garage')
      end

      it 'includes property details' do
        asset_data = pasarela.send(:map_to_asset_schema, sample_property_data)
        details = asset_data['details']
        
        expect(details['features']).to include('Furnished', 'Sea view')
        expect(details['furnished']).to be true
        expect(details['sea_view']).to be true
      end

      it 'sets property flags and counts' do
        asset_data = pasarela.send(:map_to_asset_schema, sample_property_data)
        
        expect(asset_data['has_sale_listings']).to be true
        expect(asset_data['has_rental_listings']).to be false
        expect(asset_data['sale_listings_count']).to eq(1)
        expect(asset_data['rental_listings_count']).to eq(0)
        expect(asset_data['ra_photos_count']).to eq(3)
      end

      it 'detects garage from features' do
        asset_data = pasarela.send(:map_to_asset_schema, sample_property_data)
        expect(asset_data['count_garages']).to eq(1)
      end

      it 'handles missing location data' do
        data_without_location = sample_property_data.except('city', 'region', 'latitude', 'longitude')
        asset_data = pasarela.send(:map_to_asset_schema, data_without_location)
        
        expect(asset_data['city']).to eq('Unknown')
        expect(asset_data['region']).to eq('Sal') # Default
        expect(asset_data['latitude']).to be_nil
        expect(asset_data['longitude']).to be_nil
      end
    end
  end

  describe 'utility methods' do
    let(:pasarela) { described_class.new(scrape_item) }

    describe '#format_features_hash' do
      it 'converts feature array to parameterized hash' do
        features = ['Sea View', 'Fully Furnished', 'Air Conditioning']
        result = pasarela.send(:format_features_hash, features)
        
        expect(result).to be_a(Hash)
        expect(result['sea_view']).to eq('Sea View')
        expect(result['fully_furnished']).to eq('Fully Furnished')
        expect(result['air_conditioning']).to eq('Air Conditioning')
      end

      it 'handles non-array input' do
        result = pasarela.send(:format_features_hash, nil)
        expect(result).to eq({})
        
        result = pasarela.send(:format_features_hash, 'not an array')
        expect(result).to eq({})
      end
    end

    describe '#format_categories' do
      it 'converts feature array to category objects' do
        features = ['Sea View', 'Furnished']
        result = pasarela.send(:format_categories, features)
        
        expect(result).to be_an(Array)
        expect(result.length).to eq(2)
        expect(result.first).to eq({ 'name' => 'Sea View' })
        expect(result.last).to eq({ 'name' => 'Furnished' })
      end

      it 'handles non-array input' do
        result = pasarela.send(:format_categories, nil)
        expect(result).to eq([])
      end
    end

    describe '#extract_garage_count' do
      it 'detects garage in features' do
        property_data = { 'features' => ['Garage', 'Sea view'] }
        count = pasarela.send(:extract_garage_count, property_data)
        expect(count).to eq(1)
      end

      it 'detects garage in description' do
        property_data = { 'description' => 'Property with private garage and garden' }
        count = pasarela.send(:extract_garage_count, property_data)
        expect(count).to eq(1)
      end

      it 'returns 0 when no garage mentioned' do
        property_data = { 'features' => ['Sea view'], 'description' => 'Nice property' }
        count = pasarela.send(:extract_garage_count, property_data)
        expect(count).to eq(0)
      end
    end

    describe '#generate_slug' do
      it 'generates slug from title' do
        slug = pasarela.send(:generate_slug, 'CA OCEANO 2 Bedroom Apartment')
        expect(slug).to eq('ca-oceano-2-bedroom-apartment')
      end

      it 'limits slug length' do
        long_title = 'Very Long Property Title That Should Be Truncated To Reasonable Length'
        slug = pasarela.send(:generate_slug, long_title)
        expect(slug.length).to be <= 30
      end

      it 'handles empty title' do
        slug = pasarela.send(:generate_slug, '')
        expect(slug).to start_with('cvp_')
      end
    end

    describe '#generate_reference' do
      it 'generates reference with date and random component' do
        reference = pasarela.send(:generate_reference)
        
        expect(reference).to start_with('CVP')
        expect(reference).to include(Time.current.strftime('%Y%m%d'))
        expect(reference.length).to eq(14) # CVP + 8 digits + 3 hex chars
      end
    end
  end

  context 'integration with real HTML' do
    it 'processes complete HTML without errors' do
      result = described_class.extract_listing_and_asset_from_scrape_item(scrape_item)
      
      listing_data = result[:listing_data]
      asset_data = result[:asset_data]
      
      # Verify essential fields are populated
      expect(listing_data['title']).to include('CA OCEANO')
      expect(listing_data['price_sale_current_cents']).to eq(12_500_000)
      expect(asset_data['count_bedrooms']).to eq(2)
      expect(asset_data['city']).to eq('SANTA MARIA')
      expect(result[:images]).not_to be_empty
    end
  end
end
