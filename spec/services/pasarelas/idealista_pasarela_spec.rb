require 'rails_helper'

RSpec.describe Pasarelas::IdealistaPasarela, type: :service do
  let(:agency_tenant) { create(:agency_tenant) }

  describe '#call' do
    context 'with valid Idealista HTML content' do
      let(:idealista_html_content) do
        <<~HTML
          <!DOCTYPE html>
          <html>
          <head>
            <title>Piso en venta en Madrid - Idealista</title>
          </head>
          <body>
            <h1>Piso en venta en Calle Mayor, Madrid</h1>
            <div class="price">350.000 €</div>
            <div class="description">
              Precioso piso en el centro de Madrid con 3 habitaciones y 2 baños.
              Totalmente reformado con cocina equipada y aire acondicionado.
            </div>
            <div class="features">
              <span>3 habitaciones</span>
              <span>2 baños</span>
              <span>120 m²</span>
              <span>Ascensor</span>
              <span>Aire acondicionado</span>
            </div>
            <div class="location">Calle Mayor, Madrid, 28013</div>
            <script type="application/json" id="property-data">
            {
              "property": {
                "id": "108332223",
                "title": "Piso en venta en Calle Mayor, Madrid",
                "price": {
                  "amount": 350000,
                  "currency": "EUR"
                },
                "description": "Precioso piso en el centro de Madrid con 3 habitaciones y 2 baños.",
                "bedrooms": 3,
                "bathrooms": 2,
                "area": 120,
                "location": {
                  "address": "Calle Mayor, Madrid",
                  "city": "Madrid",
                  "postal_code": "28013",
                  "latitude": 40.4168,
                  "longitude": -3.7038
                },
                "features": ["Ascensor", "Aire acondicionado", "Reformado"],
                "images": [
                  "https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image1.jpg",
                  "https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image2.jpg"
                ],
                "property_type": "Apartment",
                "year_built": 1980,
                "energy": {
                  "rating": "E",
                  "performance": "150 kWh/m²"
                }
              }
            }
            </script>
            <img src="https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image1.jpg" alt="Property image 1">
            <img src="https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image2.jpg" alt="Property image 2">
          </body>
          </html>
        HTML
      end

      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.idealista.com/inmueble/108332223/',
          scrape_uri_host: 'www.idealista.com',
          full_content_before_js: idealista_html_content,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      let(:pasarela) { described_class.new(scrape_item) }

      it 'extracts property data successfully' do
        pasarela.call
        scrape_item.reload
        
        expect(scrape_item.extracted_asset_data).to be_present
        expect(scrape_item.extracted_listing_data).to be_present
        expect(scrape_item.extracted_image_urls).to be_present
      end

      it 'extracts correct asset data' do
        pasarela.call
        scrape_item.reload
        
        asset_data = scrape_item.extracted_asset_data
        expect(asset_data['title']).to eq('Piso en venta en Calle Mayor, Madrid')
        expect(asset_data['city']).to eq('Madrid')
        expect(asset_data['count_bedrooms']).to eq(3)
        expect(asset_data['count_bathrooms']).to eq(2.0)
        expect(asset_data['constructed_area']).to eq(120.0)
        expect(asset_data['country']).to eq('ES')
        expect(asset_data['latitude']).to eq(40.4168)
        expect(asset_data['longitude']).to eq(-3.7038)
        expect(asset_data['postal_code']).to eq('28013')
        expect(asset_data['prop_type']).to eq('Apartment')
        expect(asset_data['year_construction']).to eq(1980)
        expect(asset_data['energy_rating']).to eq('E')
        expect(asset_data['energy_performance']).to eq('150 kWh/m²')
      end

      it 'extracts correct listing data' do
        pasarela.call
        scrape_item.reload
        
        listing_data = scrape_item.extracted_listing_data
        expect(listing_data['title']).to eq('Piso en venta en Calle Mayor, Madrid')
        expect(listing_data['price_sale_current_cents']).to eq(35000000) # 350,000 EUR in cents
        expect(listing_data['price_sale_current_currency']).to eq('EUR')
        expect(listing_data['count_bedrooms']).to eq(3)
        expect(listing_data['count_bathrooms']).to eq(2.0)
        expect(listing_data['constructed_area']).to eq(120.0)
        expect(listing_data['property_reference']).to eq('108332223')
        expect(listing_data['year_construction']).to eq(1980)
        expect(listing_data['energy_rating']).to eq('E')
        expect(listing_data['energy_performance']).to eq('150 kWh/m²')
      end

      it 'extracts image URLs correctly' do
        pasarela.call
        scrape_item.reload
        
        image_urls = scrape_item.extracted_image_urls
        expect(image_urls).to be_an(Array)
        expect(image_urls.length).to eq(2)
        expect(image_urls).to include('https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image1.jpg')
        expect(image_urls).to include('https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image2.jpg')
      end
    end

    context 'with HTML content without JSON data' do
      let(:html_only_content) do
        <<~HTML
          <!DOCTYPE html>
          <html>
          <body>
            <h1>Piso en venta en Barcelona</h1>
            <div class="price">280.000 €</div>
            <div class="description">Apartamento en Barcelona con 2 habitaciones</div>
            <div>2 habitaciones</div>
            <div>1 baño</div>
            <div>85 m²</div>
            <div class="location">Barcelona, 08001</div>
          </body>
          </html>
        HTML
      end

      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.idealista.com/inmueble/123456/',
          scrape_uri_host: 'www.idealista.com',
          full_content_before_js: html_only_content,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'falls back to HTML extraction' do
        pasarela = described_class.new(scrape_item)
        pasarela.call
        scrape_item.reload
        
        asset_data = scrape_item.extracted_asset_data
        expect(asset_data).to be_present
        expect(asset_data['title']).to eq('Piso en venta en Barcelona')
      end
    end

    context 'with insufficient content' do
      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.idealista.com/inmueble/123456/',
          scrape_uri_host: 'www.idealista.com',
          full_content_before_js: 'Short content',
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'does not process insufficient content' do
        pasarela = described_class.new(scrape_item)
        
        expect { pasarela.call }.not_to raise_error
        
        scrape_item.reload
        expect(scrape_item.extracted_asset_data).to be_blank
        expect(scrape_item.extracted_listing_data).to be_blank
        expect(scrape_item.extracted_image_urls).to be_blank
      end
    end

    context 'with malformed JSON' do
      let(:malformed_html) do
        <<~HTML
          <!DOCTYPE html>
          <html>
          <body>
            <h1>Test Property</h1>
            <script type="application/json">
            { invalid json content
            </script>
          </body>
          </html>
        HTML
      end

      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.idealista.com/inmueble/malformed/',
          scrape_uri_host: 'www.idealista.com',
          full_content_before_js: malformed_html,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'handles malformed JSON gracefully' do
        pasarela = described_class.new(scrape_item)
        
        expect { pasarela.call }.not_to raise_error
        
        scrape_item.reload
        # Should fall back to HTML extraction
        asset_data = scrape_item.extracted_asset_data
        expect(asset_data).to be_present
        expect(asset_data['title']).to eq('Test Property')
      end
    end

    context 'with no content' do
      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.idealista.com/inmueble/123456/',
          scrape_uri_host: 'www.idealista.com',
          full_content_before_js: nil,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'handles missing content gracefully' do
        pasarela = described_class.new(scrape_item)
        
        expect { pasarela.call }.not_to raise_error
        
        scrape_item.reload
        expect(scrape_item.extracted_asset_data).to be_blank
        expect(scrape_item.extracted_listing_data).to be_blank
        expect(scrape_item.extracted_image_urls).to be_blank
      end
    end
  end

  private

  def create_scrape_item_with_content(content)
    create(:realty_scraped_item,
      scrapable_url: 'https://www.idealista.com/inmueble/108332223/',
      scrape_uri_host: 'www.idealista.com',
      full_content_before_js: content,
      agency_tenant_uuid: agency_tenant.uuid
    )
  end
end
