require 'rails_helper'

RSpec.describe Pasarelas::ZillowPasarela, type: :service do
  let(:agency_tenant) { create(:agency_tenant) }

  describe '#call' do
    context 'with valid Zillow HTML content' do
      let(:zillow_html_content) do
        <<~HTML
          <!DOCTYPE html>
          <html lang="en">
          <head>
            <title>90 Prospect Drive, Chappaqua, NY 10514 | MLS #873236 | Zillow</title>
          </head>
          <body>
            <h1>90 Prospect Drive, Chappaqua, NY 10514</h1>
            <div data-testid="price">$1,599,000</div>
            <div data-testid="description">
              Welcome to 90 Prospect Drive, a timeless Dutch Colonial nestled on over an acre of picturesque, 
              park-like land in the heart of Chappaqua's walkable village center. This sun-filled 4-bedroom, 
              2.5-bath home blends storybook charm with modern luxury.
            </div>
            <div class="features">
              <span>4 bedrooms</span>
              <span>3 bathrooms</span>
              <span>3,087 sqft</span>
              <span>1.17 Acres Lot</span>
              <span>Built in 1936</span>
              <span>Single Family Residence</span>
            </div>
            <script id="__NEXT_DATA__" type="application/json">
            {
              "props": {
                "pageProps": {
                  "property": {
                    "zpid": "33066770",
                    "streetAddress": "90 Prospect Drive, Chappaqua, NY 10514",
                    "price": {
                      "value": 1599000,
                      "currency": "USD"
                    },
                    "description": "Welcome to 90 Prospect Drive, a timeless Dutch Colonial nestled on over an acre of picturesque, park-like land in the heart of Chappaqua's walkable village center.",
                    "bedrooms": 4,
                    "bathrooms": 3,
                    "livingArea": 3087,
                    "lotSize": 1.17,
                    "yearBuilt": 1936,
                    "homeType": "SingleFamily",
                    "address": {
                      "streetAddress": "90 Prospect Drive",
                      "city": "Chappaqua",
                      "state": "NY",
                      "zipcode": "10514",
                      "latitude": 41.1595,
                      "longitude": -73.7648
                    },
                    "photos": [
                      "https://photos.zillowstatic.com/fp/b3275c56180cc84001bf5c317e3814be-cc_ft_960.jpg",
                      "https://photos.zillowstatic.com/fp/0236b5da0d2dc0d6fee259a81f6931fd-cc_ft_576.jpg"
                    ],
                    "features": ["Hardwood floors", "Fireplace", "Central air", "Garage"],
                    "parkingSpaces": 2
                  }
                }
              }
            }
            </script>
            <img src="https://photos.zillowstatic.com/fp/b3275c56180cc84001bf5c317e3814be-cc_ft_960.jpg" alt="Property image 1">
            <img src="https://photos.zillowstatic.com/fp/0236b5da0d2dc0d6fee259a81f6931fd-cc_ft_576.jpg" alt="Property image 2">
          </body>
          </html>
        HTML
      end

      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.zillow.com/homedetails/90-Prospect-Dr-Chappaqua-NY-10514/33066770_zpid/',
          scrape_uri_host: 'www.zillow.com',
          full_content_before_js: zillow_html_content,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      let(:pasarela) { described_class.new(scrape_item) }

      it 'extracts property data successfully' do
        pasarela.call
        scrape_item.reload
        
        expect(scrape_item.extracted_asset_data).to be_present
        expect(scrape_item.extracted_listing_data).to be_present
        expect(scrape_item.extracted_image_urls).to be_present
      end

      it 'extracts correct asset data' do
        pasarela.call
        scrape_item.reload
        
        asset_data = scrape_item.extracted_asset_data
        expect(asset_data['title']).to eq('90 Prospect Drive, Chappaqua, NY 10514')
        expect(asset_data['city']).to eq('Chappaqua')
        expect(asset_data['count_bedrooms']).to eq(4)
        expect(asset_data['count_bathrooms']).to eq(3.0)
        expect(asset_data['constructed_area']).to eq(3087.0)
        expect(asset_data['country']).to eq('US')
        expect(asset_data['latitude']).to eq(41.1595)
        expect(asset_data['longitude']).to eq(-73.7648)
        expect(asset_data['postal_code']).to eq('10514')
        expect(asset_data['prop_type']).to eq('SingleFamily')
        expect(asset_data['year_construction']).to eq(1936)
        expect(asset_data['province']).to eq('NY')
      end

      it 'extracts correct listing data' do
        pasarela.call
        scrape_item.reload
        
        listing_data = scrape_item.extracted_listing_data
        expect(listing_data['title']).to eq('90 Prospect Drive, Chappaqua, NY 10514')
        expect(listing_data['price_sale_current_cents']).to eq(159900000) # $1,599,000 in cents
        expect(listing_data['price_sale_current_currency']).to eq('USD')
        expect(listing_data['count_bedrooms']).to eq(4)
        expect(listing_data['count_bathrooms']).to eq(3.0)
        expect(listing_data['constructed_area']).to eq(3087.0)
        expect(listing_data['property_reference']).to eq('33066770')
        expect(listing_data['year_construction']).to eq(1936)
        expect(listing_data['area_unit']).to eq('sqft')
      end

      it 'extracts image URLs correctly' do
        pasarela.call
        scrape_item.reload
        
        image_urls = scrape_item.extracted_image_urls
        expect(image_urls).to be_an(Array)
        expect(image_urls.length).to eq(2)
        expect(image_urls).to include('https://photos.zillowstatic.com/fp/b3275c56180cc84001bf5c317e3814be-cc_ft_960.jpg')
        expect(image_urls).to include('https://photos.zillowstatic.com/fp/0236b5da0d2dc0d6fee259a81f6931fd-cc_ft_576.jpg')
      end
    end

    context 'with HTML content without JSON data' do
      let(:html_only_content) do
        <<~HTML
          <!DOCTYPE html>
          <html>
          <body>
            <h1>123 Main Street, New York, NY 10001</h1>
            <div data-testid="price">$850,000</div>
            <div data-testid="description">Beautiful apartment in Manhattan with 2 bedrooms</div>
            <div>2 bedrooms</div>
            <div>1 bathroom</div>
            <div>1,200 sqft</div>
            <div class="address">123 Main Street, New York, NY 10001</div>
          </body>
          </html>
        HTML
      end

      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.zillow.com/homedetails/123-Main-St-New-York-NY-10001/12345678_zpid/',
          scrape_uri_host: 'www.zillow.com',
          full_content_before_js: html_only_content,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'falls back to HTML extraction' do
        pasarela = described_class.new(scrape_item)
        pasarela.call
        scrape_item.reload
        
        asset_data = scrape_item.extracted_asset_data
        expect(asset_data).to be_present
        expect(asset_data['title']).to eq('123 Main Street, New York, NY 10001')
      end
    end

    context 'with insufficient content' do
      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.zillow.com/homedetails/property/12345678_zpid/',
          scrape_uri_host: 'www.zillow.com',
          full_content_before_js: 'Short content',
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'does not process insufficient content' do
        pasarela = described_class.new(scrape_item)
        
        expect { pasarela.call }.not_to raise_error
        
        scrape_item.reload
        expect(scrape_item.extracted_asset_data).to be_blank
        expect(scrape_item.extracted_listing_data).to be_blank
        expect(scrape_item.extracted_image_urls).to be_blank
      end
    end

    context 'with malformed JSON' do
      let(:malformed_html) do
        <<~HTML
          <!DOCTYPE html>
          <html>
          <body>
            <h1>Test Property</h1>
            <script id="__NEXT_DATA__" type="application/json">
            { invalid json content
            </script>
          </body>
          </html>
        HTML
      end

      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.zillow.com/homedetails/malformed/12345678_zpid/',
          scrape_uri_host: 'www.zillow.com',
          full_content_before_js: malformed_html,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'handles malformed JSON gracefully' do
        pasarela = described_class.new(scrape_item)
        
        expect { pasarela.call }.not_to raise_error
        
        scrape_item.reload
        # Should fall back to HTML extraction
        asset_data = scrape_item.extracted_asset_data
        expect(asset_data).to be_present
        expect(asset_data['title']).to eq('Test Property')
      end
    end

    context 'with no content' do
      let(:scrape_item) do
        create(:realty_scraped_item,
          scrapable_url: 'https://www.zillow.com/homedetails/property/12345678_zpid/',
          scrape_uri_host: 'www.zillow.com',
          full_content_before_js: nil,
          agency_tenant_uuid: agency_tenant.uuid
        )
      end

      it 'handles missing content gracefully' do
        pasarela = described_class.new(scrape_item)
        
        expect { pasarela.call }.not_to raise_error
        
        scrape_item.reload
        expect(scrape_item.extracted_asset_data).to be_blank
        expect(scrape_item.extracted_listing_data).to be_blank
        expect(scrape_item.extracted_image_urls).to be_blank
      end
    end
  end

  private

  def create_scrape_item_with_content(content)
    create(:realty_scraped_item,
      scrapable_url: 'https://www.zillow.com/homedetails/90-Prospect-Dr-Chappaqua-NY-10514/33066770_zpid/',
      scrape_uri_host: 'www.zillow.com',
      full_content_before_js: content,
      agency_tenant_uuid: agency_tenant.uuid
    )
  end
end
