# OnTheMarket Scraping Tests

This directory contains comprehensive tests for the OnTheMarket scraping functionality.

## Test Structure

### Unit Tests
- **`spec/models/scrape_item_from_otm_spec.rb`** - Tests the OnTheMarket property page scraping model
- **`spec/models/scrape_item_from_otm_search_spec.rb`** - Tests the OnTheMarket search results scraping model
- **`spec/services/realty_parsers/parse_onthemarket_listings_json_spec.rb`** - Tests the JSON parsing service
- **`spec/services/scraper_connectors/regular_spec.rb`** - Tests the HTTP scraper connector

### Integration Tests
- **`spec/integration/onthemarket_integration_spec.rb`** - End-to-end workflow tests

### Test Fixtures
- **`spec/fixtures/files/onthemarket_sample.html`** - Sample OnTheMarket HTML for testing

### Factory Definitions
- **`spec/factories/scrape_items.rb`** - Factory definitions with OnTheMarket-specific traits

## Running the Tests

### Prerequisites

Ensure you have Rails environment variables set up:

```bash
# Required before running terminal commands (as mentioned in your notes)
unset RAILS_MASTER_KEY
```

### Run Individual Test Files

```bash
# Model tests
bundle exec rspec spec/models/scrape_item_from_otm_spec.rb
bundle exec rspec spec/models/scrape_item_from_otm_search_spec.rb

# Service tests
bundle exec rspec spec/services/realty_parsers/parse_onthemarket_listings_json_spec.rb
bundle exec rspec spec/services/scraper_connectors/regular_spec.rb

# Integration tests
bundle exec rspec spec/integration/onthemarket_integration_spec.rb
```

### Run All OnTheMarket Tests

```bash
# Run all OnTheMarket-related tests
bundle exec rspec spec/models/scrape_item_from_otm*_spec.rb spec/services/realty_parsers/parse_onthemarket_listings_json_spec.rb spec/integration/onthemarket_integration_spec.rb
```

### Run Comprehensive Test Rake Task

```bash
# Run the comprehensive OnTheMarket testing rake task
bundle exec rake h2c:test:test_onthemarket_scraping
```

## Test Coverage

### What is Tested

#### Model Layer (`ScrapeItemFromOtm`)
- ✅ Creation and persistence
- ✅ URL processing and validation
- ✅ Content extraction from HTML/JSON
- ✅ Data mapping to asset and listing schemas
- ✅ Error handling for malformed content
- ✅ Integration with scraper connectors

#### Search Model (`ScrapeItemFromOtmSearch`)
- ✅ Search result processing
- ✅ Summary listing extraction
- ✅ JSON parsing and data transformation
- ✅ Multiple property handling
- ✅ Default scope filtering

#### Parser Service (`ParseOnthemarketListingsJson`)
- ✅ HTML parsing and JSON extraction
- ✅ Data mapping methods
- ✅ Multiple data structure support (legacy/current)
- ✅ Image extraction from various sources
- ✅ Error handling for malformed JSON

#### Scraper Connector (`ScraperConnectors::Regular`)
- ✅ HTTP request handling
- ✅ Site-specific configurations (headers, user agents)
- ✅ Content validation and length checks
- ✅ Error handling (timeouts, HTTP errors, redirects)
- ✅ Image extraction and metadata processing

#### Integration Workflows
- ✅ End-to-end scraping from URL to structured data
- ✅ Data validation and schema compliance
- ✅ Error recovery and resilience
- ✅ Performance under multiple simultaneous requests

## Test Data and Mocking

### Mock Data Strategy
- Uses realistic OnTheMarket HTML structure in fixtures
- Includes both legacy and current JSON data formats
- Covers edge cases (missing fields, malformed data)
- Simulates various error conditions

### VCR Integration
- HTTP interactions can be recorded with VCR cassettes
- Mock data used by default to avoid external dependencies
- Real HTTP calls available for integration testing (when needed)

### Factory Traits
- `:onthemarket` - Basic OnTheMarket scrape item
- `:onthemarket_search` - Search scrape item
- `:with_valid_content` - Item with realistic content
- `:with_onthemarket_content` - Item with OnTheMarket-specific JSON structure

## Expected Test Results

When all tests pass, you should see:

### Unit Test Results
- All model methods work correctly
- Data mapping produces valid schemas
- Error conditions are handled gracefully
- HTTP interactions are properly mocked

### Integration Test Results
- Complete scraping workflow from URL to data
- Proper data validation and structure
- Error recovery mechanisms functional
- Performance characteristics acceptable

### Rake Task Results
```
🧪 Testing OnTheMarket Scraping Components
============================================================

✅ ScrapeItemFromOtm created successfully
✅ ScrapeItemFromOtmSearch created successfully  
✅ ScraperConnectors::Regular initialized successfully
✅ Mock content set successfully
✅ Property hash extracted successfully
✅ Data mapping schemas validated
✅ Search results processed successfully
✅ Error handling works correctly

============================================================
🏁 TEST RESULTS SUMMARY
============================================================
Tests passed: 8/8
Success rate: 100.0%
🎉 ALL TESTS PASSED! OnTheMarket scraping is working correctly.
```

## Debugging Test Failures

### Common Issues and Solutions

#### 1. Missing AgencyTenant
```ruby
# Ensure tenant is set in test setup
ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
```

#### 2. JSON Parsing Errors
- Check the mock HTML structure in fixtures
- Validate JSON syntax in `__NEXT_DATA__` scripts
- Ensure both legacy and current structures are supported

#### 3. Factory Issues
- Verify factory traits are properly defined
- Check that all required associations are created
- Ensure factory-generated URLs are valid

#### 4. HTTP Mocking Issues
- Verify Net::HTTP mocking in scraper connector tests
- Ensure mock responses have realistic content
- Check that content length validations pass

#### 5. Database Issues
- Run migrations: `bundle exec rake db:migrate RAILS_ENV=test`
- Reset test database: `bundle exec rake db:test:prepare`

## Maintenance

### Updating Tests for OnTheMarket Changes

If OnTheMarket changes their HTML structure:

1. Update the fixture file (`onthemarket_sample.html`)
2. Modify the JSON structure in factory traits
3. Update integration test expectations
4. Test with real URLs using the rake task

### Adding New Test Cases

1. Create new test cases in appropriate spec files
2. Add new factory traits if needed
3. Update the comprehensive rake task
4. Document any new test scenarios

### Performance Testing

Use the integration tests to monitor:
- Response times for data extraction
- Memory usage during processing
- Database query performance
- Concurrent scraping capability

## Continuous Integration

For CI environments, ensure:
- Tests run without external HTTP dependencies
- Mock data provides consistent results
- Error scenarios are properly tested
- Performance benchmarks are maintained
