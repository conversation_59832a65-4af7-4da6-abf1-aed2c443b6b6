# == Schema Information
#
# Table name: realty_game_listings
#
#  id                          :bigint           not null, primary key
#  average_guess_cents         :bigint
#  discarded_at                :datetime
#  game_sessions_count         :integer          default(0)
#  guessed_prices_count        :integer          default(0)
#  highest_guess_cents         :bigint
#  is_rental_listing           :boolean          default(FALSE)
#  is_sale_listing             :boolean          default(TRUE)
#  listing_uuid                :uuid
#  lowest_guess_cents          :bigint
#  ordered_photo_uuids         :text             default([]), is an Array
#  photo_in_game_comments      :jsonb
#  position_in_game            :integer          default(0)
#  realty_asset_uuid           :uuid
#  realty_game_listing_details :jsonb
#  realty_game_listing_flags   :integer          default(0), not null
#  realty_game_uuid            :uuid
#  scoot_uuid                  :uuid
#  statistics_updated_at       :datetime
#  total_guesses_count         :integer          default(0)
#  translations                :jsonb
#  uuid                        :uuid
#  visible_in_game             :boolean          default(TRUE)
#  visible_photo_uuids         :text             default([]), is an Array
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#
# Indexes
#
#  index_realty_game_listings_on_average_guess_cents        (average_guess_cents)
#  index_realty_game_listings_on_discarded_at               (discarded_at)
#  index_realty_game_listings_on_listing_uuid               (listing_uuid)
#  index_realty_game_listings_on_realty_game_listing_flags  (realty_game_listing_flags)
#  index_realty_game_listings_on_realty_game_uuid           (realty_game_uuid)
#  index_realty_game_listings_on_statistics_updated_at      (statistics_updated_at)
#  index_realty_game_listings_on_uuid                       (uuid)
#
require 'rails_helper'

RSpec.describe RealtyGameListing, type: :model do
  pending "add some examples to (or delete) #{__FILE__}"
end
