# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
require 'rails_helper'

RSpec.describe ScrapeItemFromOtm, type: :model do
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:valid_url) { 'https://www.onthemarket.com/details/12345678/' }
  let(:mock_html_content) do
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <title>Test OnTheMarket Property</title>
        <link rel="canonical" href="https://www.onthemarket.com/details/12345678/" />
      </head>
      <body>
        <script id="__NEXT_DATA__">
        {
          "props": {
            "initialReduxState": {
              "property": {
                "id": "12345678",
                "propertyTitle": "Factory Test Property",
                "description": "A test property from factory",
                "bedrooms": 3,
                "bathrooms": 2,
                "priceRaw": 350000,
                "displayAddress": "123 Factory Street, Factory Town",
                "addressLocality": "Factory Town",
                "humanisedPropertyType": "Semi-detached house",
                "images": [
                  {"largeUrl": "https://example.com/img1.jpg"},
                  {"url": "https://example.com/img2.jpg"}
                ],
                "features": [
                  {"id": "test", "feature": "Test Feature"},
                  {"id": "garden", "feature": "Garden"}
                ],
                "location": {"lat": 51.5, "lon": -0.1},
                "agent": {"ukCountry": "england"},
                "documents": [],
                "moreLikeThis": [],
                "rooms": {
                  "descriptions": [
                    {"name": "living_room", "description": "Spacious living room"}
                  ]
                }
              }
            }
          }
        }
        </script>
        <script id="dataLayerContainer">
          window.dataLayer.push({"postcode": "FT1 2ST", "property-id": "12345678"});
        </script>
        <div class="swiper-slide">
          <picture>
            <img src="https://example.com/test-image.jpg" alt="Test property" />
          </picture>
        </div>
        #{'<p>Factory content paragraph.</p>' * 100}
      </body>
      </html>
    HTML
  end
  
  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe '.find_or_create_for_h2c_onthemarket' do
    it 'creates a new scrape item for OnTheMarket' do
      expect {
        ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url)
      }.to change(ScrapeItem, :count).by(1)
    end

    it 'sets the onthemarket flag to true' do
      scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url)
      expect(scrape_item.scrape_is_onthemarket).to be true
    end

    it 'returns the same item for duplicate URLs' do
      first_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url)
      second_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url)
      expect(first_item.id).to eq(second_item.id)
    end

    it 'sets the correct URI components' do
      scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url)
      expect(scrape_item.scrape_uri_host).to eq('www.onthemarket.com')
      expect(scrape_item.scrape_uri_scheme).to eq('https')
    end
  end

  describe '#property_hash_from_scrape_item' do
    let(:scrape_item) { ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url) }

    context 'with valid content' do
      before do
        scrape_item.update!(full_content_before_js: mock_html_content)
      end

      it 'extracts property data successfully' do
        property_hash = scrape_item.property_hash_from_scrape_item
        expect(property_hash).to be_a(Hash)
        expect(property_hash).to have_key('asset_data')
        expect(property_hash).to have_key('listing_data')
        expect(property_hash).to have_key('listing_image_urls')
      end

      it 'includes required asset data fields' do
        property_hash = scrape_item.property_hash_from_scrape_item
        asset_data = property_hash['asset_data']
        
        expect(asset_data).to have_key('count_bedrooms')
        expect(asset_data).to have_key('count_bathrooms')
        expect(asset_data).to have_key('latitude')
        expect(asset_data).to have_key('longitude')
        expect(asset_data).to have_key('description')
        expect(asset_data).to have_key('street_address')
      end

      it 'includes required listing data fields' do
        property_hash = scrape_item.property_hash_from_scrape_item
        listing_data = property_hash['listing_data']
        
        expect(listing_data).to have_key('title')
        expect(listing_data).to have_key('description')
        expect(listing_data).to have_key('price_sale_current_cents')
        expect(listing_data).to have_key('currency')
        expect(listing_data).to have_key('reference')
      end

      it 'extracts images from multiple sources' do
        property_hash = scrape_item.property_hash_from_scrape_item
        images = property_hash['listing_image_urls']
        
        expect(images).to be_an(Array)
        expect(images).not_to be_empty if mock_html_content.include?('img')
      end

      it 'handles both legacy and current data structures' do
        # Test with legacy structure
        legacy_html = mock_html_content.gsub('initialReduxState', 'pageProps')
        scrape_item.update!(full_content_before_js: legacy_html)
        
        expect {
          scrape_item.property_hash_from_scrape_item
        }.not_to raise_error
      end
    end

    context 'with invalid content' do
      it 'raises error when content is too short' do
        scrape_item.update!(full_content_before_js: 'short content')
        
        expect {
          scrape_item.property_hash_from_scrape_item
        }.to raise_error('full_content_before_js unavailabl or too short')
      end

      it 'raises error when content is missing' do
        scrape_item.update!(full_content_before_js: nil)
        
        expect {
          scrape_item.property_hash_from_scrape_item
        }.to raise_error('full_content_before_js unavailabl or too short')
      end
    end
  end

  describe 'data mapping methods' do
    let(:sample_property_data) do
      {
        'id' => '12345678',
        'propertyTitle' => 'Beautiful 3-bedroom house',
        'description' => 'A lovely family home with garden',
        'bedrooms' => 3,
        'bathrooms' => 2,
        'priceRaw' => 450000,
        'displayAddress' => '123 Sample Street, Sample Town',
        'addressLocality' => 'Sample Town',
        'humanisedPropertyType' => 'Semi-detached house',
        'images' => [
          { 'largeUrl' => 'https://example.com/image1.jpg' },
          { 'url' => 'https://example.com/image2.jpg' }
        ],
        'features' => [
          { 'id' => 'feature1', 'feature' => 'Garden' },
          { 'id' => 'feature2', 'feature' => 'Parking' }
        ],
        'location' => {
          'lat' => 51.5074,
          'lon' => -0.1278
        },
        'agent' => {
          'ukCountry' => 'england'
        }
      }
    end

    describe '#map_property_to_asset_schema' do
      let(:scrape_item) { ScrapeItemFromOtm.new }
      let(:related_urls) { { property_documents: [], other_otm_urls: [] } }

      it 'maps property data to asset schema correctly' do
        asset_data = scrape_item.send(:map_property_to_asset_schema, sample_property_data, related_urls)
        
        expect(asset_data['count_bedrooms']).to eq(3)
        expect(asset_data['count_bathrooms']).to eq(2.0)
        expect(asset_data['city']).to eq('Sample Town')
        expect(asset_data['latitude']).to eq(51.5074)
        expect(asset_data['longitude']).to eq(-0.1278)
        expect(asset_data['description']).to eq('A lovely family home with garden')
        expect(asset_data['street_address']).to eq('123 Sample Street, Sample Town')
        expect(asset_data['reference']).to eq('12345678')
      end

      it 'sets default values for missing fields' do
        minimal_data = { 'id' => '123' }
        asset_data = scrape_item.send(:map_property_to_asset_schema, minimal_data, related_urls)
        
        expect(asset_data['count_bedrooms']).to eq(0)
        expect(asset_data['count_bathrooms']).to eq(0.0)
        expect(asset_data['constructed_area']).to eq(0.0)
        expect(asset_data['plot_area']).to eq(0.0)
      end

      it 'processes categories from features' do
        asset_data = scrape_item.send(:map_property_to_asset_schema, sample_property_data, related_urls)
        categories = asset_data['categories']
        
        expect(categories).to be_an(Array)
        expect(categories.length).to eq(2)
        expect(categories.first).to have_key('id')
        expect(categories.first).to have_key('name')
      end
    end

    describe '#map_property_to_listing_schema' do
      let(:scrape_item) { ScrapeItemFromOtm.new }

      it 'maps property data to listing schema correctly' do
        listing_data = scrape_item.send(:map_property_to_listing_schema, sample_property_data)
        
        expect(listing_data['title']).to eq('Beautiful 3-bedroom house')
        expect(listing_data['description']).to eq('A lovely family home with garden')
        expect(listing_data['price_sale_current_cents']).to eq(45000000) # 450000 * 100
        expect(listing_data['currency']).to eq('GBP')
        expect(listing_data['reference']).to eq('12345678')
      end

      it 'sets proper default values' do
        minimal_data = { 'id' => '123', 'priceRaw' => 100000 }
        listing_data = scrape_item.send(:map_property_to_listing_schema, minimal_data)
        
        expect(listing_data['archived']).to be false
        expect(listing_data['visible']).to be true
        expect(listing_data['currency']).to eq('GBP')
        expect(listing_data['commission_cents']).to eq(0)
      end

      it 'processes sale listing features' do
        listing_data = scrape_item.send(:map_property_to_listing_schema, sample_property_data)
        features = listing_data['sale_listing_features']
        
        expect(features).to be_a(Hash)
        expect(features['feature1']).to eq('Garden')
        expect(features['feature2']).to eq('Parking')
      end
    end
  end

  describe 'integration with scraper connector' do
    let(:scrape_item) { ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url) }
    let(:mock_connector) { instance_double(ScraperConnectors::Regular) }

    before do
      allow(ScraperConnectors::Regular).to receive(:new).and_return(mock_connector)
    end

    it 'retrieves content using the regular connector' do
      expect(mock_connector).to receive(:retrieve_data_from_connector)
        .with(kind_of(URI), include_trailing_slash: false)
        .and_return({
          returned_content: mock_html_content,
          scrape_item_target: 'full_content_before_js'
        })

      scrape_item.retrieve_and_set_content_object('ScraperConnectors::Regular')
      
      expect(scrape_item.full_content_before_js).to eq(mock_html_content)
    end
  end

  describe 'default scope' do
    let!(:onthemarket_item) { create(:scrape_item, :onthemarket) }
    let!(:other_item) { create(:scrape_item, scrape_is_onthemarket: false) }

    it 'only returns OnTheMarket items' do
      otm_items = ScrapeItemFromOtm.all
      expect(otm_items.map(&:id)).to include(onthemarket_item.id)
      expect(otm_items.map(&:id)).not_to include(other_item.id)
    end
  end

  describe 'validation and error handling' do
    it 'handles malformed JSON in property extraction' do
      scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url)
      malformed_html = '<html><script id="__NEXT_DATA__">{"invalid": json}</script></html>' * 100
      scrape_item.update!(full_content_before_js: malformed_html)

      expect {
        scrape_item.property_hash_from_scrape_item
      }.to raise_error(JSON::ParserError)
    end

    it 'handles missing __NEXT_DATA__ script' do
      scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(valid_url)
      html_without_json = '<html><body>No JSON here</body></html>' * 100
      scrape_item.update!(full_content_before_js: html_without_json)

      expect {
        scrape_item.property_hash_from_scrape_item
      }.to raise_error
    end
  end
end
