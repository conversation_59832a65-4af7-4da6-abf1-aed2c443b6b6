# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
require 'rails_helper'

RSpec.describe ScrapeItemFromOtmSearch, type: :model do
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:search_url) { 'https://www.onthemarket.com/for-sale/property/london/' }

  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe '.find_or_create_for_otm_search' do
    it 'creates a new search scrape item' do
      expect {
        ScrapeItemFromOtmSearch.find_or_create_for_otm_search(search_url)
      }.to change(ScrapeItem, :count).by(1)
    end

    it 'sets the correct flags for OnTheMarket search' do
      scrape_item = ScrapeItemFromOtmSearch.find_or_create_for_otm_search(search_url)
      
      expect(scrape_item.scrape_is_onthemarket).to be true
      expect(scrape_item.is_realty_search_scrape).to be true
    end

    it 'returns the same item for duplicate search URLs' do
      first_item = ScrapeItemFromOtmSearch.find_or_create_for_otm_search(search_url)
      second_item = ScrapeItemFromOtmSearch.find_or_create_for_otm_search(search_url)
      
      expect(first_item.id).to eq(second_item.id)
    end
  end

  describe 'default scope' do
    let!(:otm_search_item) { create(:scrape_item, scrape_is_onthemarket: true, is_realty_search_scrape: true) }
    let!(:otm_property_item) { create(:scrape_item, scrape_is_onthemarket: true, is_realty_search_scrape: false) }
    let!(:other_search_item) { create(:scrape_item, scrape_is_onthemarket: false, is_realty_search_scrape: true) }

    it 'only returns OnTheMarket search items' do
      search_items = ScrapeItemFromOtmSearch.all
      
      expect(search_items).to include(otm_search_item)
      expect(search_items).not_to include(otm_property_item)
      expect(search_items).not_to include(other_search_item)
    end
  end

  describe '#raw_contents_as_json' do
    let(:scrape_item) { ScrapeItemFromOtmSearch.find_or_create_for_otm_search(search_url) }
    
    context 'with valid JSON content' do
      let(:sample_json_data) do
        {
          'properties' => [
            {
              'id' => '12345',
              'property-title' => 'Test Property',
              'price' => '£350,000',
              'bedrooms' => 3,
              'bathrooms' => 2,
              'images' => [{'default' => 'https://example.com/image.jpg'}]
            }
          ]
        }.to_json
      end

      before do
        scrape_item.update!(full_content_before_js: sample_json_data)
      end

      it 'parses JSON content successfully' do
        result = scrape_item.raw_contents_as_json
        
        expect(result).to be_a(Hash)
        expect(result).to have_key('properties')
        expect(result['properties']).to be_an(Array)
      end
    end

    context 'with invalid JSON content' do
      before do
        scrape_item.update!(full_content_before_js: 'invalid json')
      end

      it 'handles JSON parsing errors gracefully' do
        expect {
          scrape_item.raw_contents_as_json
        }.not_to raise_error
      end
    end

    context 'with missing content' do
      before do
        scrape_item.update!(full_content_before_js: nil)
      end

      it 'returns nil for missing content' do
        result = scrape_item.raw_contents_as_json
        expect(result).to be_nil
      end
    end
  end

  describe '#summary_sale_listings_from_scrape_item' do
    let(:scrape_item) { ScrapeItemFromOtmSearch.find_or_create_for_otm_search(search_url) }
    let(:sample_search_results) do
      {
        'properties' => [
          {
            'id' => '12345',
            'property-title' => 'Beautiful House',
            'price' => '£450,000',
            'bedrooms' => 3,
            'bathrooms' => 2,
            'display_address' => '123 Test Street, London',
            'postcode1' => 'SW1A',
            'location' => { 'lat' => 51.5074, 'lon' => -0.1278 },
            'images' => [{'default' => 'https://example.com/image1.jpg'}],
            'agent' => { 'name' => 'Test Estate Agent' }
          },
          {
            'id' => '67890',
            'property-title' => 'Modern Apartment',
            'price' => '£275,000',
            'bedrooms' => 2,
            'bathrooms' => 1,
            'display_address' => '456 Another Road, London',
            'postcode1' => 'E1 6AN',
            'location' => { 'lat' => 51.5155, 'lon' => -0.0922 },
            'images' => [{'default' => 'https://example.com/image2.jpg'}],
            'agent' => { 'name' => 'Another Agent' }
          }
        ]
      }.to_json
    end

    before do
      scrape_item.update!(full_content_before_js: sample_search_results)
    end

    it 'extracts summary listings from search results' do
      listings = scrape_item.summary_sale_listings_from_scrape_item
      
      expect(listings).to be_an(Array)
      expect(listings.length).to eq(2)
    end

    it 'processes each property correctly' do
      listings = scrape_item.summary_sale_listings_from_scrape_item
      first_listing = listings.first
      
      expect(first_listing).to have_key(:summary_listing_reference)
      expect(first_listing).to have_key(:summary_listing_price)
      expect(first_listing).to have_key(:summary_listing_bedrooms_count)
      expect(first_listing).to have_key(:full_listing_url)
      expect(first_listing).to have_key(:summary_listing_details)
      
      expect(first_listing[:summary_listing_reference]).to eq('12345')
      expect(first_listing[:summary_listing_price]).to eq('450000')
      expect(first_listing[:summary_listing_bedrooms_count]).to eq(3)
      expect(first_listing[:full_listing_url]).to eq('https://www.onthemarket.com/details/12345/')
    end

    it 'includes location coordinates' do
      listings = scrape_item.summary_sale_listings_from_scrape_item
      first_listing = listings.first
      
      expect(first_listing[:summary_listing_latitude]).to eq(51.5074)
      expect(first_listing[:summary_listing_longitude]).to eq(-0.1278)
    end

    it 'includes agent details' do
      listings = scrape_item.summary_sale_listings_from_scrape_item
      first_listing = listings.first
      
      expect(first_listing[:agent_details]).to eq({ 'name' => 'Test Estate Agent' })
      expect(first_listing[:realty_agent_details]).to eq({ 'name' => 'Test Estate Agent' })
    end

    it 'processes image data' do
      listings = scrape_item.summary_sale_listings_from_scrape_item
      first_listing = listings.first
      
      expect(first_listing[:summary_listing_details][:images]).to be_an(Array)
      expect(first_listing[:summary_listing_details][:images].first).to eq('https://example.com/image1.jpg')
    end

    it 'strips non-digits from price' do
      listings = scrape_item.summary_sale_listings_from_scrape_item
      first_listing = listings.first
      
      expect(first_listing[:summary_listing_price]).to eq('450000') # £450,000 -> 450000
    end
  end

  describe '#get_property_hash_from_summary_json' do
    let(:scrape_item) { ScrapeItemFromOtmSearch.new }
    let(:sample_summary_data) do
      {
        'id' => '12345',
        'property-title' => 'Test Property',
        'price' => '£350,000',
        'bedrooms' => 3,
        'bathrooms' => 2,
        'display_address' => '123 Test Street, London',
        'postcode1' => 'SW1A',
        'location' => { 'lat' => 51.5074, 'lon' => -0.1278 },
        'images' => [{'default' => 'https://example.com/image.jpg'}],
        'agent' => { 'name' => 'Test Agent' }
      }.stringify_keys
    end

    it 'maps summary data correctly' do
      result = scrape_item.send(:get_property_hash_from_summary_json, sample_summary_data)
      
      expect(result).to be_a(Hash)
      expect(result[:summary_listing_reference]).to eq('12345')
      expect(result[:summary_listing_price]).to eq('350000')
      expect(result[:summary_listing_bedrooms_count]).to eq(3)
      expect(result[:summary_listing_bathrooms_count]).to eq(2)
      expect(result[:summary_listing_long_address]).to eq('123 Test Street, London')
      expect(result[:summary_listing_postcode]).to eq('SW1A')
    end

    it 'generates correct full listing URL' do
      result = scrape_item.send(:get_property_hash_from_summary_json, sample_summary_data)
      
      expect(result[:full_listing_url]).to eq('https://www.onthemarket.com/details/12345/')
    end

    it 'includes location coordinates' do
      result = scrape_item.send(:get_property_hash_from_summary_json, sample_summary_data)
      
      expect(result[:summary_listing_latitude]).to eq(51.5074)
      expect(result[:summary_listing_longitude]).to eq(-0.1278)
    end

    it 'processes agent details' do
      result = scrape_item.send(:get_property_hash_from_summary_json, sample_summary_data)
      
      expect(result[:agent_details]).to eq({ 'name' => 'Test Agent' })
      expect(result[:realty_agent_details]).to eq({ 'name' => 'Test Agent' })
    end

    it 'includes summary listing details with images' do
      result = scrape_item.send(:get_property_hash_from_summary_json, sample_summary_data)
      
      details = result[:summary_listing_details]
      expect(details[:title]).to eq('Test Property')
      expect(details[:reference]).to eq('12345')
      expect(details[:images]).to eq(['https://example.com/image.jpg'])
    end

    it 'handles missing location data gracefully' do
      data_without_location = sample_summary_data.dup
      data_without_location.delete('location')
      
      result = scrape_item.send(:get_property_hash_from_summary_json, data_without_location)
      
      expect(result[:summary_listing_latitude]).to be_nil
      expect(result[:summary_listing_longitude]).to be_nil
    end

    it 'handles missing images gracefully' do
      data_without_images = sample_summary_data.dup
      data_without_images.delete('images')
      
      result = scrape_item.send(:get_property_hash_from_summary_json, data_without_images)
      
      expect(result[:summary_listing_details][:images]).to be_nil
    end

    it 'returns empty hash for invalid data' do
      result = scrape_item.send(:get_property_hash_from_summary_json, nil)
      
      expect(result).to eq({})
    end
  end

  describe 'integration with parent class' do
    it 'inherits from ScrapeItem' do
      expect(ScrapeItemFromOtmSearch.superclass).to eq(ScrapeItem)
    end

    it 'can access parent class methods' do
      scrape_item = ScrapeItemFromOtmSearch.find_or_create_for_otm_search(search_url)
      
      expect(scrape_item).to respond_to(:scrapable_url)
      expect(scrape_item).to respond_to(:scrape_unique_url)
      expect(scrape_item).to respond_to(:full_content_before_js)
    end
  end
end
