# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
require 'rails_helper'

RSpec.describe ScrapeItemFromCapeverdepropertySearch, type: :model do
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:search_url) { 'https://www.capeverdeproperty.co.uk/buy/property-for-sale/' }
  
  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe '.find_or_create_for_capeverdeproperty_search' do
    it 'creates a new search scrape item with Cape Verde Property flags' do
      scrape_item = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(search_url)
      
      expect(scrape_item).to be_persisted
      expect(scrape_item.scrape_is_capeverdeproperty).to be true
      expect(scrape_item.is_realty_search_scrape).to be true
      expect(scrape_item.scrapable_url).to eq(search_url)
    end

    it 'returns existing search scrape item if already exists' do
      first_item = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(search_url)
      second_item = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(search_url)
      
      expect(first_item.id).to eq(second_item.id)
    end
  end

  describe '#raw_contents_as_html_doc' do
    let(:scrape_item) do
      item = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(search_url)
      item.update!(full_content_before_js: sample_search_html)
      item
    end

    let(:sample_search_html) do
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>Properties for Sale - Cape Verde Property</title>
        </head>
        <body>
          <div class="search-results">
            <div class="property-item">
              <h3><a href="/property/test123/cv/sal/santa-maria/test-property/apartment/2-bedrooms">Test Property</a></h3>
              <div class="price">€100,000</div>
              <div class="location">Santa Maria, Sal</div>
              <div class="beds">2 bedrooms</div>
              <div class="baths">1 bathroom</div>
              <img src="https://wdcdn.co/Media/webp/l/test-image.jpg" alt="Property">
            </div>
            
            <div class="property-item">
              <h3><a href="/property/test456/cv/sal/santa-maria/another-property/villa/3-bedrooms">Another Property</a></h3>
              <div class="price">€150,000</div>
              <div class="location">Santa Maria, Sal</div>
              <div class="beds">3 bedrooms</div>
              <div class="baths">2 bathrooms</div>
              <img src="https://wdcdn.co/Media/webp/l/another-image.jpg" alt="Property">
            </div>
          </div>
        </body>
        </html>
      HTML
    end

    it 'returns Nokogiri document when content is sufficient' do
      doc = scrape_item.raw_contents_as_html_doc
      
      expect(doc).to be_a(Nokogiri::HTML::Document)
      expect(doc.css('.property-item').length).to eq(2)
    end

    it 'returns nil when content is insufficient' do
      scrape_item.update!(full_content_before_js: 'short')
      
      doc = scrape_item.raw_contents_as_html_doc
      expect(doc).to be_nil
    end
  end

  describe '#summary_sale_listings_from_scrape_item' do
    let(:scrape_item) do
      item = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(search_url)
      item.update!(full_content_before_js: sample_search_html)
      item
    end

    let(:sample_search_html) do
      <<~HTML
        <!DOCTYPE html>
        <html>
        <body>
          <div class="property-item">
            <h3><a href="/property/test123/cv/sal/santa-maria/test-property/apartment/2-bedrooms">Test Property Santa Maria, Sal</a></h3>
            <div class="price">€100,000</div>
            <div class="beds">2 bedrooms</div>
            <div class="baths">1 bathroom</div>
            <img src="https://wdcdn.co/Media/webp/l/test-image.jpg" alt="Property">
          </div>
        </body>
        </html>
      HTML
    end

    before do
      # Mock SummaryListing to avoid database dependencies in unit tests
      allow(SummaryListing).to receive(:find_or_initialize_by).and_return(
        double('SummaryListing', assign_attributes: true, realty_search_query_uuid=: true, save!: true)
      )
    end

    it 'extracts summary listings from search results' do
      expect { scrape_item.summary_sale_listings_from_scrape_item }.not_to raise_error
    end
  end

  describe 'private methods' do
    let(:scrape_item) { ScrapeItemFromCapeverdepropertySearch.new }

    describe '#extract_property_from_search_element' do
      let(:property_html) do
        <<~HTML
          <div class="property-item">
            <h3><a href="/property/test123/cv/sal/santa-maria/test-property/apartment/2-bedrooms">Test Property</a></h3>
            <div class="price">€100,000</div>
            <div class="location">Santa Maria, Sal</div>
            <div class="beds">2 bedrooms</div>
            <div class="baths">1 bathroom</div>
            <img src="https://wdcdn.co/Media/webp/l/test-image.jpg" alt="Property">
          </div>
        HTML
      end

      it 'extracts property data from HTML element' do
        doc = Nokogiri::HTML(property_html)
        property_element = doc.at('.property-item')
        
        property = scrape_item.send(:extract_property_from_search_element, property_element)
        
        expect(property['title']).to eq('Test Property')
        expect(property['price']).to eq(100000)
        expect(property['url']).to include('/property/test123')
        expect(property['bedrooms']).to eq(2)
        expect(property['bathrooms']).to eq(1)
        expect(property['image']).to include('wdcdn.co')
      end

      it 'handles missing elements gracefully' do
        doc = Nokogiri::HTML('<div class="property-item"><h3>Basic Property</h3></div>')
        property_element = doc.at('.property-item')
        
        property = scrape_item.send(:extract_property_from_search_element, property_element)
        
        expect(property['title']).to eq('Basic Property')
        expect(property['price']).to be_nil
        expect(property['bedrooms']).to eq(0)
        expect(property['bathrooms']).to eq(0)
      end
    end

    describe '#get_property_hash_from_summary_html' do
      let(:sample_property) do
        {
          'title' => 'Test Property Santa Maria, Sal',
          'price' => 100000,
          'url' => 'https://www.capeverdeproperty.co.uk/property/test123/cv/sal/santa-maria/test-property/apartment/2-bedrooms',
          'bedrooms' => 2,
          'bathrooms' => 1,
          'location' => 'Santa Maria, Sal',
          'image' => 'https://wdcdn.co/Media/webp/l/test-image.jpg'
        }
      end

      it 'converts property to standardized summary format' do
        result = scrape_item.send(:get_property_hash_from_summary_html, sample_property)
        
        expect(result[:summary_listing_reference]).to eq('test123')
        expect(result[:summary_listing_long_address]).to eq('Santa Maria, Sal')
        expect(result[:summary_listing_price]).to eq(100000)
        expect(result[:summary_listing_bedrooms_count]).to eq(2)
        expect(result[:summary_listing_bathrooms_count]).to eq(1)
        expect(result[:full_listing_url]).to include('test123')
        expect(result[:agent_details][:company_name]).to eq('Cape Verde Property')
      end

      it 'handles missing data gracefully' do
        minimal_property = { 'title' => 'Basic Property' }
        
        result = scrape_item.send(:get_property_hash_from_summary_html, minimal_property)
        
        expect(result[:summary_listing_price]).to eq(0)
        expect(result[:summary_listing_bedrooms_count]).to eq(0)
        expect(result[:summary_listing_bathrooms_count]).to eq(0)
        expect(result[:agent_details][:company_name]).to eq('Cape Verde Property')
      end
    end
  end

  describe 'default scope' do
    it 'filters items with Cape Verde Property and search flags' do
      search_item = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(search_url)
      
      # Create a regular Cape Verde Property item (not search)
      regular_item = ScrapeItem.create!(
        scrapable_url: 'https://www.capeverdeproperty.co.uk/property/test/',
        scrape_unique_url: 'https://www.capeverdeproperty.co.uk/property/test/#hpg',
        scrape_is_capeverdeproperty: true,
        is_realty_search_scrape: false,
        agency_tenant_uuid: agency_tenant.uuid
      )
      
      # Create an item from different portal
      other_item = ScrapeItem.create!(
        scrapable_url: 'https://example.com',
        scrape_unique_url: 'https://example.com#hpg',
        is_realty_search_scrape: true,
        agency_tenant_uuid: agency_tenant.uuid
      )
      
      expect(ScrapeItemFromCapeverdepropertySearch.count).to eq(1)
      expect(ScrapeItemFromCapeverdepropertySearch.first.id).to eq(search_item.id)
    end
  end
end
