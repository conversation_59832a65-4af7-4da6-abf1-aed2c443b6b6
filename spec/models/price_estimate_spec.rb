# == Schema Information
#
# Table name: price_estimates
#
#  id                              :bigint           not null, primary key
#  agency_tenant_uuid              :uuid
#  count_sold_transactions_shown   :integer          default(0), not null
#  discarded_at                    :datetime
#  estimate_currency               :string           default("GBP"), not null
#  estimate_details                :jsonb
#  estimate_flags                  :integer          default(0), not null
#  estimate_latitude_center        :float
#  estimate_longitude_center       :float
#  estimate_postal_code            :string
#  estimate_text                   :text
#  estimate_title                  :string
#  estimate_vicinity               :string
#  estimated_price_cents           :bigint           default(0), not null
#  estimator_name                  :string
#  extra_uuid                      :uuid
#  is_ai_estimate                  :boolean          default(FALSE), not null
#  is_for_rental_listing           :boolean          default(FALSE), not null
#  is_for_sale_listing             :boolean          default(TRUE), not null
#  is_protected                    :boolean          default(FALSE), not null
#  listing_uuid                    :uuid
#  percentage_above_or_below       :integer          default(0), not null
#  price_at_time_of_estimate_cents :bigint           default(0), not null
#  realty_dossier_uuid             :uuid
#  scoot_uuid                      :uuid
#  user_uuid                       :uuid
#  uuid                            :uuid             not null
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  game_session_id                 :string
#
# Indexes
#
#  index_price_estimates_on_agency_tenant_uuid   (agency_tenant_uuid)
#  index_price_estimates_on_discarded_at         (discarded_at)
#  index_price_estimates_on_estimate_flags       (estimate_flags)
#  index_price_estimates_on_listing_uuid         (listing_uuid)
#  index_price_estimates_on_realty_dossier_uuid  (realty_dossier_uuid)
#  index_price_estimates_on_scoot_uuid           (scoot_uuid)
#  index_price_estimates_on_user_uuid            (user_uuid)
#  index_price_estimates_on_uuid                 (uuid) UNIQUE
#
require 'rails_helper'

RSpec.describe PriceEstimate, type: :model do
  pending "add some examples to (or delete) #{__FILE__}"
end
