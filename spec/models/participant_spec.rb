# == Schema Information
#
# Table name: participants
#
#  id                       :bigint           not null, primary key
#  average_session_duration :decimal(10, 2)
#  behavior_patterns        :jsonb
#  engagement_metrics       :jsonb
#  first_browser            :string
#  first_city               :string
#  first_country            :string
#  first_device_type        :string
#  first_landing_page       :text
#  first_os                 :string
#  first_referrer           :text
#  first_utm_campaign       :string
#  first_utm_medium         :string
#  first_utm_source         :string
#  first_visit_at           :datetime
#  last_visit_at            :datetime
#  returning_visitor        :boolean          default(FALSE)
#  total_events             :integer          default(0)
#  total_page_views         :integer          default(0)
#  total_visits             :integer          default(0)
#  unique_pages_visited     :integer          default(0)
#  visitor_token            :string           not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
# Indexes
#
#  index_participants_on_behavior_patterns   (behavior_patterns) USING gin
#  index_participants_on_engagement_metrics  (engagement_metrics) USING gin
#  index_participants_on_first_visit_at      (first_visit_at)
#  index_participants_on_last_visit_at       (last_visit_at)
#  index_participants_on_returning_visitor   (returning_visitor)
#  index_participants_on_total_visits        (total_visits)
#  index_participants_on_visitor_token       (visitor_token) UNIQUE
#
require 'rails_helper'

RSpec.describe Participant, type: :model do
  let(:participant) { create(:participant) }
  let(:ahoy_visit) { create(:ahoy_visit, visitor_token: participant.visitor_token) }

  describe 'validations' do
    it { should validate_presence_of(:visitor_token) }
    it { should validate_uniqueness_of(:visitor_token) }
  end

  describe 'associations' do
    it { should have_many(:ahoy_visits).with_primary_key(:visitor_token).with_foreign_key(:visitor_token) }
    it { should have_many(:ahoy_events).through(:ahoy_visits) }
  end

  describe 'scopes' do
    let!(:returning_participant) { create(:participant, returning_visitor: true) }
    let!(:new_participant) { create(:participant, returning_visitor: false) }
    let!(:high_engagement_participant) { create(:participant, total_visits: 10) }
    let!(:recent_participant) { create(:participant, last_visit_at: 1.day.ago) }

    describe '.returning' do
      it 'returns only returning visitors' do
        expect(Participant.returning).to include(returning_participant)
        expect(Participant.returning).not_to include(new_participant)
      end
    end

    describe '.new_visitors' do
      it 'returns only new visitors' do
        expect(Participant.new_visitors).to include(new_participant)
        expect(Participant.new_visitors).not_to include(returning_participant)
      end
    end

    describe '.high_engagement' do
      it 'returns participants with more than 5 visits' do
        expect(Participant.high_engagement).to include(high_engagement_participant)
        expect(Participant.high_engagement).not_to include(new_participant)
      end
    end

    describe '.recent' do
      it 'returns participants active in the last 30 days' do
        expect(Participant.recent).to include(recent_participant)
      end
    end
  end

  describe '#engagement_score' do
    it 'calculates engagement score based on visits, events, and pages' do
      participant.update!(
        total_visits: 5,
        total_events: 10,
        unique_pages_visited: 3,
        returning_visitor: true
      )

      expected_score = (5 * 10) + (10 * 2) + (3 * 5) + 20 # base + events + pages + returning
      expect(participant.engagement_score).to eq(expected_score)
    end

    it 'caps base score at 100' do
      participant.update!(total_visits: 20) # Would be 200, but capped at 100
      expect(participant.engagement_score).to be <= 100 + 50 + 30 + 20 # max possible
    end
  end

  describe '#behavior_category' do
    it 'returns "inactive" for participants with no visits' do
      participant.update!(total_visits: 0)
      expect(participant.behavior_category).to eq('inactive')
    end

    it 'returns "explorer" for participants with many unique pages' do
      participant.update!(unique_pages_visited: 15, total_visits: 5)
      expect(participant.behavior_category).to eq('explorer')
    end

    it 'returns "engaged" for participants with many events' do
      participant.update!(total_events: 25, unique_pages_visited: 5, total_visits: 5)
      expect(participant.behavior_category).to eq('engaged')
    end

    it 'returns "regular" for returning visitors with multiple visits' do
      participant.update!(returning_visitor: true, total_visits: 5, total_events: 10, unique_pages_visited: 3)
      expect(participant.behavior_category).to eq('regular')
    end

    it 'returns "casual" for participants with few visits' do
      participant.update!(total_visits: 2, total_events: 5, unique_pages_visited: 2)
      expect(participant.behavior_category).to eq('casual')
    end
  end

  describe '#sync_with_visit' do
    let(:visit) do
      create(:ahoy_visit,
        visitor_token: participant.visitor_token,
        started_at: 2.days.ago,
        referrer: 'https://google.com',
        landing_page: '/home',
        country: 'US',
        city: 'New York',
        device_type: 'Desktop'
      )
    end

    it 'updates first visit data when no previous first visit' do
      participant.update!(first_visit_at: nil)
      participant.sync_with_visit(visit)

      expect(participant.first_visit_at).to eq(visit.started_at)
      expect(participant.first_referrer).to eq(visit.referrer)
      expect(participant.first_landing_page).to eq(visit.landing_page)
      expect(participant.first_country).to eq(visit.country)
    end

    it 'updates last visit data' do
      participant.sync_with_visit(visit)
      expect(participant.last_visit_at).to eq(visit.started_at)
    end

    it 'recalculates totals' do
      expect(participant).to receive(:recalculate_totals)
      participant.sync_with_visit(visit)
    end
  end

  describe '#days_since_first_visit' do
    it 'returns 0 when no first visit' do
      participant.update!(first_visit_at: nil)
      expect(participant.days_since_first_visit).to eq(0)
    end

    it 'calculates days since first visit' do
      participant.update!(first_visit_at: 5.days.ago)
      expect(participant.days_since_first_visit).to be_within(1).of(5)
    end
  end

  describe '#days_since_last_visit' do
    it 'returns 0 when no last visit' do
      participant.update!(last_visit_at: nil)
      expect(participant.days_since_last_visit).to eq(0)
    end

    it 'calculates days since last visit' do
      participant.update!(last_visit_at: 3.days.ago)
      expect(participant.days_since_last_visit).to be_within(1).of(3)
    end
  end

  describe '.analytics_summary' do
    let!(:participants) { create_list(:participant, 3, total_visits: 5, total_events: 10) }
    let!(:high_engagement) { create(:participant, total_visits: 10) }

    it 'returns analytics summary' do
      summary = Participant.analytics_summary

      expect(summary[:total_participants]).to eq(Participant.count)
      expect(summary[:average_visits_per_participant]).to be > 0
      expect(summary[:average_events_per_participant]).to be > 0
      expect(summary[:high_engagement_participants]).to eq(1)
    end
  end

  describe 'callbacks' do
    it 'calculates returning visitor status before save' do
      participant.total_visits = 3
      participant.save!
      expect(participant.returning_visitor).to be true
    end

    it 'updates engagement metrics before save' do
      participant.total_visits = 5
      participant.total_events = 10
      participant.save!

      expect(participant.engagement_metrics['engagement_score']).to be > 0
      expect(participant.engagement_metrics['behavior_category']).to be_present
    end
  end
end
