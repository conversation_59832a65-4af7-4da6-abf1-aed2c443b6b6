# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
require 'rails_helper'

RSpec.describe ScrapeItemFromCapeverdeproperty, type: :model do
  let(:agency_tenant) { AgencyTenant.unique_tenant }
  let(:sample_url) { 'https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms' }
  
  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe '.find_or_create_for_h2c_capeverdeproperty' do
    it 'creates a new scrape item with Cape Verde Property flag' do
      scrape_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      
      expect(scrape_item).to be_persisted
      expect(scrape_item.scrape_is_capeverdeproperty).to be true
      expect(scrape_item.scrapable_url).to eq(sample_url)
    end

    it 'returns existing scrape item if already exists' do
      first_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      second_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      
      expect(first_item.id).to eq(second_item.id)
    end
  end

  describe '#property_hash_from_scrape_item' do
    let(:sample_html) do
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>CA OCEANO 2 BEDROOM APARTMENT</title>
          <link rel="canonical" href="https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms">
        </head>
        <body>
          <h1>CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA, SAL</h1>
          <h2>€125,000</h2>
          <h2>2 Bedroom Apartment For Sale</h2>
          <h3>Tenure: Freehold</h3>
          
          <p>Sea Views, 2 bed first floor apartment located by the Budda Beach Hotel in the east of Santa Maria. Fully Furnished. Great location.</p>
          
          <img src="https://maps.googleapis.com/maps/api/staticmap?center=16.5963490599297,-22.8951838191986&zoom=13&size=2000x1000" alt="Map">
          
          <div class="main-features">
            <ul>
              <li>1 bathroom(s)</li>
              <li>Condition: Excellent</li>
              <li>Furnished</li>
              <li>Sea view</li>
              <li>Distance from the sea: Walking</li>
              <li>Distance from an airport: 15 mins</li>
            </ul>
          </div>
          
          <img src="https://wdcdn.co/Media/webp/l/6e70a3d7-8920-48e8-8409-46e1b1606436.jpg" alt="Property Image">
          <img src="https://wdcdn.co/Media/webp/l/4ef69a11-404b-4148-910f-0234126192ab.jpg" alt="Property Image">
          
          <h3>OFFICE DETAILS</h3>
          <div>
            <a href="/offices/estate-agents/cape-verde">Number 3, Residence Isla do Fogo, Rua 1 De Junho, Santa Maria, Sal, Cape Verde</a>
            <a href="tel:002382422041">00238 242 2041</a>
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </div>
        </body>
        </html>
      HTML
    end

    let(:scrape_item) do
      item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      item.update!(full_content_before_js: sample_html)
      item
    end

    it 'extracts property data successfully' do
      property_data = scrape_item.property_hash_from_scrape_item
      
      expect(property_data).to be_a(Hash)
      expect(property_data['listing_data']).to be_present
      expect(property_data['asset_data']).to be_present
    end

    it 'extracts basic property information' do
      property_data = scrape_item.property_hash_from_scrape_item
      listing_data = property_data['listing_data']
      
      expect(listing_data['title']).to eq('CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA, SAL')
      expect(listing_data['price_sale_current_cents']).to eq(12_500_000) # €125,000 in cents
      expect(listing_data['description']).to include('Sea Views')
    end

    it 'extracts asset information' do
      property_data = scrape_item.property_hash_from_scrape_item
      asset_data = property_data['asset_data']
      
      expect(asset_data['count_bedrooms']).to eq(2)
      expect(asset_data['count_bathrooms']).to eq(1.0)
      expect(asset_data['city']).to eq('SANTA MARIA')
      expect(asset_data['region']).to eq('SAL')
      expect(asset_data['country']).to eq('Cape Verde')
    end

    it 'extracts location coordinates' do
      property_data = scrape_item.property_hash_from_scrape_item
      asset_data = property_data['asset_data']
      
      expect(asset_data['latitude']).to eq(16.5963490599297)
      expect(asset_data['longitude']).to eq(-22.8951838191986)
    end

    it 'extracts image URLs' do
      property_data = scrape_item.property_hash_from_scrape_item
      image_urls = property_data['listing_image_urls']
      
      expect(image_urls).to be_an(Array)
      expect(image_urls.length).to eq(2)
      expect(image_urls.first).to include('wdcdn.co')
    end

    it 'extracts features' do
      property_data = scrape_item.property_hash_from_scrape_item
      asset_data = property_data['asset_data']
      
      expect(asset_data['realty_asset_tags']).to include('Furnished')
      expect(asset_data['realty_asset_tags']).to include('Sea view')
    end

    it 'raises error when content is insufficient' do
      scrape_item.update!(full_content_before_js: 'short content')
      
      expect {
        scrape_item.property_hash_from_scrape_item
      }.to raise_error(/full_content_before_js unavailable or too short/)
    end
  end

  describe 'default scope' do
    it 'filters items with Cape Verde Property flag' do
      cvp_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(sample_url)
      other_item = ScrapeItem.create!(
        scrapable_url: 'https://example.com',
        scrape_unique_url: 'https://example.com#hpg',
        agency_tenant_uuid: agency_tenant.uuid
      )
      
      expect(ScrapeItemFromCapeverdeproperty.count).to eq(1)
      expect(ScrapeItemFromCapeverdeproperty.first.id).to eq(cvp_item.id)
    end
  end

  describe 'schema mapping' do
    let(:sample_property) do
      {
        'title' => 'Test Property',
        'price_raw' => 100000,
        'bedrooms' => 2,
        'bathrooms' => 1,
        'property_type' => 'apartment',
        'description' => 'Nice property with sea views',
        'features' => ['Furnished', 'Sea view', 'Balcony'],
        'location' => { 'city' => 'Santa Maria', 'region' => 'Sal', 'lat' => 16.59, 'lon' => -22.89 },
        'reference' => 'CVP123'
      }
    end

    it 'maps property to listing schema correctly' do
      scrape_item = ScrapeItemFromCapeverdeproperty.new
      
      listing_data = scrape_item.send(:map_property_to_listing_schema, sample_property)
      
      expect(listing_data['title']).to eq('Test Property')
      expect(listing_data['price_sale_current_cents']).to eq(10_000_000)
      expect(listing_data['currency']).to eq('EUR')
      expect(listing_data['furnished']).to be true
    end

    it 'maps property to asset schema correctly' do
      scrape_item = ScrapeItemFromCapeverdeproperty.new
      
      asset_data = scrape_item.send(:map_property_to_asset_schema, sample_property)
      
      expect(asset_data['title']).to eq('Test Property')
      expect(asset_data['count_bedrooms']).to eq(2)
      expect(asset_data['count_bathrooms']).to eq(1.0)
      expect(asset_data['city']).to eq('Santa Maria')
      expect(asset_data['country']).to eq('Cape Verde')
      expect(asset_data['latitude']).to eq(16.59)
      expect(asset_data['longitude']).to eq(-22.89)
    end
  end
end
