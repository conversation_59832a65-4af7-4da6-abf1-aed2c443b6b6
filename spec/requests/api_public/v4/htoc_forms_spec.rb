require 'rails_helper'

RSpec.describe 'ApiPublic::V4::HtocFormsController', type: :request do
  describe 'POST /api_public/v4/forms/:form_id' do
    let(:form_id) { 'generic_form_test' }
    let(:customer_request_form_params) do
      {
        name: 'Test User',
        email: '<EMAIL>',
        message: 'This is a test message.'
      }
    end
    let(:headers) { { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' } }

    before do
      # Ensure an AgencyTenant exists if AgencyTenant.unique_tenant requires one
      # This will create an agency_tenant if the factory is defined.
      # If AgencyTenant.unique_tenant relies on a specific instance or setup,
      # this might need to be more specific.
      if FactoryBot.factories.registered?(:agency_tenant)
        create(:agency_tenant)
      else
        # If no factory, we might need to mock or skip, depending on how critical
        # AgencyTenant.unique_tenant is to this specific controller action.
        # For now, we proceed, assuming it's either not strictly required for this path
        # or the default setup (if any) is sufficient.
        # Rails.logger.warn "Consider creating an AgencyTenant or mocking AgencyTenant.unique_tenant for HtocFormsController specs."
      end
    end

    context 'when submitting a generic form (not "start_htoc_a")' do
      it 'creates a new Communication record' do
        expect {
          post "/api_public/v4/forms/#{form_id}",
               params: { customerRequestForm: customer_request_form_params }.to_json,
               headers: headers
        }.to change(Communication, :count).by(1)
      end

      it 'returns a 200 OK status with success true and the communication uuid' do
        post "/api_public/v4/forms/#{form_id}",
             params: { customerRequestForm: customer_request_form_params }.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['success']).to be_truthy
        expect(json['comm_uuid']).to be_a(String)

        created_communication = Communication.find_by(uuid: json['comm_uuid'])
        expect(created_communication).not_to be_nil
        expect(created_communication.comm_form_name).to eq(form_id)
        # expect(created_communication.request_referrer).not_to be_nil # This depends on Referer header
      end

      it 'assigns comm_form data to the Communication record (basic check)' do
        # The controller snippet doesn't explicitly show generic comm_form params
        # being deeply stored in a structured way for Communication records
        # outside of the 'start_htoc_a' path.
        # This test primarily ensures the request succeeds and essential fields are set.
        post "/api_public/v4/forms/#{form_id}",
             params: { customerRequestForm: customer_request_form_params }.to_json,
             headers: headers
        expect(response).to have_http_status(:ok)
        created_communication = Communication.find_by(uuid: JSON.parse(response.body)['comm_uuid'])
        expect(created_communication).not_to be_nil
        # If `details_json` or a similar field stores the params:
        # expect(created_communication.details_json['name']).to eq(customer_request_form_params[:name])
        # expect(created_communication.details_json['email']).to eq(customer_request_form_params[:email])
        # For now, we've confirmed comm_form_name and creation.
      end
    end

    context 'when form_id is "start_htoc_a"' do
      let(:start_htoc_form_id) { 'start_htoc_a' }

      # Mock Communications::StartHtocComm to avoid testing its internal logic here
      # and to control its output.
      let(:mock_communication_instance) { instance_double(Communication, uuid: SecureRandom.uuid, update!: true, request_referrer: nil, 'request_referrer=': true) }

      before do
        # Ensure the class Communications::StartHtocComm is defined for stubbing/mocking if not already loaded
        # If it's an actual class, this stub_const might not be necessary,
        # but good for isolation if the class itself has complex dependencies.
        unless defined?(Communications::StartHtocComm)
          # Define a dummy class for the test if it's not found.
          # This is a simplified approach. A more robust way would be to ensure
          # the path to this class is in Rails' autoload_paths for test env
          # or require the file explicitly.
          module Communications; class StartHtocComm; end; end
        end

        allow(Communications::StartHtocComm).to receive(:create_from_form).with(any_args).and_return(mock_communication_instance)
      end

      it 'calls Communications::StartHtocComm.create_from_form with correct params' do
        # Use deep_stringify_keys because controller params are usually stringified.
        expect(Communications::StartHtocComm).to receive(:create_from_form).with(customer_request_form_params.deep_stringify_keys).and_return(mock_communication_instance)

        post "/api_public/v4/forms/#{start_htoc_form_id}",
             params: { customerRequestForm: customer_request_form_params }.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['success']).to be_truthy
        expect(json['comm_uuid']).to eq(mock_communication_instance.uuid)
      end

       it 'updates the communication record with request_referrer' do
        # Ensure the mock instance is prepared to receive the update! call
        expect(mock_communication_instance).to receive(:request_referrer=).with('http://test.referer')
        # The controller calls `update!` separately after setting `request_referrer`.
        expect(mock_communication_instance).to receive(:update!).with(no_args)


        post "/api_public/v4/forms/#{start_htoc_form_id}",
             params: { customerRequestForm: customer_request_form_params }.to_json,
             headers: headers.merge({ 'Referer' => 'http://test.referer' }) # Pass Referer header

        expect(response).to have_http_status(:ok)
      end
    end

    # Consider adding tests for failure scenarios:
    # - What happens if Communications::StartHtocComm.create_from_form raises an error?
    # - What happens if Communication.create! (for generic forms) raises an error (e.g., validation)?
    #   The current controller action does not have explicit rescue blocks for these,
    #   so it would likely result in a 500 error, which could be tested.
  end
end
