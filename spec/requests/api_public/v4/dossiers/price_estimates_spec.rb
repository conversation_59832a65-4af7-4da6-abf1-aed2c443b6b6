# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'ApiPublic::V4::Dossiers::PriceEstimatesController', type: :request do
  let!(:agency_tenant) { create(:agency_tenant) }
  let!(:realty_dossier) { create(:realty_dossier, agency_tenant: agency_tenant) }
  let!(:price_estimate) { create(:price_estimate, realty_dossier: realty_dossier, agency_tenant: agency_tenant) }
  let!(:other_price_estimate) { create(:price_estimate, :ai_generated, realty_dossier: realty_dossier, agency_tenant: agency_tenant) }
  let!(:discarded_estimate) { create(:price_estimate, :discarded, realty_dossier: realty_dossier, agency_tenant: agency_tenant) }

  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe 'GET /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates' do
    context 'when the dossier exists' do
      it 'returns a 200 OK status and lists all kept price estimates' do
        get "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates"

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['price_estimates']).to be_an(Array)
        expect(json['price_estimates'].length).to eq(2) # Only kept estimates

        # Check that discarded estimate is not included
        estimate_uuids = json['price_estimates'].map { |pe| pe['uuid'] }
        expect(estimate_uuids).to include(price_estimate.uuid)
        expect(estimate_uuids).to include(other_price_estimate.uuid)
        expect(estimate_uuids).not_to include(discarded_estimate.uuid)

        # Check response structure
        first_estimate = json['price_estimates'].first
        expect(first_estimate).to include('uuid', 'estimated_price_cents', 'estimate_currency',
                                          'estimate_title', 'estimator_name', 'is_ai_estimate',
                                          'formatted_estimated_price')
      end
    end

    context 'when the dossier does not exist' do
      it 'returns a 404 Not Found status' do
        get '/api_public/v4/dossiers/invalid-uuid/price_estimates'
        expect(response).to have_http_status(:not_found)

        json = JSON.parse(response.body)
        expect(json['error']).to eq('Dossier not found')
      end
    end
  end

  describe 'GET /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid' do
    context 'when the price estimate exists' do
      it 'returns a 200 OK status and the price estimate details' do
        get "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates/#{price_estimate.uuid}"

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['price_estimate']['uuid']).to eq(price_estimate.uuid)
        expect(json['price_estimate']['estimated_price_cents']).to eq(price_estimate.estimated_price_cents)
        expect(json['price_estimate']['estimate_title']).to eq(price_estimate.estimate_title)
        expect(json['price_estimate']).to include('formatted_estimated_price', 'formatted_price_at_time_of_estimate')
      end
    end

    context 'when the price estimate does not exist' do
      it 'returns a 404 Not Found status' do
        get "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates/invalid-uuid"
        expect(response).to have_http_status(:not_found)

        json = JSON.parse(response.body)
        expect(json['error']).to eq('Price estimate not found')
      end
    end

    context 'when trying to access a discarded estimate' do
      it 'returns a 404 Not Found status' do
        get "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates/#{discarded_estimate.uuid}"
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'POST /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates' do
    let(:valid_params) do
      {
        price_estimate: {
          estimated_price_cents: 60_000_000,
          estimate_currency: 'GBP',
          estimate_title: 'New Valuation',
          estimate_text: 'Updated market analysis',
          estimator_name: 'Jane Doe',
          is_ai_estimate: false,
          is_for_sale_listing: true,
          percentage_above_or_below: 5
        }
      }
    end

    context 'with valid parameters' do
      it 'creates a new price estimate and returns 201 Created' do
        expect do
          post "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates",
               params: valid_params, as: :json
        end.to change(PriceEstimate, :count).by(1)

        expect(response).to have_http_status(:created)
        json = JSON.parse(response.body)

        expect(json['price_estimate']['estimated_price_cents']).to eq(60_000_000)
        expect(json['price_estimate']['estimate_title']).to eq('New Valuation')
        expect(json['message']).to eq('Price estimate created successfully')

        # Verify the estimate is associated with the correct dossier
        created_estimate = PriceEstimate.find_by(uuid: json['price_estimate']['uuid'])
        expect(created_estimate.realty_dossier_uuid).to eq(realty_dossier.uuid)
        expect(created_estimate.agency_tenant_uuid).to eq(agency_tenant.uuid)
      end
    end

    context 'with invalid parameters' do
      let(:invalid_params) do
        {
          price_estimate: {
            estimated_price_cents: nil,
            estimate_currency: '',
            estimate_title: ''
          }
        }
      end

      it 'returns 422 Unprocessable Entity with error messages' do
        post "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates",
             params: invalid_params, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        json = JSON.parse(response.body)

        expect(json['errors']).to be_an(Array)
        expect(json['message']).to eq('Failed to create price estimate')
      end
    end
  end

  describe 'PUT /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid' do
    let(:update_params) do
      {
        price_estimate: {
          estimated_price_cents: 55_000_000,
          estimate_title: 'Updated Valuation',
          estimate_text: 'Revised market analysis',
          percentage_above_or_below: 10
        }
      }
    end

    context 'with valid parameters' do
      it 'updates the price estimate and returns 200 OK' do
        put "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates/#{price_estimate.uuid}",
            params: update_params, as: :json

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['price_estimate']['estimated_price_cents']).to eq(55_000_000)
        expect(json['price_estimate']['estimate_title']).to eq('Updated Valuation')
        expect(json['message']).to eq('Price estimate updated successfully')

        # Verify the estimate was actually updated
        price_estimate.reload
        expect(price_estimate.estimated_price_cents).to eq(55_000_000)
        expect(price_estimate.estimate_title).to eq('Updated Valuation')
      end
    end

    context 'with invalid parameters' do
      let(:invalid_update_params) do
        {
          price_estimate: {
            estimated_price_cents: -1000
          }
        }
      end

      it 'returns 422 Unprocessable Entity with error messages' do
        put "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates/#{price_estimate.uuid}",
            params: invalid_update_params, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        json = JSON.parse(response.body)

        expect(json['errors']).to be_an(Array)
        expect(json['message']).to eq('Failed to update price estimate')
      end
    end

    context 'when the price estimate does not exist' do
      it 'returns 404 Not Found' do
        put "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates/invalid-uuid",
            params: update_params, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'DELETE /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid' do
    context 'when the price estimate exists' do
      it 'soft deletes the price estimate and returns 200 OK' do
        delete "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates/#{price_estimate.uuid}"

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['message']).to eq('Price estimate deleted successfully')

        # Verify the estimate was soft deleted
        price_estimate.reload
        expect(price_estimate.discarded?).to be true
      end
    end

    context 'when the price estimate does not exist' do
      it 'returns 404 Not Found' do
        delete "/api_public/v4/dossiers/#{realty_dossier.uuid}/price_estimates/invalid-uuid"

        expect(response).to have_http_status(:not_found)

        json = JSON.parse(response.body)
        expect(json['error']).to eq('Price estimate not found')
      end
    end
  end
end
