require 'rails_helper'

RSpec.describe 'ApiPublic::V4::SaleListingsController', type: :request do
  describe 'GET /api_public/v4/sale_listings/:sale_listing_uuid' do
    # Assuming a :sale_listing factory exists and it has a `uuid` attribute.
    # Also assuming the factory creates associated records if necessary (e.g., realty_asset)
    # for the jbuilder view to render correctly.
    let!(:realty_asset) { create(:realty_asset) } # Create a RealtyAsset first
    let!(:sale_listing) { create(:sale_listing, realty_asset: realty_asset) } # Associate SaleListing with it

    context 'when the sale listing exists' do
      it 'returns a 200 OK status and the sale listing details' do
        get "/api_public/v4/sale_listings/#{sale_listing.uuid}"

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['uuid']).to eq(sale_listing.uuid)
        # Example of checking another attribute, assuming 'title' exists from the jbuilder
        # expect(json['title']).to eq(sale_listing.title) 
        # Add more detailed assertions based on `app/views/api_public/v4/sale_listings/show.json.jbuilder`
        # For now, checking presence of key fields that are typical.
        expect(json).to include('uuid')
        expect(json).to include('status_code')
        expect(json).to include('title')
        # ... other fields as defined in the jbuilder view
      end
    end

    context 'when the sale listing does not exist' do
      it 'returns a 404 Not Found status' do
        get "/api_public/v4/sale_listings/invalid-uuid"
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  # Tests for the list action will be added here later

  describe 'GET /api_public/v4/sale_listings/list/superwiser' do
    # Create some sale listings, including kept and discarded ones, with varying creation times
    # Ensure each listing has a realty_asset, as it's often a required association.
    let!(:realty_asset1) { create(:realty_asset) }
    let!(:realty_asset2) { create(:realty_asset) }
    let!(:realty_asset3) { create(:realty_asset) }
    let!(:realty_asset4) { create(:realty_asset) }

    let!(:listing1_kept) { create(:sale_listing, realty_asset: realty_asset1, created_at: 1.day.ago) }
    let!(:listing2_kept_newest) { create(:sale_listing, realty_asset: realty_asset2, created_at: Time.current) }
    # Assuming a :discarded trait or similar. Ensure your factory supports it.
    # If :discarded trait sets discarded_at, this should work.
    let!(:listing3_discarded) { create(:sale_listing, :discarded, realty_asset: realty_asset3, created_at: 2.days.ago) }
    let!(:listing4_kept_oldest) { create(:sale_listing, realty_asset: realty_asset4, created_at: 3.days.ago) }


    before do
      # If :discarded trait isn't available or doesn't set `discarded_at` properly,
      # and your model uses the `discard` gem, you might manually discard:
      # listing3_discarded.discard! # Use discard! if you want to ensure it's persisted
      # For this test, we rely on the factory or the model's behavior for `kept` scope.
      # If the :discarded trait doesn't work, this test for discarded items might not be accurate.
      # To be certain, one could also do:
      # listing3_discarded.update(discarded_at: Time.current)
      # However, using traits or direct model methods (`.discard`) is cleaner.
    end

    it 'returns a 200 OK status' do
      get "/api_public/v4/sale_listings/list/superwiser"
      expect(response).to have_http_status(:ok)
    end

    it 'returns only kept sale listings' do
      get "/api_public/v4/sale_listings/list/superwiser"
      json = JSON.parse(response.body)
      expect(json).to be_an(Array) # Ensure it's an array
      returned_uuids = json.map { |item| item['uuid'] }

      expect(returned_uuids).to include(listing1_kept.uuid, listing2_kept_newest.uuid, listing4_kept_oldest.uuid)
      expect(returned_uuids).not_to include(listing3_discarded.uuid)
    end

    it 'returns sale listings ordered by created_at descending' do
      get "/api_public/v4/sale_listings/list/superwiser"
      json = JSON.parse(response.body)
      expect(json).to be_an(Array) # Ensure it's an array
      returned_uuids = json.map { |item| item['uuid'] }

      # Expected order: newest to oldest among kept listings
      expect(returned_uuids).to eq([listing2_kept_newest.uuid, listing1_kept.uuid, listing4_kept_oldest.uuid])
    end

    it 'notes that this endpoint currently lacks authentication (as per controller TODO)' do
      # This is more of a documentation test or a reminder.
      # No specific assertion other than the test passing if the request is successful without auth.
      get "/api_public/v4/sale_listings/list/superwiser"
      expect(response).to have_http_status(:ok)
      # Consider adding a Kaminari test for pagination if the endpoint supports it.
      # For example, if Kaminari is used and configured:
      # expect(response.headers['Total-Count']).to eq('3') # Assuming 3 kept items
    end
  end
end
