require 'rails_helper'

RSpec.describe 'ApiPublic::V4::DossiersController', type: :request do
  describe 'GET /api_public/v4/dossiers/:realty_dossier_uuid' do
    # Factories will be used here, assume they exist for:
    # :realty_dossier, :sale_listing, :scoot
    # (e.g., create(:realty_dossier), create(:sale_listing, realty_dossier: dossier), create(:scoot))

    let!(:realty_dossier) { create(:realty_dossier) }
    # Assuming a SaleListing factory that can be associated with a RealtyDossier
    let!(:sale_listing) do
      # Ensure realty_dossier has a primary_realty_asset
      primary_asset = realty_dossier.primary_realty_asset || create(:realty_asset, realty_dossier: realty_dossier, is_primary: true)
      realty_dossier.reload # ensure association is loaded
      create(:sale_listing, realty_asset: primary_asset)
    end

    before do
      # Ensure the dossier is associated with the sale listing correctly
      # The controller uses `realty_dossier.primary_sale_listing` which might be
      # `realty_dossier.primary_realty_asset.default_sale_listing`.
      # This setup ensures that the sale_listing created is set as the default_sale_listing
      # on the primary_realty_asset of the realty_dossier.
      unless realty_dossier.primary_realty_asset
        create(:realty_asset, realty_dossier: realty_dossier, is_primary: true)
        realty_dossier.reload
      end
      realty_dossier.primary_realty_asset.update!(default_sale_listing: sale_listing)
      realty_dossier.reload # To ensure primary_sale_listing is up-to-date
    end

    context 'when authentication is handled by Scoot' do
      let!(:scoot) { create(:scoot, scoot_subdomain: 'testsubdomain', access_token: 'valid_token') }

      before do
        # Configure host to include subdomain for Scoot to be found by request.subdomain
        host! "#{scoot.scoot_subdomain}.example.com"
        # Add the dossier to the scoot's accessible dossiers
        # Based on controller logic `scoot.has_access_to_dossier?(dossier_id: @realty_dossier.id)`
        # which relies on `scoot.safe_dossier_ids`
        scoot.update!(safe_dossier_ids: [realty_dossier.id])
      end

      it 'returns a 200 OK status and the dossier details for a valid request' do
        get "/api_public/v4/dossiers/#{realty_dossier.uuid}", headers: { 'X-User-Access-Code' => scoot.access_token }

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['uuid']).to eq(realty_dossier.uuid)
        # Assuming the jbuilder view for dossier includes primary_sale_listing details
        expect(json['primary_sale_listing']['uuid']).to eq(sale_listing.uuid)
      end

      it 'returns a 401 Unauthorized status for an invalid X-User-Access-Code' do
        get "/api_public/v4/dossiers/#{realty_dossier.uuid}", headers: { 'X-User-Access-Code' => 'invalid_token' }
        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns a 401 Unauthorized status if X-User-Access-Code is missing' do
        get "/api_public/v4/dossiers/#{realty_dossier.uuid}"
        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns a 401 Unauthorized status if scoot does not have access to the dossier' do
        other_dossier = create(:realty_dossier)
        # Ensure other_dossier also has a primary sale listing for the endpoint to proceed that far
        unless other_dossier.primary_realty_asset
          create(:realty_asset, realty_dossier: other_dossier, is_primary: true)
          other_dossier.reload
        end
        # Create a sale listing for the other_dossier
        other_primary_asset = other_dossier.primary_realty_asset
        create(:sale_listing, realty_asset: other_primary_asset)
        other_primary_asset.update!(default_sale_listing: other_primary_asset.sale_listings.first) # Link it as default
        other_dossier.reload


        get "/api_public/v4/dossiers/#{other_dossier.uuid}", headers: { 'X-User-Access-Code' => scoot.access_token }
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when the dossier or its primary listing does not exist' do
      before do
        # For these tests, no specific Scoot setup is needed, or assume default host
        host! "www.example.com" # Reset host if subdomain affects these tests
      end

      it 'returns a 404 Not Found status if the realty_dossier_uuid is invalid' do
        get "/api_public/v4/dossiers/invalid-uuid"
        expect(response).to have_http_status(:not_found)
        # json = JSON.parse(response.body)
        # expect(json['error']).to eq('Dossier not found')
      end

      it 'returns a 404 Not Found status if the dossier exists but has no primary sale listing' do
        dossier_without_listing = create(:realty_dossier)
        # Ensure it truly has no primary sale listing by ensuring its primary asset has no default sale listing
        primary_asset = dossier_without_listing.primary_realty_asset || create(:realty_asset, realty_dossier: dossier_without_listing, is_primary: true)
        dossier_without_listing.reload

        # Explicitly ensure no default_sale_listing
        primary_asset.update!(default_sale_listing: nil)
        # If there are other sale listings, they should not be considered primary unless set as default
        primary_asset.sale_listings.destroy_all # Clear any other potential listings

        dossier_without_listing.reload # reload to reflect changes


        get "/api_public/v4/dossiers/#{dossier_without_listing.uuid}"
        expect(response).to have_http_status(:not_found)
        # json = JSON.parse(response.body)
        # expect(json['error']).to eq('Listing not found') # Or similar based on controller
      end
    end
  end
end
