require 'rails_helper'

RSpec.describe 'Api::Mgmt::V4::RealtyGamesMgmt Pre-scraped Content', type: :request do
  let(:agency_tenant) { create(:agency_tenant) }
  let(:scoot) { create(:scoot, scoot_subdomain: 'test-subdomain') }
  
  let(:sample_scrape_item_data) do
    {
      'scrape_class' => 'ScrapeItemFromBuenavista',
      'scrapable_url' => 'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/12345',
      'scrape_unique_url' => 'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/12345#h2c',
      'full_content_before_js' => '{"property": {"Price": 250000, "Reference": "BVH12345"}}',
      'full_content_after_js' => nil,
      'title' => 'Test Property',
      'description' => 'A beautiful test property',
      'page_locale_code' => 'en',
      'is_valid_scrape' => true,
      'content_is_html' => false,
      'content_is_json' => true,
      'content_is_xml' => false,
      'all_page_images' => [],
      'script_json' => {},
      'scrape_is_buenavista' => true,
      'scrape_is_onthemarket' => false,
      'scrape_is_zoopla' => false,
      'scrape_is_rightmove' => false,
      'scrape_is_purplebricks' => false
    }
  end

  before do
    ActsAsTenant.current_tenant = agency_tenant
  end

  describe 'POST /api_mgmt/v4/realty_games_mgmt/init_game_with_pre_scraped_listing' do
    let(:valid_params) do
      {
        vendor_name: 'buenavista',
        scoot_subdomain: 'test-subdomain',
        retrieval_portal: 'buenavista',
        retrieval_end_point: 'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/12345',
        realty_game_slug: 'test-game',
        scrape_item_data: sample_scrape_item_data
      }
    end

    context 'with valid parameters' do
      before do
        # Mock the RealtyGameListingCreator to avoid actual scraping/listing creation
        allow_any_instance_of(Creators::RealtyGameListingCreator)
          .to receive(:create_game_listing_from_pre_scraped_content)
          .and_return(double('RealtyGameListing', uuid: 'test-uuid'))
      end

      it 'creates a realty game successfully' do
        post '/api_mgmt/v4/realty_games_mgmt/init_game_with_pre_scraped_listing',
             params: valid_params.to_json,
             headers: { 'Content-Type' => 'application/json' }

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('realty_game_id')
        expect(json_response['realty_game_id']).to be_present
      end

      it 'creates the scoot and realty game with correct attributes' do
        post '/api_mgmt/v4/realty_games_mgmt/init_game_with_pre_scraped_listing',
             params: valid_params.to_json,
             headers: { 'Content-Type' => 'application/json' }

        expect(response).to have_http_status(:ok)
        
        created_scoot = Scoot.find_by(scoot_subdomain: 'test-subdomain')
        expect(created_scoot).to be_present
        expect(created_scoot.supports_multiple_games).to be true
        expect(created_scoot.should_show_out_links).to be true

        created_game = created_scoot.realty_games.find_by(realty_game_slug: 'test-game')
        expect(created_game).to be_present
        expect(created_game.game_source_portal).to eq('buenavista')
        expect(created_game.game_title).to eq('Regular Game')
      end
    end

    context 'with missing parameters' do
      it 'returns error when scrape_item_data is missing' do
        invalid_params = valid_params.except(:scrape_item_data)
        
        post '/api_mgmt/v4/realty_games_mgmt/init_game_with_pre_scraped_listing',
             params: invalid_params.to_json,
             headers: { 'Content-Type' => 'application/json' }

        expect(response).to have_http_status(:internal_server_error)
        
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('error')
      end
    end
  end

  describe 'POST /api_mgmt/v4/realty_games_mgmt/add_pre_scraped_listing_to_game' do
    let(:realty_game) { create(:realty_game, scoot: scoot) }
    
    let(:valid_params) do
      {
        realty_game_id: realty_game.id,
        retrieval_portal: 'buenavista',
        retrieval_end_point: 'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/67890',
        scrape_item_data: sample_scrape_item_data
      }
    end

    context 'with valid parameters' do
      before do
        # Mock the RealtyGameListingCreator to avoid actual scraping/listing creation
        allow_any_instance_of(Creators::RealtyGameListingCreator)
          .to receive(:create_game_listing_from_pre_scraped_content)
          .and_return(double('RealtyGameListing', uuid: 'test-uuid'))
      end

      it 'adds a pre-scraped listing to existing game successfully' do
        post '/api_mgmt/v4/realty_games_mgmt/add_pre_scraped_listing_to_game',
             params: valid_params.to_json,
             headers: { 'Content-Type' => 'application/json' }

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq('Pre-scraped listing added')
      end
    end

    context 'with invalid game ID' do
      it 'returns error when game is not found' do
        invalid_params = valid_params.merge(realty_game_id: 99999)
        
        post '/api_mgmt/v4/realty_games_mgmt/add_pre_scraped_listing_to_game',
             params: invalid_params.to_json,
             headers: { 'Content-Type' => 'application/json' }

        expect(response).to have_http_status(:internal_server_error)
        
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('error')
      end
    end
  end
end
