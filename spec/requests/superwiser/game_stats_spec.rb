require 'rails_helper'

RSpec.describe "Superwiser::GameStats", type: :request do
  # Include Devise test helpers
  include Devise::Test::IntegrationHelpers

  let(:admin_user) do
    # Assuming a factory for a user that can be configured as an admin_admin
    # Adjust if your User model and factory are different
    create(:user, özellikleri: { admin_access_level: "admin_admin" }) # Example: using a jsonb field for roles
    # Or, if you have a specific trait or boolean field:
    # create(:user, :admin_admin)
    # create(:user, is_admin_admin: true)
  end

  # Create some initial data
  let!(:game_session1) { create(:game_session, total_score: 100, site_visitor_token: 'token1', session_guest_name: 'Player A', created_at: 2.days.ago) }
  let!(:game_session2) { create(:game_session, total_score: 200, site_visitor_token: 'token2', session_guest_name: 'Player B', created_at: 1.day.ago) }
  let!(:game_session3) { create(:game_session, total_score: 150, site_visitor_token: 'token1', session_guest_name: nil, created_at: Time.current) }

  let!(:guessed_price1) { create(:guessed_price, percentage_above_or_below: 10, guessed_price_amount_cents: 50000, guessed_price_currency: "USD") }
  let!(:guessed_price2) { create(:guessed_price, percentage_above_or_below: -5, guessed_price_amount_cents: 75000, guessed_price_currency: "USD") }
  let!(:guessed_price3) { create(:guessed_price, percentage_above_or_below: 0, guessed_price_amount_cents: 50000, guessed_price_currency: "USD") }


  before do
    # Sign in the admin user before each request
    # Need to mock is_admin_admin? if not directly on user model
    # For simplicity, assuming user model has `is_admin_admin?` or similar that `authenticate_admin_admin` uses
    # If Pundit or another authorization gem is used, its own test helpers might be needed.
    # The provided User factory setup might need adjustment based on actual User model.
    # A common way for `is_admin_admin?` might be:
    # allow(admin_user).to receive(:is_admin_admin?).and_return(true)
    # However, `authenticate_admin_admin` checks `current_user.is_admin_admin?`
    # So the user instance itself should respond to `is_admin_admin?` correctly.
    # Let's assume the factory `create(:user, özellikleri: { admin_access_level: "admin_admin" })`
    # implies the user object will have `is_admin_admin?` return true via some mechanism in the User model.
    # If not, this will need adjustment. For now, we proceed assuming it works.
    sign_in admin_user
  end

  describe "GET /superwiser/game_stats/overview" do
    it "renders the overview page successfully" do
      get superwiser_game_stats_overview_path
      expect(response).to have_http_status(:ok)
      expect(response.body).to include("Game Statistics Overview")

      # Check for instance variable assignment indirectly via what's rendered or via assigns()
      # For request specs, assigns() is available.
      expect(assigns(:total_games_played)).to eq(3)
      expect(assigns(:average_score)).to be_present
      expect(assigns(:unique_players_by_token)).to eq(2) # token1, token2
      expect(assigns(:unique_players_by_name)).to eq(2) # Player A, Player B
      expect(assigns(:game_sessions_over_time)).to be_present
    end
  end

  describe "GET /superwiser/game_stats/player_activity" do
    it "renders the player_activity page successfully" do
      get superwiser_game_stats_player_activity_path
      expect(response).to have_http_status(:ok)
      expect(response.body).to include("Player Activity Statistics")

      expect(assigns(:player_stats)).to be_present
      expect(assigns(:score_distribution)).to be_present
      # Example check on player_stats content
      # player_A_stats = assigns(:player_stats).find { |p| p.player_identifier == "Player A" }
      # expect(player_A_stats.games_played).to eq(1)
    end
  end

  describe "GET /superwiser/game_stats/guess_analysis" do
    it "renders the guess_analysis page successfully" do
      get superwiser_game_stats_guess_analysis_path
      expect(response).to have_http_status(:ok)
      expect(response.body).to include("Guess Analysis Statistics")

      expect(assigns(:total_guesses)).to eq(3)
      expect(assigns(:average_accuracy)).to be_present
      expect(assigns(:guess_distribution_by_percentage)).to be_present
      expect(assigns(:top_guessed_amounts)).to be_present
      # Example check on top_guessed_amounts
      # expect(assigns(:top_guessed_amounts).first[0]).to eq([50000, "USD"]) # [[amount_cents, currency], count]
      # expect(assigns(:top_guessed_amounts).first[1]).to eq(2) # count
    end
  end
end
