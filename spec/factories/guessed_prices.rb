# == Schema Information
#
# Table name: guessed_prices
#
#  id                                 :bigint           not null, primary key
#  agency_tenant_uuid                 :uuid
#  discarded_at                       :datetime
#  estimate_title                     :string
#  estimator_name                     :string
#  extra_uuid                         :uuid
#  game_session_string                :string
#  game_session_uuid                  :uuid
#  guessed_price_amount_cents         :bigint           default(0), not null
#  guessed_price_currency             :string           default("GBP"), not null
#  guessed_price_details              :jsonb
#  guessed_price_flags                :integer          default(0), not null
#  guessed_price_in_ui_currency_cents :bigint           default(0), not null
#  is_ai_estimate                     :boolean          default(FALSE), not null
#  is_protected                       :boolean          default(FALSE), not null
#  listing_uuid                       :uuid
#  notes_on_guess                     :text
#  percentage_above_or_below          :integer          default(0), not null
#  price_at_time_of_estimate_cents    :bigint           default(0), not null
#  realty_game_listing_uuid           :uuid
#  realty_game_uuid                   :uuid
#  score_for_guess                    :integer          default(0), not null
#  source_currency                    :string           default("GBP"), not null
#  ui_currency                        :string           default("GBP"), not null
#  user_uuid                          :uuid
#  uuid                               :uuid             not null
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  game_session_id                    :string
#
# Indexes
#
#  index_guessed_prices_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_guessed_prices_on_discarded_at              (discarded_at)
#  index_guessed_prices_on_guessed_price_flags       (guessed_price_flags)
#  index_guessed_prices_on_realty_game_listing_uuid  (realty_game_listing_uuid)
#  index_guessed_prices_on_realty_game_uuid          (realty_game_uuid)
#  index_guessed_prices_on_user_uuid                 (user_uuid)
#  index_guessed_prices_on_uuid                      (uuid) UNIQUE
#
FactoryBot.define do
  factory :guessed_price do
    
  end
end
