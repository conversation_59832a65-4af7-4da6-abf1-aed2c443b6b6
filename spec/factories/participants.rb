# == Schema Information
#
# Table name: participants
#
#  id                       :bigint           not null, primary key
#  average_session_duration :decimal(10, 2)
#  behavior_patterns        :jsonb
#  engagement_metrics       :jsonb
#  first_browser            :string
#  first_city               :string
#  first_country            :string
#  first_device_type        :string
#  first_landing_page       :text
#  first_os                 :string
#  first_referrer           :text
#  first_utm_campaign       :string
#  first_utm_medium         :string
#  first_utm_source         :string
#  first_visit_at           :datetime
#  last_visit_at            :datetime
#  returning_visitor        :boolean          default(FALSE)
#  total_events             :integer          default(0)
#  total_page_views         :integer          default(0)
#  total_visits             :integer          default(0)
#  unique_pages_visited     :integer          default(0)
#  visitor_token            :string           not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
# Indexes
#
#  index_participants_on_behavior_patterns   (behavior_patterns) USING gin
#  index_participants_on_engagement_metrics  (engagement_metrics) USING gin
#  index_participants_on_first_visit_at      (first_visit_at)
#  index_participants_on_last_visit_at       (last_visit_at)
#  index_participants_on_returning_visitor   (returning_visitor)
#  index_participants_on_total_visits        (total_visits)
#  index_participants_on_visitor_token       (visitor_token) UNIQUE
#
FactoryBot.define do
  factory :participant do
    visitor_token { SecureRandom.uuid }
    first_visit_at { 30.days.ago }
    last_visit_at { 1.day.ago }
    total_visits { 3 }
    total_events { 8 }
    total_page_views { 5 }
    average_session_duration { 2.5 }
    unique_pages_visited { 4 }
    returning_visitor { false }
    first_referrer { 'https://google.com' }
    first_landing_page { '/home' }
    first_utm_source { 'google' }
    first_utm_medium { 'organic' }
    first_country { 'United States' }
    first_city { 'New York' }
    first_device_type { 'Desktop' }
    first_browser { 'Chrome' }
    first_os { 'Windows' }

    trait :returning do
      returning_visitor { true }
      total_visits { 8 }
      total_events { 20 }
    end

    trait :high_engagement do
      total_visits { 15 }
      total_events { 45 }
      unique_pages_visited { 12 }
      returning_visitor { true }
    end

    trait :explorer do
      unique_pages_visited { 20 }
      total_visits { 8 }
      total_events { 15 }
    end

    trait :engaged do
      total_events { 30 }
      total_visits { 6 }
      unique_pages_visited { 8 }
    end

    trait :casual do
      total_visits { 2 }
      total_events { 3 }
      unique_pages_visited { 2 }
      returning_visitor { false }
    end

    trait :inactive do
      total_visits { 0 }
      total_events { 0 }
      unique_pages_visited { 0 }
      first_visit_at { nil }
      last_visit_at { nil }
    end

    trait :mobile do
      first_device_type { 'Mobile' }
      first_browser { 'Safari' }
      first_os { 'iOS' }
    end

    trait :from_social do
      first_referrer { 'https://facebook.com' }
      first_utm_source { 'facebook' }
      first_utm_medium { 'social' }
    end

    trait :from_email do
      first_referrer { nil }
      first_utm_source { 'newsletter' }
      first_utm_medium { 'email' }
    end

    trait :international do
      first_country { 'United Kingdom' }
      first_city { 'London' }
    end
  end
end
