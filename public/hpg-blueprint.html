<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HousePriceGuess: The Interactive Strategy Blueprint</title>
    <!-- Chosen Palette: Warm Neutral & Deep Teal -->
    <!-- Application Structure Plan: The SPA is designed as a narrative journey, guiding the user from "The Opportunity" to "The Execution." This thematic structure, rather than mirroring the report's chapters, creates a more intuitive and persuasive user flow. It starts by establishing the market gap, presents the core strategies (gamification, community), details the business model, outlines the execution roadmap, and concludes with the global vision. Key interactions include interactive charts to replace static tables (making data comparison easier), clickable cards to reveal detailed information on demand (reducing initial cognitive load), and a visual, interactive timeline for the roadmap (making the plan easy to digest). This structure was chosen to transform a dense report into an engaging, story-driven experience that builds a case for the business step-by-step. -->
    <!-- Visualization & Content Choices: 
        - Report Info: Zillow/Rightmove dominance & "enthusiast" audience statistics. -> Goal: Inform & establish market opportunity. -> Viz: Dynamic stat cards with animated numbers. -> Interaction: None, purely for impact. -> Justification: High-impact visuals to immediately grab attention and state the core premise. -> Method: HTML/CSS/JS.
        - Report Info: Table 2.1 (Gamification ROI). -> Goal: Compare & prove gamification's value. -> Viz: Interactive Bar Chart. -> Interaction: Hover on bars to see details (company, specific ROI). -> Justification: A bar chart is superior to a table for comparing magnitudes of impact quickly. Interactivity provides depth without clutter. -> Library: Chart.js.
        - Report Info: Community Flywheel concept. -> Goal: Organize & explain a process. -> Viz: Custom CSS/HTML diagram. -> Interaction: Hover over stages to reveal descriptive text. -> Justification: A visual, circular diagram effectively communicates the self-sustaining nature of the flywheel, which is hard to grasp from text alone. -> Method: HTML/CSS/JS.
        - Report Info: Table 4.1 (Monetization Suitability Matrix). -> Goal: Compare & organize complex options. -> Viz: Interactive Cards/Grid. -> Interaction: Click on a monetization stream to expand a card with details. -> Justification: Allows users to explore complex options at their own pace, focusing on one at a time, which is more digestible than a dense matrix. -> Method: HTML/CSS/JS.
        - Report Info: Table 5.1 (International Market Scorecard). -> Goal: Compare & inform strategic choice. -> Viz: Interactive Radar Chart. -> Interaction: Hover over datasets to highlight a country's profile. -> Justification: A radar chart is excellent for comparing multiple entities across multiple variables, making it easy to see which markets are "strongest" overall. -> Library: Chart.js.
        - CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #FDFBF7; /* Warm Neutral */
            color: #374151;
        }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
        .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }
        .nav-link.active, .nav-link:hover {
            color: #0d9488; /* Teal Accent */
        }
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #0d9488;
            transition: width 0.3s ease;
        }
        .nav-link.active::after, .nav-link:hover::after {
            width: 100%;
        }
        .stat-card h3 {
            color: #0f766e; /* Darker Teal for stats */
        }
        .section-title {
            color: #115e59; /* Darkest Teal for titles */
        }
        .flywheel-item {
            transition: transform 0.3s ease, background-color 0.3s ease;
        }
        .flywheel-item:hover {
            transform: scale(1.05);
            background-color: #ccfbf1;
        }
        .roadmap-item::before {
            content: '';
            position: absolute;
            top: 1rem;
            left: -1.375rem;
            width: 1.25rem;
            height: 1.25rem;
            border-radius: 50%;
            background-color: #FDFBF7;
            border: 4px solid #14b8a6; /* Teal */
        }
        .chart-container {
            position: relative;
            margin: auto;
            height: 40vh;
            width: 100%;
            max-width: 600px;
            max-height: 400px;
        }
    </style>
</head>
<body class="antialiased">

    <!-- Header & Navigation -->
    <header id="header" class="bg-white/80 backdrop-blur-lg sticky top-0 z-50 shadow-sm">
        <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex-shrink-0">
                    <h1 class="text-xl font-bold text-teal-800">HousePriceGuess Blueprint</h1>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#opportunity" class="nav-link px-3 py-2 rounded-md text-sm font-semibold text-gray-600">The Opportunity</a>
                        <a href="#strategy" class="nav-link px-3 py-2 rounded-md text-sm font-semibold text-gray-600">The Strategy</a>
                        <a href="#monetization" class="nav-link px-3 py-2 rounded-md text-sm font-semibold text-gray-600">Business Model</a>
                        <a href="#roadmap" class="nav-link px-3 py-2 rounded-md text-sm font-semibold text-gray-600">Roadmap</a>
                        <a href="#global" class="nav-link px-3 py-2 rounded-md text-sm font-semibold text-gray-600">Global Vision</a>
                    </div>
                </div>
                 <div class="md:hidden">
                    <button id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-teal-700 hover:bg-teal-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                        <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <div id="mobile-menu" class="hidden md:hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                    <a href="#opportunity" class="nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-600">The Opportunity</a>
                    <a href="#strategy" class="nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-600">The Strategy</a>
                    <a href="#monetization" class="nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-600">Business Model</a>
                    <a href="#roadmap" class="nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-600">Roadmap</a>
                    <a href="#global" class="nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-600">Global Vision</a>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">

        <!-- Hero Section -->
        <section class="text-center py-12 md:py-20">
            <h2 class="text-3xl md:text-5xl font-bold tracking-tight section-title">The Eyeball Economy</h2>
            <p class="mt-4 max-w-3xl mx-auto text-lg md:text-xl text-gray-600">
                Transforming casual property Browse into a highly engaged, monetizable audience. This is the strategic blueprint for HousePriceGuess.
            </p>
        </section>

        <!-- Section 1: The Opportunity -->
        <section id="opportunity" class="py-12 md:py-20 scroll-mt-20">
            <div class="text-center">
                <h2 class="text-3xl font-bold tracking-tight section-title sm:text-4xl">A Market Primed for Disruption</h2>
                <p class="mt-4 max-w-2xl mx-auto text-lg text-gray-600">The digital real estate landscape has evolved. It's no longer just a tool for buyers; it's a mainstream form of entertainment. This shift has created a massive, underserved audience.</p>
            </div>
            
            <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div class="stat-card bg-white p-6 rounded-xl shadow-lg">
                    <h3 class="text-5xl font-bold" data-target="96">0</h3>
                    <p class="mt-2 text-base font-medium text-gray-500">% of home buyers use online search, creating a digital-first environment.</p>
                </div>
                <div class="stat-card bg-white p-6 rounded-xl shadow-lg">
                    <h3 class="text-5xl font-bold" data-target="80">0</h3>
                    <p class="mt-2 text-base font-medium text-gray-500">% of Zillow's audience are "Enthusiasts" Browse for fun, not to buy.</p>
                </div>
                <div class="stat-card bg-white p-6 rounded-xl shadow-lg">
                     <h3 class="text-5xl font-bold" data-target="86.7">0</h3>
                    <p class="mt-2 text-base font-medium text-gray-500">Million unique monthly visitors on Zillow, proving the massive scale of attention.</p>
                </div>
            </div>

            <div class="mt-16">
                 <h3 class="text-2xl font-bold text-center section-title">The Competitive Landscape & The Gap</h3>
                 <p class="mt-2 max-w-2xl mx-auto text-center text-gray-600">Major portals focus on active buyers, leaving the massive "enthusiast" audience's needs for engagement and entertainment unmet. This is the strategic opening for HousePriceGuess.</p>
                <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h4 class="font-bold text-lg text-teal-700">Zillow</h4>
                        <p class="mt-2 text-sm text-gray-600">Dominates with AI valuation ("Zestimate") and 3D tours but only lightly touches gamification. Their core business is agent advertising.</p>
                    </div>
                     <div class="bg-teal-600 text-white p-6 rounded-lg shadow-xl ring-4 ring-teal-200">
                        <h4 class="font-bold text-lg">The Gap: HousePriceGuess</h4>
                        <p class="mt-2 text-sm">Builds a platform where deep, game-first engagement is the core value proposition, specifically designed for the 80% enthusiast majority.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h4 class="font-bold text-lg text-teal-700">Rightmove / Redfin</h4>
                        <p class="mt-2 text-sm text-gray-600">Focus on property data (alerts, school info) or a tech-enabled brokerage model. Engagement is a means to a transaction, not the end product.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: The Strategy -->
        <section id="strategy" class="py-12 md:py-20 scroll-mt-20">
             <div class="text-center">
                <h2 class="text-3xl font-bold tracking-tight section-title sm:text-4xl">The Two-Pillar Strategy</h2>
                <p class="mt-4 max-w-2xl mx-auto text-lg text-gray-600">Success hinges on a dual approach: leveraging the proven power of gamification to drive engagement and building a thriving community to create a self-sustaining growth engine.</p>
            </div>
            
            <div class="mt-16">
                 <h3 class="text-2xl font-bold text-center section-title">Pillar 1: Gamification's Proven ROI</h3>
                 <p class="mt-2 max-w-2xl mx-auto text-center text-gray-600">Gamification isn't just for fun; it's a powerful business driver with quantifiable results across industries. By applying these mechanics, we turn passive Browse into active participation.</p>
                <div class="mt-8 chart-container">
                    <canvas id="gamificationRoiChart"></canvas>
                </div>
            </div>

            <div class="mt-16">
                <h3 class="text-2xl font-bold text-center section-title">Pillar 2: The Community Flywheel</h3>
                <p class="mt-2 max-w-2xl mx-auto text-center text-gray-600">A loyal community is the ultimate competitive moat. Inspired by platforms like BiggerPockets and Twitch, we will create a self-perpetuating cycle of organic growth.</p>
                <div class="mt-12 max-w-4xl mx-auto relative">
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="w-48 h-48 md:w-64 md:h-64 border-4 border-dashed border-teal-300 rounded-full animate-spin-slow"></div>
                    </div>
                    <div class="grid grid-cols-2 gap-4 md:gap-8">
                        <div id="flywheel-attract" class="flywheel-item bg-white p-4 rounded-lg shadow-lg text-center cursor-pointer">
                            <h4 class="font-bold text-teal-700">1. Attract</h4>
                            <p class="text-sm text-gray-600 hidden md:block">Draw in users with the core game & viral content.</p>
                        </div>
                        <div id="flywheel-engage" class="flywheel-item bg-white p-4 rounded-lg shadow-lg text-center cursor-pointer">
                            <h4 class="font-bold text-teal-700">2. Engage</h4>
                            <p class="text-sm text-gray-600 hidden md:block">Hook users with leaderboards, challenges & rewards.</p>
                        </div>
                        <div id="flywheel-amplify" class="flywheel-item bg-white p-4 rounded-lg shadow-lg text-center cursor-pointer col-start-2">
                            <h4 class="font-bold text-teal-700">4. Amplify</h4>
                            <p class="text-sm text-gray-600 hidden md:block">User-generated content becomes authentic marketing.</p>
                        </div>
                        <div id="flywheel-nurture" class="flywheel-item bg-white p-4 rounded-lg shadow-lg text-center cursor-pointer row-start-2">
                            <h4 class="font-bold text-teal-700">3. Nurture</h4>
                            <p class="text-sm text-gray-600 hidden md:block">Foster connection in forums & dedicated communities.</p>
                        </div>
                    </div>
                     <div id="flywheel-tooltip" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-teal-800 text-white p-4 rounded-lg shadow-2xl text-center w-40 md:w-56 pointer-events-none">
                        <h4 id="flywheel-tooltip-title" class="font-bold">The Flywheel</h4>
                        <p id="flywheel-tooltip-text" class="text-sm mt-1">Hover over a stage to learn more.</p>
                    </div>
                </div>
            </div>
        </section>


        <!-- Section 3: The Business Model -->
        <section id="monetization" class="py-12 md:py-20 scroll-mt-20">
            <div class="text-center">
                <h2 class="text-3xl font-bold tracking-tight section-title sm:text-4xl">Monetizing Attention</h2>
                <p class="mt-4 max-w-2xl mx-auto text-lg text-gray-600">A multi-pronged revenue strategy is key to capturing value from all user segments, from casual players to institutional clients. Click each stream to learn more.</p>
            </div>
            <div id="monetization-grid" class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            </div>
        </section>

        <!-- Section 4: The Roadmap -->
        <section id="roadmap" class="py-12 md:py-20 scroll-mt-20">
            <div class="text-center">
                <h2 class="text-3xl font-bold tracking-tight section-title sm:text-4xl">The Path to Scale</h2>
                <p class="mt-4 max-w-2xl mx-auto text-lg text-gray-600">A phased approach ensures focus on the right priorities at the right time, from building a solid product to achieving global scale.</p>
            </div>
            <div class="mt-16 max-w-3xl mx-auto">
                <div class="relative pl-10 border-l-4 border-teal-200">
                    <!-- Near-Term -->
                    <div class="mb-12">
                        <h3 class="text-2xl font-bold text-teal-700">Near-Term (0-6 Months)</h3>
                        <p class="text-gray-600">Focus: Build a great product and lay the foundation.</p>
                        <div class="mt-4 space-y-3">
                            <div class="roadmap-item relative pl-4"><strong>Perfect the Core Game Loop:</strong> Make guessing fun, fast, and addictive.</div>
                            <div class="roadmap-item relative pl-4"><strong>Secure Data Feeds:</strong> Partner for a reliable MLS data stream via RESO Web API.</div>
                            <div class="roadmap-item relative pl-4"><strong>Launch Foundational Community Features:</strong> Leaderboards, profiles, and social sharing from day one.</div>
                        </div>
                    </div>
                    <!-- Mid-Term -->
                    <div class="mb-12">
                        <h3 class="text-2xl font-bold text-teal-700">Mid-Term (6-18 Months)</h3>
                        <p class="text-gray-600">Focus: User acquisition, community growth, and initial monetization.</p>
                        <div class="mt-4 space-y-3">
                            <div class="roadmap-item relative pl-4"><strong>Launch the "Community Flywheel":</strong> Hire a community manager and create dedicated spaces (Discord/Subreddit).</div>
                            <div class="roadmap-item relative pl-4"><strong>Execute Viral Content Strategy:</strong> Systematically produce short-form video content.</div>
                            <div class="roadmap-item relative pl-4"><strong>Implement Initial Monetization:</strong> Roll out advertising and simple in-app purchases.</div>
                        </div>
                    </div>
                    <!-- Long-Term -->
                    <div>
                        <h3 class="text-2xl font-bold text-teal-700">Long-Term (18+ Months)</h3>
                        <p class="text-gray-600">Focus: Full monetization, B2B offerings, and global expansion.</p>
                        <div class="mt-4 space-y-3">
                            <div class="roadmap-item relative pl-4"><strong>Launch Premium Subscriptions:</strong> Roll out power-user tiers with advanced data and analytics.</div>
                            <div class="roadmap-item relative pl-4"><strong>Develop B2B Offerings:</strong> Build the Data-as-a-Service API and the White-Label "Gamification-as-a-Service" solution.</div>
                            <div class="roadmap-item relative pl-4"><strong>Execute International Expansion:</strong> Launch in the first prioritized international market (e.g., UK or Canada).</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 5: Global Vision -->
        <section id="global" class="py-12 md:py-20 scroll-mt-20">
             <div class="text-center">
                <h2 class="text-3xl font-bold tracking-tight section-title sm:text-4xl">Global Expansion Opportunities</h2>
                <p class="mt-4 max-w-2xl mx-auto text-lg text-gray-600">The model is built for scale. After proving success in a pilot market, we will expand to key international markets with a strong "property obsession."</p>
            </div>
             <div class="mt-8 chart-container">
                <canvas id="globalMarketChart"></canvas>
            </div>
        </section>

    </main>

    <footer class="bg-white border-t border-gray-200">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6 text-center text-gray-500">
            <p>&copy; 2024 HousePriceGuess Strategic Blueprint. An interactive summary based on the provided research report.</p>
        </div>
    </footer>

<script>
document.addEventListener('DOMContentLoaded', () => {

    // Mobile Menu Toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
        mobileMenuButton.querySelector('svg:first-child').classList.toggle('hidden');
        mobileMenuButton.querySelector('svg:last-child').classList.toggle('hidden');
    });

    // Animate stats on scroll
    const statCards = document.querySelectorAll('.stat-card h3');
    const animateStat = (element) => {
        const target = +element.getAttribute('data-target');
        const duration = 1500;
        let start = 0;
        const stepTime = 20;
        const steps = duration / stepTime;
        const increment = target / steps;
        
        const timer = setInterval(() => {
            start += increment;
            if (start >= target) {
                clearInterval(timer);
                start = target;
            }
            if (target % 1 !== 0) {
                 element.innerText = start.toFixed(1);
            } else {
                 element.innerText = Math.floor(start);
            }
        }, stepTime);
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateStat(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    statCards.forEach(card => {
        observer.observe(card);
    });

    // Navigation Active State
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('section');

    const activateNavLink = () => {
        let currentSectionId = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            if (pageYOffset >= sectionTop - 80) { // 80px offset for sticky header
                currentSectionId = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${currentSectionId}`) {
                link.classList.add('active');
            }
        });
    };
    window.addEventListener('scroll', activateNavLink);
    activateNavLink();

    // Chart.js: Gamification ROI Chart
    const gamificationData = {
        labels: ['Customer Acquisition', 'Engagement Lift', 'Retention Boost', 'Revenue Increase (Autodesk)', 'Time on Site (Verizon)'],
        datasets: [{
            label: 'Proven ROI of Gamification (%)',
            data: [700, 48, 22, 29, 30],
            backgroundColor: [
                'rgba(13, 148, 136, 0.6)',
                'rgba(15, 118, 110, 0.6)',
                'rgba(17, 94, 89, 0.6)',
                'rgba(19, 78, 74, 0.6)',
                'rgba(20, 68, 65, 0.6)',
            ],
            borderColor: [
                'rgba(13, 148, 136, 1)',
                'rgba(15, 118, 110, 1)',
                'rgba(17, 94, 89, 1)',
                'rgba(19, 78, 74, 1)',
                'rgba(20, 68, 65, 1)',
            ],
            borderWidth: 1
        }]
    };
    const gamificationConfig = {
        type: 'bar',
        data: gamificationData,
        options: {
            maintainAspectRatio: false,
            responsive: true,
            indexAxis: 'y',
            plugins: {
                legend: { display: false },
                title: { display: true, text: 'Gamification Impact Across Industries', color: '#115e59' },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label = 'Impact: ';
                            }
                            if (context.parsed.x !== null) {
                                label += `+${context.parsed.x}%`;
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: { display: true, text: 'Percentage Increase (%)', color: '#115e59' },
                    ticks: { color: '#374151' }
                },
                y: { ticks: { color: '#374151', autoSkip: false } }
            }
        }
    };
    const gamificationRoiChartCtx = document.getElementById('gamificationRoiChart').getContext('2d');
    new Chart(gamificationRoiChartCtx, gamificationConfig);

    // Flywheel Interaction
    const flywheelItems = [
        { id: 'flywheel-attract', title: '1. Attract', text: 'Draw new users into the ecosystem with the fun of the core game and viral social media content.' },
        { id: 'flywheel-engage', title: '2. Engage', text: 'Keep users returning daily with leaderboards, challenges, and a constant stream of new properties.' },
        { id: 'flywheel-nurture', title: '3. Nurture', text: 'Create dedicated community spaces (forums, Discord) to facilitate focused discussions and connection.' },
        { id: 'flywheel-amplify', title: '4. Amplify', text: 'Repurpose user-generated content as authentic, compelling marketing that attracts more new users.' }
    ];
    const tooltipTitle = document.getElementById('flywheel-tooltip-title');
    const tooltipText = document.getElementById('flywheel-tooltip-text');
    flywheelItems.forEach(itemData => {
        const itemElement = document.getElementById(itemData.id);
        itemElement.addEventListener('mouseover', () => {
            tooltipTitle.textContent = itemData.title;
            tooltipText.textContent = itemData.text;
        });
        itemElement.addEventListener('mouseout', () => {
            tooltipTitle.textContent = 'The Flywheel';
            tooltipText.textContent = 'Hover over a stage to learn more.';
        });
    });
    
    // Monetization Grid
    const monetizationData = [
        { title: 'Advertising', target: 'Casual Enthusiast (Free User Base)', potential: 'Medium', ux_impact: 'Medium-High', complexity: 'Low-Medium', color: 'bg-teal-100', text_color: 'text-teal-800' },
        { title: 'In-App Purchases', target: 'Engaged Gamer', potential: 'Medium-High', ux_impact: 'Low-Medium', complexity: 'Medium', color: 'bg-teal-200', text_color: 'text-teal-900' },
        { title: 'Premium Subscription', target: 'Power User / Aspiring Pro', potential: 'High', ux_impact: 'Low (Opt-in)', complexity: 'High', color: 'bg-teal-400', text_color: 'text-teal-900' },
        { title: 'Data API (DaaS)', target: 'B2B Enterprise / Institutional', potential: 'Very High', ux_impact: 'None (External)', complexity: 'Very High', color: 'bg-teal-600', text_color: 'text-white' }
    ];
    const monetizationGrid = document.getElementById('monetization-grid');
    monetizationData.forEach(item => {
        const card = document.createElement('div');
        card.className = `p-6 rounded-lg shadow-lg cursor-pointer transition-transform transform hover:scale-105 ${item.color} ${item.text_color}`;
        card.innerHTML = `
            <h4 class="text-xl font-bold">${item.title}</h4>
            <div class="details hidden mt-4 space-y-2 text-sm">
                <p><strong>Target:</strong> ${item.target}</p>
                <p><strong>Revenue Potential:</strong> <span class="font-semibold">${item.potential}</span></p>
                <p><strong>UX Impact:</strong> <span class="font-semibold">${item.ux_impact}</span></p>
                <p><strong>Complexity:</strong> <span class="font-semibold">${item.complexity}</span></p>
            </div>
        `;
        monetizationGrid.appendChild(card);
        card.addEventListener('click', () => {
            card.querySelector('.details').classList.toggle('hidden');
        });
    });

    // Global Market Chart
    const globalMarketData = {
        labels: ['Market Size', 'Digital Maturity', 'Data Accessibility', 'Cultural Fit', 'Low Competition'],
        datasets: [
            { label: 'USA', data: [4, 5, 4, 5, 2], fill: true, backgroundColor: 'rgba(13, 148, 136, 0.2)', borderColor: 'rgb(13, 148, 136)', pointBackgroundColor: 'rgb(13, 148, 136)', pointBorderColor: '#fff', pointHoverBackgroundColor: '#fff', pointHoverBorderColor: 'rgb(13, 148, 136)'},
            { label: 'UK', data: [4, 5, 3, 5, 2], fill: true, backgroundColor: 'rgba(255, 159, 64, 0.2)', borderColor: 'rgb(255, 159, 64)', pointBackgroundColor: 'rgb(255, 159, 64)', pointBorderColor: '#fff', pointHoverBackgroundColor: '#fff', pointHoverBorderColor: 'rgb(255, 159, 64)' },
            { label: 'Canada', data: [3, 4, 4, 4, 3], fill: true, backgroundColor: 'rgba(75, 192, 192, 0.2)', borderColor: 'rgb(75, 192, 192)', pointBackgroundColor: 'rgb(75, 192, 192)', pointBorderColor: '#fff', pointHoverBackgroundColor: '#fff', pointHoverBorderColor: 'rgb(75, 192, 192)' },
            { label: 'Australia', data: [4, 4, 4, 5, 3], fill: true, backgroundColor: 'rgba(153, 102, 255, 0.2)', borderColor: 'rgb(153, 102, 255)', pointBackgroundColor: 'rgb(153, 102, 255)', pointBorderColor: '#fff', pointHoverBackgroundColor: '#fff', pointHoverBorderColor: 'rgb(153, 102, 255)' }
        ]
    };
    const globalMarketConfig = {
        type: 'radar',
        data: globalMarketData,
        options: {
            maintainAspectRatio: false,
            responsive: true,
            plugins: {
                title: { display: true, text: 'International Market Opportunity Scorecard', color: '#115e59' },
                legend: { position: 'bottom', labels: { color: '#374151' } }
            },
            scales: {
                r: {
                    angleLines: { color: 'rgba(0,0,0,0.1)' },
                    grid: { color: 'rgba(0,0,0,0.1)' },
                    pointLabels: { font: { size: 12 }, color: '#115e59' },
                    ticks: { backdropColor: '#FDFBF7', color: '#374151' },
                    suggestedMin: 0,
                    suggestedMax: 5,
                }
            }
        }
    };
    const globalMarketChartCtx = document.getElementById('globalMarketChart').getContext('2d');
    new Chart(globalMarketChartCtx, globalMarketConfig);
});
</script>
</body>
</html>
