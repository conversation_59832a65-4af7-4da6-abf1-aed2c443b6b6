<!DOCTYPE html>
<html>
<head>
  <title>Custom Analytics Dashboard</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.3/dist/chart.umd.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.min.js"></script>
  <style>
    body { font-family: sans-serif; margin: 20px; }
    .container { max-width: 1200px; margin: 0 auto; }
    .chart-container { margin-bottom: 40px; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Custom Analytics Dashboard</h1>

    <div class="chart-container">
      <h2>Activity Over Time</h2>
      <canvas id="activity-chart" style="height: 300px;"></canvas>
    </div>

    <div class="chart-container">
      <h2>Top Participants by Average Session Duration</h2>
      <table id="top-participants-table">
        <thead>
          <tr>
            <th>Visitor Token</th>
            <th>Avg. Session Duration (min)</th>
            <th>Total Visits</th>
            <th>Total Page Views</th>
            <th>Behavior Category</th>
          </tr>
        </thead>
        <tbody>
        </tbody>
      </table>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      fetch('/custom_analytics/data')
        .then(response => response.json())
        .then(data => {
          // Activity Over Time Chart
          const visitsData = Object.entries(data.activity_over_time.visits).map(([date, count]) => ({ x: new Date(date), y: count }));
          const eventsData = Object.entries(data.activity_over_time.events).map(([date, count]) => ({ x: new Date(date), y: count }));

          const ctx = document.getElementById('activity-chart').getContext('2d');
          new Chart(ctx, {
            type: 'line',
            data: {
              datasets: [
                {
                  label: 'Visits',
                  data: visitsData,
                  borderColor: 'rgb(75, 192, 192)',
                  tension: 0.1
                },
                {
                  label: 'Events',
                  data: eventsData,
                  borderColor: 'rgb(255, 99, 132)',
                  tension: 0.1
                }
              ]
            },
            options: {
              scales: {
                x: {
                  type: 'time',
                  time: {
                    unit: 'day'
                  },
                  title: {
                    display: true,
                    text: 'Date'
                  }
                },
                y: {
                  title: {
                    display: true,
                    text: 'Count'
                  }
                }
              }
            }
          });

          // Top Participants Table
          const tableBody = document.querySelector('#top-participants-table tbody');
          data.top_participants_by_duration.forEach(participant => {
            const row = tableBody.insertRow();
            row.insertCell().textContent = participant.visitor_token;
            row.insertCell().textContent = participant.average_session_duration ? participant.average_session_duration.toFixed(2) : 'N/A';
            row.insertCell().textContent = participant.total_visits;
            row.insertCell().textContent = participant.total_page_views;
            row.insertCell().textContent = participant.behavior_category;
          });
        })
        .catch(error => console.error('Error fetching analytics data:', error));
    });
  </script>
</body>
</html>
