class ParticipantSyncJob < ApplicationJob
  queue_as :default

  # Retry configuration
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(batch_size: 1000, visitor_token: nil)
    Rails.logger.info "Starting participant sync job with batch_size: #{batch_size}"
    
    if visitor_token.present?
      sync_single_participant(visitor_token)
    else
      sync_all_participants(batch_size)
    end
    
    Rails.logger.info "Participant sync job completed successfully"
  end

  private

  def sync_single_participant(visitor_token)
    Rails.logger.info "Syncing single participant: #{visitor_token}"
    
    visits = Ahoy::Visit.where(visitor_token: visitor_token).includes(:events)
    return if visits.empty?

    participant = Participant.find_or_initialize_by(visitor_token: visitor_token)
    
    visits.each do |visit|
      participant.sync_with_visit(visit)
    end
    
    participant.save!
    Rails.logger.info "Successfully synced participant: #{visitor_token}"
  end

  def sync_all_participants(batch_size)
    total_visits = Ahoy::Visit.where.not(visitor_token: nil).count
    processed = 0
    errors = 0
    
    Rails.logger.info "Syncing #{total_visits} visits in batches of #{batch_size}"
    
    Ahoy::Visit.where.not(visitor_token: nil)
               .includes(:events)
               .find_in_batches(batch_size: batch_size) do |visits_batch|
      
      begin
        sync_batch(visits_batch)
        processed += visits_batch.size
        
        # Log progress every 10 batches
        if (processed / batch_size) % 10 == 0
          Rails.logger.info "Processed #{processed}/#{total_visits} visits"
        end
        
      rescue StandardError => e
        errors += visits_batch.size
        Rails.logger.error "Error processing batch: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        
        # Try to process visits individually in case of batch error
        visits_batch.each do |visit|
          begin
            sync_single_visit(visit)
          rescue StandardError => individual_error
            Rails.logger.error "Error processing individual visit #{visit.id}: #{individual_error.message}"
          end
        end
      end
    end
    
    Rails.logger.info "Sync completed. Processed: #{processed}, Errors: #{errors}"
    
    # Update analytics cache after sync
    clear_analytics_cache
  end

  def sync_batch(visits_batch)
    # Group visits by visitor_token for efficient processing
    visits_by_token = visits_batch.group_by(&:visitor_token)
    
    visits_by_token.each do |visitor_token, visits|
      next if visitor_token.blank?
      
      participant = Participant.find_or_initialize_by(visitor_token: visitor_token)
      
      visits.each do |visit|
        participant.sync_with_visit(visit)
      end
      
      participant.save!
    end
  end

  def sync_single_visit(visit)
    return if visit.visitor_token.blank?
    
    participant = Participant.find_or_initialize_by(visitor_token: visit.visitor_token)
    participant.sync_with_visit(visit)
    participant.save!
  end

  def clear_analytics_cache
    Rails.logger.info "Clearing analytics cache"
    
    # Clear all participant analytics cache keys
    cache_patterns = [
      'participant_overview_*',
      'participants_over_time_*',
      'visit_distribution_*',
      'engagement_scores_*',
      'behavior_categories_*',
      'device_types_*',
      'traffic_sources_*',
      'geographic_distribution_*',
      'visit_frequency_*',
      'session_duration_*',
      'top_participants_*',
      'cohort_analysis_*'
    ]
    
    cache_patterns.each do |pattern|
      Rails.cache.delete_matched(pattern)
    end
  end
end
