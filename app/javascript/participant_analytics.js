// Participant Analytics JavaScript Module
import "chartkick"
import "Chart.bundle"

class ParticipantAnalytics {
  constructor() {
    this.charts = new Map();
    this.initializeEventListeners();
  }

  initializeEventListeners() {
    document.addEventListener('DOMContentLoaded', () => {
      this.initializeCharts();
      this.setupDateRangeHandlers();
      this.setupRefreshHandlers();
    });
  }

  initializeCharts() {
    // Find all chart containers and initialize them
    const chartContainers = document.querySelectorAll('[id$="-chart"]');
    chartContainers.forEach(container => {
      const chartId = container.id;
      const endpoint = container.dataset.endpoint;
      const chartType = container.dataset.chartType;
      
      if (endpoint && chartType) {
        this.loadChart(chartId, endpoint, chartType);
      }
    });
  }

  async loadChart(elementId, dataUrl, chartType) {
    try {
      const response = await fetch(dataUrl);
      const data = await response.json();
      
      const element = document.getElementById(elementId);
      if (element && data.chart_data) {
        const chart = this.createChart(element, data.chart_data, chartType, data.title);
        this.charts.set(elementId, chart);
      }
    } catch (error) {
      console.error(`Error loading chart ${elementId}:`, error);
      this.showChartError(elementId);
    }
  }

  createChart(element, data, chartType, title) {
    const baseOptions = {
      height: '400px',
      library: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!title,
            text: title,
            font: {
              size: 16,
              weight: 'bold'
            }
          },
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    };

    switch (chartType) {
      case 'line':
        return new Chartkick.LineChart(element, data, {
          ...baseOptions,
          colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
          library: {
            ...baseOptions.library,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });

      case 'pie':
        return new Chartkick.PieChart(element, data, {
          ...baseOptions,
          colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#6B7280']
        });

      case 'bar':
        return new Chartkick.BarChart(element, data, {
          ...baseOptions,
          colors: ['#10B981'],
          library: {
            ...baseOptions.library,
            scales: {
              x: {
                beginAtZero: true
              }
            }
          }
        });

      case 'column':
        return new Chartkick.ColumnChart(element, data, {
          ...baseOptions,
          colors: ['#8B5CF6'],
          library: {
            ...baseOptions.library,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });

      case 'area':
        return new Chartkick.AreaChart(element, data, {
          ...baseOptions,
          colors: ['#3B82F6']
        });

      default:
        console.warn(`Unknown chart type: ${chartType}`);
        return null;
    }
  }

  showChartError(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.innerHTML = `
        <div class="flex items-center justify-center h-full text-gray-500">
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Chart Error</h3>
            <p class="mt-1 text-sm text-gray-500">Unable to load chart data</p>
          </div>
        </div>
      `;
    }
  }

  setupDateRangeHandlers() {
    const dateForm = document.querySelector('form[action*="participant_analytics"]');
    if (dateForm) {
      const startDateInput = dateForm.querySelector('input[name="start_date"]');
      const endDateInput = dateForm.querySelector('input[name="end_date"]');
      
      if (startDateInput && endDateInput) {
        [startDateInput, endDateInput].forEach(input => {
          input.addEventListener('change', () => {
            this.validateDateRange(startDateInput, endDateInput);
          });
        });
      }
    }
  }

  validateDateRange(startInput, endInput) {
    const startDate = new Date(startInput.value);
    const endDate = new Date(endInput.value);
    
    if (startDate > endDate) {
      endInput.setCustomValidity('End date must be after start date');
      endInput.reportValidity();
    } else {
      endInput.setCustomValidity('');
    }
  }

  setupRefreshHandlers() {
    // Add refresh buttons to charts
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
      this.addRefreshButton(container);
    });
  }

  addRefreshButton(container) {
    const header = container.querySelector('h3');
    if (header && !header.querySelector('.refresh-btn')) {
      const refreshBtn = document.createElement('button');
      refreshBtn.className = 'refresh-btn ml-2 text-gray-400 hover:text-gray-600 transition-colors';
      refreshBtn.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      `;
      
      refreshBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.refreshChart(container);
      });
      
      header.appendChild(refreshBtn);
    }
  }

  async refreshChart(container) {
    const chartElement = container.querySelector('[id$="-chart"]');
    if (chartElement) {
      const refreshBtn = container.querySelector('.refresh-btn');
      if (refreshBtn) {
        refreshBtn.classList.add('animate-spin');
      }
      
      const endpoint = chartElement.dataset.endpoint;
      const chartType = chartElement.dataset.chartType;
      
      if (endpoint && chartType) {
        await this.loadChart(chartElement.id, endpoint, chartType);
      }
      
      if (refreshBtn) {
        refreshBtn.classList.remove('animate-spin');
      }
    }
  }

  // Utility method to format numbers
  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  // Utility method to format percentages
  formatPercentage(value, total) {
    if (total === 0) return '0%';
    return ((value / total) * 100).toFixed(1) + '%';
  }

  // Export data functionality
  exportChartData(chartId, format = 'csv') {
    const chart = this.charts.get(chartId);
    if (!chart) {
      console.error(`Chart ${chartId} not found`);
      return;
    }

    // This would need to be implemented based on the specific chart library
    console.log(`Exporting ${chartId} as ${format}`);
  }
}

// Initialize the analytics dashboard
const participantAnalytics = new ParticipantAnalytics();

// Export for global access if needed
window.ParticipantAnalytics = participantAnalytics;

// Global chart initialization function for backward compatibility
window.initializeChart = function(elementId, dataUrl, chartType) {
  participantAnalytics.loadChart(elementId, dataUrl, chartType);
};

export default participantAnalytics;
