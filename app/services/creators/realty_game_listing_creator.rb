# frozen_string_literal: true

module Creators
  # RealtyGameListingCreator is responsible for creating RealtyGameListing records
  # from property listing URLs. It scrapes data from various real estate portals,
  # creates the necessary listing records, and associates them with a RealtyGame.
  #
  # Usage:
  #   creator = Creators::RealtyGameListingCreator.new
  #   game_listing = creator.create_game_listing_from_url(
  #     realty_game_uuid,
  #     'https://www.zoopla.co.uk/for-sale/details/12345678/'
  #   )
  #
  # Or using the convenience method on RealtyGame:
  #   realty_game = RealtyGame.find_by(uuid: game_uuid)
  #   game_listing = realty_game.add_listing_from_url('https://www.rightmove.co.uk/properties/123456789')
  #
  class RealtyGameListingCreator
    PORTAL_CONFIG = {
      'purplebricks' => {
        scrape_class: 'ScrapeItemFromPurplebricks',
        connector: 'ScraperConnectors::Purplebricks',
        method: :find_or_create_for_h2c_purplebricks,
        include_trailing_slash: false
      },
      'onthemarket' => {
        scrape_class: 'ScrapeItemFromOtm',
        connector: 'ScraperConnectors::Regular',
        method: :find_or_create_for_h2c_onthemarket,
        include_trailing_slash: true
      },
      'zoopla' => {
        scrape_class: 'ScrapeItem',
        connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c,
        include_trailing_slash: false
      },
      'rightmove' => {
        scrape_class: 'ScrapeItemFromRightmove',
        connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_rightmove,
        include_trailing_slash: false
      },
      'buenavista' => {
        scrape_class: 'ScrapeItemFromBuenavista',
        connector: 'ScraperConnectors::Json',
        method: :find_or_create_for_h2c_buenavista,
        include_trailing_slash: false
      }
    }.freeze

    # Portal hostname patterns for automatic detection
    PORTAL_HOSTNAMES = {
      'zoopla' => %w[zoopla.co.uk www.zoopla.co.uk],
      'rightmove' => %w[rightmove.co.uk www.rightmove.co.uk],
      'onthemarket' => %w[onthemarket.com www.onthemarket.com],
      'purplebricks' => %w[purplebricks.co.uk www.purplebricks.co.uk],
      'buenavista' => %w[buenavistahomes.eu www.buenavistahomes.eu]
    }.freeze

    MINIMUM_SCRAPE_LENGTH = 1000

    class UnknownPortalError < StandardError; end
    class RealtyGameNotFoundError < StandardError; end
    class InvalidUrlError < StandardError; end

    # Creates a RealtyGameListing from a URL and associates it with an existing RealtyGame.
    # @param realty_game_uuid [String] The UUID of the existing RealtyGame
    # @param url [String] The source URL to scrape for the listing
    # @param portal [String, nil] The portal identifier (optional - will be auto-detected if nil)
    # @return [RealtyGameListing] The created realty game listing
    # @raise [UnknownPortalError] If the portal cannot be detected or is not recognized
    # @raise [RealtyGameNotFoundError] If the realty game UUID doesn't correspond to an existing game
    # @raise [InvalidUrlError] If the URL is invalid
    def create_game_listing_from_url(realty_game_uuid, url, portal = nil)
      Rails.logger.info("Creating RealtyGameListing from URL: #{url} for game: #{realty_game_uuid}")

      # Auto-detect portal if not provided
      portal ||= detect_portal_from_url(url)

      validate_inputs(realty_game_uuid, url, portal)
      realty_game = fetch_realty_game(realty_game_uuid)

      # Check if listing already exists in this game
      exstg_listing_by_url = find_existing_game_listing_by_url(realty_game, url)
      return exstg_listing_by_url if exstg_listing_by_url

      scrape_item = scrape_content(url, portal)
      validate_scrape_result(scrape_item, portal)

      listing = create_listing_from_scrape_item(scrape_item, portal)

      listing.realty_asset.set_bvh_geojson if listing.realty_asset_uuid

      game_listing = create_and_associate_game_listing(realty_game, listing, url, portal)

      Rails.logger.info("Successfully created RealtyGameListing: #{game_listing.uuid}")
      game_listing
    rescue StandardError => e
      Rails.logger.error("Failed to create game listing from #{portal || 'unknown'} URL for game #{realty_game_uuid}: #{e.message}")
      raise
    end

    # 5 july 2025 - think I'll stop using below
    def create_game_listing_from_pre_scraped_content(realty_game_uuid, url, portal, scrape_item_data)
      Rails.logger.info("Creating RealtyGameListing from pre-scraped content: #{url} for game: #{realty_game_uuid}")

      validate_inputs(realty_game_uuid, url, portal)
      realty_game = fetch_realty_game(realty_game_uuid)

      # Check if listing already exists in this game
      existing_listing = find_existing_game_listing_by_url(realty_game, url)
      return existing_listing if existing_listing

      # Create a scrape item from the pre-scraped data
      scrape_item = create_scrape_item_from_data(scrape_item_data)

      listing = create_listing_from_scrape_item(scrape_item, portal)

      listing.realty_asset.set_bvh_geojson if listing.realty_asset_uuid

      game_listing = create_and_associate_game_listing(realty_game, listing, url, portal)

      Rails.logger.info("Successfully created RealtyGameListing from pre-scraped content: #{game_listing.uuid}")
      game_listing
    rescue StandardError => e
      Rails.logger.error("Failed to create game listing from pre-scraped content for game #{realty_game_uuid}: #{e.message}")
      raise
    end

    def create_game_listing_from_existing_listing(realty_game_uuid, listing_for_game)
      Rails.logger.info("Creating RealtyGameListing from existing listing: #{listing_for_game.import_url} for game: #{realty_game_uuid}")

      realty_game = fetch_realty_game(realty_game_uuid)

      # Check if listing already exists in this game
      existing_game_listing = find_s_listing_in_game_by_listing_uuid(realty_game, listing_for_game.uuid)
      return existing_game_listing if existing_game_listing

      portal = 'does_the_portal_matter'
      game_listing = create_and_associate_game_listing(realty_game, listing_for_game, listing_for_game.import_url, portal)

      Rails.logger.info("Successfully created RealtyGameListing from existing listing: #{game_listing.uuid}")
      game_listing
    rescue StandardError => e
      Rails.logger.error("Failed to create game listing from existing listing for game #{realty_game_uuid}: #{e.message}")
      raise
    end

    private

    # Detects the portal from a URL based on hostname patterns
    # @param url [String] The URL to analyze
    # @return [String] The detected portal name
    # @raise [UnknownPortalError] If the portal cannot be detected
    def detect_portal_from_url(url)
      uri = URI.parse(url)
      hostname = uri.hostname&.downcase

      PORTAL_HOSTNAMES.each do |portal, hostnames|
        return portal if hostnames.include?(hostname)
      end

      raise UnknownPortalError, "Cannot detect portal from URL: #{url}. Hostname: #{hostname}"
    rescue URI::InvalidURIError
      raise InvalidUrlError, "Invalid URL format: #{url}"
    end

    # Validates input parameters
    # @param realty_game_uuid [String] The UUID to validate
    # @param url [String] The URL to validate
    # @param portal [String] The portal to validate
    def validate_inputs(realty_game_uuid, url, portal)
      raise ArgumentError, 'Invalid realty game UUID format' unless realty_game_uuid =~ /\A[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\z/

      raise InvalidUrlError, 'Invalid URL format' unless url =~ URI::DEFAULT_PARSER.make_regexp
      raise UnknownPortalError, "Unknown portal: #{portal}" unless PORTAL_CONFIG.key?(portal)
    end

    # Fetches the RealtyGame by UUID
    # @param realty_game_uuid [String] The UUID of the realty game
    # @return [RealtyGame] The found realty game
    # @raise [RealtyGameNotFoundError] If the game is not found
    def fetch_realty_game(realty_game_uuid)
      realty_game = RealtyGame.find_by(uuid: realty_game_uuid)
      raise RealtyGameNotFoundError, "No realty game found with UUID: #{realty_game_uuid}" unless realty_game

      realty_game
    end

    # Checks if a listing from this URL already exists in the game
    # @param realty_game [RealtyGame] The realty game to check
    # @param url [String] The URL to check for
    # @return [RealtyGameListing, nil] Existing game listing or nil
    def find_existing_game_listing_by_url(realty_game, url)
      # Try to find by the URL in realty_game_listing_details
      realty_game.realty_game_listings.kept.find do |game_listing|
        details = game_listing.realty_game_listing_details || {}
        details['source_url'] == url
      end
    end

    # Checks if a listing with the given listing_uuid already exists in the game
    # @param realty_game [RealtyGame] The realty game to check
    # @param listing_uuid [String] The listing UUID to check for
    # @return [RealtyGameListing, nil] Existing game listing or nil
    def find_s_listing_in_game_by_listing_uuid(realty_game, listing_uuid)
      realty_game.realty_game_listings.kept.find_by(listing_uuid: listing_uuid)
    end

    # Scrapes content from the URL using the appropriate portal configuration
    # @param url [String] The URL to scrape
    # @param portal [String] The portal identifier
    # @return [ScrapeItem] The scrape item with content
    def scrape_content(url, portal)
      p_config = PORTAL_CONFIG[portal]
      scrape_class = p_config[:scrape_class].constantize
      scrape_item = scrape_class.send(p_config[:method], url)
      scrape_item.retrieve_and_set_content_object(
        p_config[:connector],
        include_trailing_slash: p_config[:include_trailing_slash],
        force_retrieval: false
      )
      scrape_item
    end

    # Validates that the scraped content is sufficient
    # @param content [String] The scraped content to validate
    # @raise [StandardError] If content is insufficient
    def validate_scrape_result(scrape_item, portal)
      content_to_validate = if portal == 'rightmove'
                              puts "validating script_json for scrape_item no : #{scrape_item.id}"
                              scrape_item.script_json.to_s
                            else
                              puts "validating full_content_before_js for scrape_item no : #{scrape_item.id}"
                              scrape_item.full_content_before_js
                            end

      puts "content_to_validate.length: #{content_to_validate.length}"
      puts "MINIMUM_SCRAPE_LENGTH: #{MINIMUM_SCRAPE_LENGTH}"
      return if content_to_validate && content_to_validate.length > MINIMUM_SCRAPE_LENGTH

      Rails.logger.debug { "scrape_result: #{content_to_validate}" }
      raise 'Scrape result unavailable or suspiciously short'
    end

    # Creates a listing (SaleListing or RentalListing) from the scraped content
    # @param scrape_item [ScrapeItem] The scrape item containing the raw data
    # @param portal [String] The portal identifier
    # @return [SaleListing, RentalListing] The created listing
    def create_listing_from_scrape_item(scrape_item, portal)
      if portal == 'zoopla'
        RealtyParsers::ParseListingViaLlm.new.inferred_listing_from_scrape_item(scrape_item)
      else
        scrape_item.sale_listing_from_scrape_item
      end
    end

    # Creates a scrape item instance from pre-scraped data
    # @param scrape_item_data [Hash] The serialized scrape item data
    # @return [ScrapeItem] A scrape item instance populated with the data
    def create_scrape_item_from_data(scrape_item_data)
      scrape_class = scrape_item_data['scrape_class'].constantize

      # Create a new instance without saving to database
      scrape_item = scrape_class.new(
        scrapable_url: scrape_item_data['scrapable_url'],
        scrape_unique_url: scrape_item_data['scrape_unique_url'],
        full_content_before_js: scrape_item_data['full_content_before_js'],
        full_content_after_js: scrape_item_data['full_content_after_js'],
        title: scrape_item_data['title'],
        description: scrape_item_data['description'],
        page_locale_code: scrape_item_data['page_locale_code'],
        is_valid_scrape: scrape_item_data['is_valid_scrape'],
        content_is_html: scrape_item_data['content_is_html'],
        content_is_json: scrape_item_data['content_is_json'],
        content_is_xml: scrape_item_data['content_is_xml'],
        all_page_images: scrape_item_data['all_page_images'],
        script_json: scrape_item_data['script_json']
      )

      # Set portal-specific flags
      scrape_item.scrape_is_buenavista = scrape_item_data['scrape_is_buenavista'] if scrape_item.respond_to?(:scrape_is_buenavista=)
      scrape_item.scrape_is_onthemarket = scrape_item_data['scrape_is_onthemarket'] if scrape_item.respond_to?(:scrape_is_onthemarket=)
      scrape_item.scrape_is_zoopla = scrape_item_data['scrape_is_zoopla'] if scrape_item.respond_to?(:scrape_is_zoopla=)
      scrape_item.scrape_is_rightmove = scrape_item_data['scrape_is_rightmove'] if scrape_item.respond_to?(:scrape_is_rightmove=)
      scrape_item.scrape_is_purplebricks = scrape_item_data['scrape_is_purplebricks'] if scrape_item.respond_to?(:scrape_is_purplebricks=)

      # Save the scrape item to database so it can be used by the listing creation process
      scrape_item.save!
      scrape_item
    end

    # Creates and associates a RealtyGameListing with the RealtyGame
    # @param realty_game [RealtyGame] The realty game to associate with
    # @param listing [SaleListing, RentalListing] The listing to associate
    # @param source_url [String] The original source URL
    # @param portal [String] The portal identifier
    # @return [RealtyGameListing] The created game listing
    def create_and_associate_game_listing(realty_game, listing, source_url, portal)
      # Determine listing type
      is_sale_listing = listing.is_a?(SaleListing)
      is_rental_listing = listing.is_a?(RentalListing)

      RealtyGameListing.create!(
        uuid: SecureRandom.uuid,
        realty_game_uuid: realty_game.uuid,
        listing_uuid: listing.uuid,
        realty_asset_uuid: listing.realty_asset_uuid,
        scoot_uuid: realty_game.scoot_uuid,
        is_sale_listing: is_sale_listing,
        is_rental_listing: is_rental_listing,
        realty_game_listing_details: {
          source_url: source_url,
          portal: portal,
          created_at: Time.current,
          listing_type: is_sale_listing ? 'sale' : 'rental',
          scrape_method: 'automated'
        }
      )
    end
  end
end
