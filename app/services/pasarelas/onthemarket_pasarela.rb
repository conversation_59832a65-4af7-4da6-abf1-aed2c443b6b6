# frozen_string_literal: true

require 'nokogiri'

module Pasarelas
  class OnthemarketPasarela
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      # Try to get data from script_json first (for LocalPlaywright scraper)
      next_data = nil
      doc = nil

      if @realty_scraped_item.script_json.present?
        begin
          # Handle case where script_json is already parsed JSON data
          if @realty_scraped_item.script_json.is_a?(Hash)
            next_data = @realty_scraped_item.script_json
          elsif @realty_scraped_item.script_json.is_a?(String)
            # Try to evaluate as Ruby hash first (safer than string manipulation)
            begin
              next_data = eval(@realty_scraped_item.script_json)
            rescue => eval_error
              Rails.logger.error("Failed to eval script_json as Ruby hash: #{eval_error.message}")
              # Try to parse as JSON with basic Ruby-to-JSON conversion
              json_string = @realty_scraped_item.script_json
              # Convert Ruby hash format to JSON format
              json_string = json_string.gsub(/=>\s*/, ': ')
                                      .gsub(/\bnil\b/, 'null')
                                      .gsub(/\btrue\b/, 'true')
                                      .gsub(/\bfalse\b/, 'false')
              next_data = JSON.parse(json_string)
            end
          end
        rescue JSON::ParserError => e
          Rails.logger.error("Failed to parse script_json as JSON: #{e.message}")
          # Fall back to HTML parsing
        rescue => e
          Rails.logger.error("Failed to process script_json: #{e.message}")
          # Fall back to HTML parsing
        end
      end

      # Fall back to HTML parsing if script_json didn't work
      if next_data.nil? && @realty_scraped_item.full_content_before_js.present?
        html_content = @realty_scraped_item.full_content_before_js
        return unless html_content.length > 1000

        begin
          # Parse the HTML to find the __NEXT_DATA__ script
          doc = Nokogiri::HTML(html_content)
          next_data_script = doc.at('script#__NEXT_DATA__')
          return unless next_data_script

          # Parse the JSON content from the script
          json_content = next_data_script.text
          next_data = JSON.parse(json_content)
        rescue JSON::ParserError => e
          Rails.logger.error("Failed to parse OnTheMarket JSON data from HTML: #{e.message}")
          return
        rescue Exception => e
          Rails.logger.error("Error processing OnTheMarket HTML data: #{e.message}")
          return
        end
      end

      return unless next_data

      begin
        # Extract property data from OnTheMarket structure
        otm_property = if next_data.dig('props', 'initialReduxState', 'property')
                         next_data['props']['initialReduxState']['property']
                       else
                         # Fallback to old structure for backward compatibility
                         next_data['props']['pageProps']['property']
                       end

        return unless otm_property

        # Extract postal code from dataLayer if available (only if we have HTML doc)
        postal_code = doc ? extract_postal_code_from_datalayer(doc) : nil

        # Map the data to our schema
        listing_data = map_property_to_listing_schema(otm_property)
        asset_data = map_property_to_asset_schema(otm_property, postal_code)

        # Extract image URLs
        extracted_image_urls = extract_image_urls(otm_property, doc)

        # Save the extracted data
        @realty_scraped_item.extracted_asset_data = asset_data || {}
        @realty_scraped_item.extracted_listing_data = listing_data || {}
        @realty_scraped_item.extracted_image_urls = extracted_image_urls || []
        @realty_scraped_item.save!

      rescue Exception => e
        Rails.logger.error("Error processing OnTheMarket property data: #{e.message}")
      end
    end

    private

    def extract_postal_code_from_datalayer(doc)
      data_layer_script = doc.at('script#dataLayerContainer')
      if data_layer_script
        begin
          data_layer_json = JSON.parse(data_layer_script.text.scan(/window.dataLayer.push\((.*)\)/).flatten.first)
          return data_layer_json['postcode']
        rescue JSON::ParserError => e
          Rails.logger.error("Error parsing dataLayer JSON: #{e.message}")
        end
      end
      nil
    end

    def extract_image_urls(otm_property, doc)
      image_urls = []

      # Primary: Get images from JSON data if available
      if otm_property['images'] && otm_property['images'].is_a?(Array)
        image_urls = otm_property['images'].map { |img| img['largeUrl'] || img['url'] }.compact
      end

      # Fallback: CSS selectors for both old and new structures (only if we have HTML doc)
      if image_urls.empty? && doc
        image_urls = doc.css('div.swiper-slide picture img, .swiper-slide img').map do |img|
          img['src'] || img['data-src'] || img['srcset']&.split(',')&.first&.strip&.split(' ')&.first
        end.compact
      end

      image_urls
    end

    def map_property_to_asset_schema(property, postal_code = nil)
      {
        'title' => property['propertyTitle'],
        'categories' => property['features'] ? property['features'].map { |f| { 'id' => f['id'], 'name' => f['feature'] } } : [],
        'city' => property['addressLocality'],
        'city_search_key' => property['addressLocality']&.downcase&.gsub(/\s+/, '-') || '',
        'constructed_area' => 0.0, # Not directly available, default to 0.0
        'count_bathrooms' => property['bathrooms'].to_f || 0.0,
        'count_bedrooms' => property['bedrooms'] || 0,
        'count_garages' => property['description'].to_s.downcase.include?('garage') ? 1 : 0, # Infer from description
        'count_toilets' => 0, # Not available, default to 0
        'country' => property.dig('agent', 'ukCountry')&.capitalize,
        'description' => property['description'],
        'details' => property['rooms'] ? property['rooms']['descriptions'].map { |room| [room['name'], room] }.to_h : {},
        'discarded_at' => nil, # Not available, set to nil
        'energy_performance' => nil, # Not available, set to nil
        'energy_rating' => nil, # Not available, set to nil
        'floor' => nil, # Not directly available, set to nil
        'has_rental_listings' => false, # Default value
        'has_sale_listings' => true, # Set to true as this is a sale listing
        'has_sold_transactions' => false, # Default value
        'host_on_create' => 'unknown_host', # Default value
        'is_ai_generated_realty_asset' => false, # Default value
        'latitude' => property.dig('location', 'lat'),
        'longitude' => property.dig('location', 'lon'),
        'neighborhood' => nil, # Not directly available, set to nil
        'neighborhood_search_key' => '',
        'plot_area' => 0.0, # Not available, default to 0.0
        'postal_code' => postal_code || property['postal_code'],
        'prop_state_key' => 'new', # Assuming "new" based on newHomeFlag
        'prop_type_key' => property['humanisedPropertyType']&.downcase&.gsub(/\s+/, '-') || '',
        'province' => property.dig('metadata', 'dataLayer', 'parent-locations', 2), # Extract region if available
        'ra_photos_count' => property['images']&.size || 0,
        'realty_asset_flags' => 0, # Default value
        'realty_asset_tags' => property['propertyLabels'] || [],
        'reference' => property['id'],
        'region' => property.dig('metadata', 'dataLayer', 'parent-locations', 2),
        'rental_listings_count' => 0, # Default value
        'sale_listings_count' => 1, # Assuming this is the only sale listing for this property
        'sold_transactions_count' => 0, # Default value
        'street_address' => property['displayAddress'],
        'street_number' => nil, # Not directly available, set to nil
        'year_construction' => 0 # Not available, default to 0
      }
    end

    def map_property_to_listing_schema(property)
      {
        'title' => property['propertyTitle'],
        'description' => property['description'],
        'archived' => false, # Default value
        'commission_cents' => 0, # Default value
        'commission_currency' => 'GBP', # Default value
        'currency' => 'GBP', # Assuming all prices are in GBP if not specified otherwise
        'design_style' => nil, # Not available, set to nil
        'details_of_rooms' => property['rooms'] ? property['rooms']['descriptions'].map { |room| [room['name'], room] }.to_h : {},
        'discarded_at' => nil, # Not available, set to nil
        'extra_sale_details' => {},
        'furnished' => false, # Default value
        'hide_map' => false, # Default value
        'highlighted' => false, # Default value
        'host_on_create' => 'unknown_host', # Default value
        'is_ai_generated_listing' => false, # Default value
        'listing_pages_count' => 0, # Default value
        'listing_slug' => property['id'],
        'listing_tags' => [],
        'main_video_url' => property.dig('videotours', 0, 'url'),
        'obscure_map' => false, # Default value
        'page_section_listings_count' => 0, # Default value
        'position_in_list' => nil, # Not available, set to nil
        'price_sale_current_cents' => (property['priceRaw'] * 100).to_i, # Convert to cents
        'price_sale_current_currency' => 'GBP', # Assuming GBP as per schema default
        'price_sale_original_cents' => (property['priceRaw'] * 100).to_i, # Assuming original price is the same as current
        'price_sale_original_currency' => 'GBP', # Assuming GBP as per schema default
        'property_board_items_count' => 0, # Default value
        'publish_from' => nil, # Not available, set to nil
        'publish_till' => nil, # Not available, set to nil
        'reference' => property['id'],
        'related_urls' => {},
        'reserved' => false, # Default value
        'sale_listing_features' => property['features'] ? property['features'].map { |feature| [feature['id'], feature['feature']] }.to_h : {},
        'sale_listing_flags' => 0, # Default value
        'sale_listing_gen_prompt' => nil, # Not available, set to nil
        'service_charge_yearly_cents' => 0, # Default value
        'service_charge_yearly_currency' => 'GBP', # Default value
        'sl_photos_count' => property['images']&.size || 0,
        'visible' => true # Default value, assuming all listings are visible unless specified otherwise
      }
    end

    def asset_attributes
      %w[title categories city city_search_key constructed_area count_bathrooms count_bedrooms count_garages count_toilets
         country description details discarded_at energy_performance energy_rating floor has_rental_listings has_sale_listings has_sold_transactions host_on_create latitude longitude neighborhood neighborhood_search_key plot_area postal_code prop_origin_key prop_state_key prop_type_key province ra_photos_count realty_asset_flags realty_asset_tags reference region rental_listings_count sale_listings_count site_visitor_token sold_transactions_count street_address street_number year_construction]
    end

    def listing_attributes
      %w[
        title description archived commission_cents commission_currency currency design_style
        details_of_rooms discarded_at extra_sale_details furnished hide_map highlighted
        host_on_create is_ai_generated_listing listing_pages_count listing_slug listing_tags
        main_video_url obscure_map page_section_listings_count position_in_list
        price_sale_current_cents price_sale_current_currency price_sale_original_cents
        price_sale_original_currency property_board_items_count publish_from publish_till
        reference related_urls reserved sale_listing_features sale_listing_flags
        sale_listing_gen_prompt service_charge_yearly_cents service_charge_yearly_currency
        site_visitor_token sl_photos_count visible
      ]
    end
  end
end
