module Pasarelas
  class CapeverdepropertyPasarela
    def initialize(scrape_item)
      @scrape_item = scrape_item
    end

    def call
      # Extract data and set on the scrape item
      result = extract_listing_and_asset
      
      @scrape_item.extracted_listing_data = result[:listing_data] || {}
      @scrape_item.extracted_asset_data = result[:asset_data] || {}
      @scrape_item.extracted_image_urls = result[:images] || []
      
      # Save the changes
      @scrape_item.save!
    end

    def self.extract_listing_and_asset_from_scrape_item(scrape_item)
      pasarela = new(scrape_item)
      pasarela.extract_listing_and_asset
    end

    def extract_listing_and_asset
      # Validate scrape item has content
      unless @scrape_item.full_content_before_js&.length&.> 500
        raise 'Insufficient content in scrape item for Cape Verde Property parsing'
      end

      # Use the parser service to extract structured data
      property_data = RealtyParsers::ParseCapeverdepropertyListingsHtml.property_hash_from_html(
        @scrape_item.full_content_before_js
      )

      # Map to listing and asset schemas
      listing_data = map_to_listing_schema(property_data)
      asset_data = map_to_asset_schema(property_data)

      {
        listing_data: listing_data,
        asset_data: asset_data,
        images: property_data['images'] || [],
        raw_data: property_data
      }
    end

    private

    attr_reader :scrape_item

    def map_to_listing_schema(property_data)
      agent_info = property_data['agent_info'] || {}
      
      {
        'title' => property_data['title'] || '',
        'description' => property_data['description'] || '',
        'archived' => false,
        'commission_cents' => 0,
        'commission_currency' => 'EUR',
        'currency' => property_data['currency'] || 'EUR',
        'design_style' => nil,
        'details_of_rooms' => {},
        'discarded_at' => nil,
        'extra_sale_details' => {
          'agent_info' => agent_info,
          'features' => property_data['features'] || [],
          'furnished' => property_data['furnished'] || false,
          'sea_view' => property_data['sea_view'] || false,
          'source_website' => 'capeverdeproperty.co.uk'
        },
        'furnished' => property_data['furnished'] || false,
        'hide_map' => false,
        'highlighted' => false,
        'host_on_create' => 'capeverdeproperty_scraper',
        'is_ai_generated_listing' => false,
        'listing_pages_count' => 0,
        'listing_slug' => property_data['reference'] || generate_slug(property_data['title']),
        'listing_tags' => property_data['features'] || [],
        'main_video_url' => nil,
        'obscure_map' => false,
        'page_section_listings_count' => 0,
        'position_in_list' => nil,
        'price_sale_current_cents' => (property_data['price_raw'] || 0) * 100,
        'price_sale_current_currency' => 'EUR',
        'price_sale_original_cents' => (property_data['price_raw'] || 0) * 100,
        'price_sale_original_currency' => 'EUR',
        'property_board_items_count' => 0,
        'publish_from' => nil,
        'publish_till' => nil,
        'reference' => property_data['reference'] || generate_reference,
        'related_urls' => {},
        'reserved' => false,
        'sale_listing_features' => format_features_hash(property_data['features']),
        'sale_listing_flags' => 0,
        'sale_listing_gen_prompt' => nil,
        'service_charge_yearly_cents' => 0,
        'service_charge_yearly_currency' => 'EUR',
        'sl_photos_count' => property_data['image_count'] || 0,
        'visible' => true
      }
    end

    def map_to_asset_schema(property_data)
      location_info = {
        'city' => property_data['city'] || 'Unknown',
        'region' => property_data['region'] || 'Sal',
        'country' => property_data['country'] || 'Cape Verde',
        'latitude' => property_data['latitude'],
        'longitude' => property_data['longitude']
      }

      {
        'categories' => format_categories(property_data['features']),
        'city' => location_info['city'],
        'city_search_key' => location_info['city']&.downcase&.gsub(/\s+/, '-') || 'unknown',
        'constructed_area' => 0.0,
        'count_bathrooms' => property_data['bathrooms']&.to_f || 0.0,
        'count_bedrooms' => property_data['bedrooms'] || 0,
        'count_garages' => extract_garage_count(property_data),
        'count_toilets' => 0,
        'country' => location_info['country'],
        'description' => property_data['description'] || '',
        'details' => {
          'features' => property_data['features'] || [],
          'furnished' => property_data['furnished'] || false,
          'sea_view' => property_data['sea_view'] || false
        },
        'discarded_at' => nil,
        'energy_performance' => nil,
        'energy_rating' => nil,
        'floor' => nil,
        'has_rental_listings' => false,
        'has_sale_listings' => true,
        'has_sold_transactions' => false,
        'host_on_create' => 'capeverdeproperty_scraper',
        'is_ai_generated_realty_asset' => false,
        'latitude' => location_info['latitude'],
        'longitude' => location_info['longitude'],
        'neighborhood' => nil,
        'neighborhood_search_key' => '',
        'plot_area' => 0.0,
        'postal_code' => nil,
        'prop_state_key' => 'for_sale',
        'prop_type_key' => property_data['property_type']&.downcase&.gsub(/\s+/, '-') || 'property',
        'province' => location_info['region'],
        'ra_photos_count' => property_data['image_count'] || 0,
        'realty_asset_flags' => 0,
        'realty_asset_tags' => property_data['features'] || [],
        'reference' => property_data['reference'] || generate_reference,
        'region' => location_info['region'],
        'rental_listings_count' => 0,
        'sale_listings_count' => 1,
        'sold_transactions_count' => 0,
        'street_address' => property_data['title'] || '',
        'street_number' => nil,
        'title' => property_data['title'] || '',
        'year_construction' => 0
      }
    end

    def format_features_hash(features)
      return {} unless features.is_a?(Array)
      
      features.each_with_object({}) do |feature, hash|
        key = feature.parameterize.underscore
        hash[key] = feature
      end
    end

    def format_categories(features)
      return [] unless features.is_a?(Array)
      
      features.map { |feature| { 'name' => feature } }
    end

    def extract_garage_count(property_data)
      features = property_data['features'] || []
      description = property_data['description'] || ''
      
      # Check if garage is mentioned in features or description
      garage_mentioned = features.any? { |f| f.downcase.include?('garage') } ||
                        description.downcase.include?('garage')
      
      garage_mentioned ? 1 : 0
    end

    def generate_slug(title)
      return "cvp_#{SecureRandom.hex(4)}" unless title.present?
      
      title.parameterize[0..30]
    end

    def generate_reference
      "CVP#{Time.current.strftime('%Y%m%d')}#{SecureRandom.hex(3).upcase}"
    end
  end
end
