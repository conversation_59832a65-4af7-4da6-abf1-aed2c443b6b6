# frozen_string_literal: true

module Pasarelas
  class BuenavistaPasarela
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      # For Buenavista, we need to parse JSON content from full_content_before_js
      json_content = @realty_scraped_item.full_content_before_js
      return unless json_content.present? && !json_content.empty?

      begin
        # Parse the JSON content
        buenavista_json = JSON.parse(json_content)
      rescue JSON::ParserError => e
        Rails.logger.error("Failed to parse Buenavista JSON: #{e.message}")
        return
      end

      # Ensure 'property' key exists
      return unless buenavista_json['property']

      # Map the data to our schema
      listing_data = map_property_to_listing_schema(buenavista_json)
      asset_data = map_property_to_asset_schema(buenavista_json)

      # Extract image URLs
      extracted_image_urls = extract_image_urls(buenavista_json)

      # Save the extracted data
      @realty_scraped_item.extracted_asset_data = asset_data || {}
      @realty_scraped_item.extracted_listing_data = listing_data || {}
      @realty_scraped_item.extracted_image_urls = extracted_image_urls || []
      @realty_scraped_item.save!

    rescue Exception => e
      Rails.logger.error("Error processing Buenavista data: #{e.message}")
    end

    private

    def clean_string_to_float(value)
      value.to_s.gsub(/[^0-9.]/, '').to_f
    end

    def clean_string_to_int(value)
      value.to_s.gsub(/[^0-9]/, '').to_i
    end

    def extract_all_features(property_features_array)
      return [] unless property_features_array.is_a?(Array)

      property_features_array.flat_map { |feature_group| feature_group['Value'] }.compact.uniq
    end

    def extract_image_urls(buenavista_json)
      pictures = buenavista_json.dig('property', 'Pictures', 'Picture')
      if pictures.is_a?(Array)
        pictures.map { |img| img['PictureURL'] }.compact
      else
        []
      end
    end

    def map_property_to_asset_schema(buenavista_json)
      prop = buenavista_json['property'] # Main property data
      auto_title = buenavista_json['auto_title']
      auto_desc = buenavista_json.dig('auto_desc', 'description')

      # Extract all property features
      all_property_features = extract_all_features(prop.dig('PropertyFeatures', 'Category'))

      constructed_area_val = prop['Built'].is_a?(Numeric) ? prop['Built'].to_f : clean_string_to_float(prop['Built'])
      plot_area_val = prop['GardenPlot'].is_a?(Numeric) ? prop['GardenPlot'].to_f : clean_string_to_float(prop['GardenPlot'])

      year_built = 0
      if prop['BuiltYear'].to_s.match?(/\A\d{4}\z/)
        year_built = prop['BuiltYear'].to_i
      elsif prop['CompletionDate']
        begin
          # Ensure CompletionDate is not nil or empty before parsing
          year_built = Date.parse(prop['CompletionDate']).year if prop['CompletionDate'].present?
        rescue ArgumentError, TypeError
          # Keep 0 if parsing fails
        end
      end

      garage_count = if prop['Parking'].to_i > 0
                       prop['Parking'].to_i
                     else
                       ((auto_desc || prop['Description']).to_s.downcase.include?('garage') ? 1 : 0)
                     end

      {
        'title' => auto_title || "#{prop.dig('PropertyType', 'NameType')} in #{prop['Location']}",
        'categories' => all_property_features.map { |f| { 'id' => f.to_s.downcase.gsub(/\s+/, '-'), 'name' => f.to_s } },
        'city' => prop['Location'],
        'city_search_key' => prop['Location']&.downcase&.gsub(/\s+/, '-') || '',
        'constructed_area' => constructed_area_val,
        'count_bathrooms' => prop['Bathrooms'].to_f || 0.0,
        'count_bedrooms' => prop['Bedrooms'].to_i || 0,
        'count_garages' => garage_count,
        'count_toilets' => 0, # New schema doesn't specify toilets separately from bathrooms
        'country' => prop['Country'],
        'description' => auto_desc || prop['Description'],
        'details' => {}, # New schema doesn't have structured room data like the old one might have
        'discarded_at' => nil,
        'energy_performance' => nil, # Not directly available, could parse from EnergyRating.Image if needed
        'energy_rating' => prop.dig('EnergyRating', 'EnergyRated').present? && prop.dig('EnergyRating', 'EnergyRated') != '' ? 'Available' : nil,
        'floor' => nil, # Not a structured field in new schema (might be in description)
        'has_rental_listings' => false, # Assuming properties from this source are for sale
        'has_sale_listings' => true,
        'has_sold_transactions' => false,
        'host_on_create' => 'unknown_host', # Default
        'is_ai_generated_realty_asset' => false, # Default
        'latitude' => nil, # Not available in the new example JSON
        'longitude' => nil, # Not available in the new example JSON
        'neighborhood' => prop['SubLocation'].presence, # SubLocation seems like the best fit
        'neighborhood_search_key' => prop['SubLocation']&.downcase&.gsub(/\s+/, '-') || '',
        'plot_area' => plot_area_val,
        'postal_code' => nil, # Not available in the new example JSON
        'prop_state_key' => 'new', # Default or map from prop['Status']['system'] if applicable
        'prop_type_key' => prop.dig('PropertyType', 'NameType')&.downcase&.gsub(/\s+/, '-') || prop.dig('PropertyType', 'Type')&.downcase&.gsub(/\s+/, '-') || '',
        'province' => prop['Province'],
        'ra_photos_count' => prop.dig('Pictures', 'Picture')&.is_a?(Array) ? prop.dig('Pictures', 'Picture').size : 0,
        'realty_asset_flags' => 0, # Default
        'realty_asset_tags' => all_property_features, # Using all features as tags
        'reference' => prop['Reference'],
        'region' => prop['Area'], # e.g., "Costa del Sol"
        'rental_listings_count' => 0,
        'sale_listings_count' => 1, # Assuming one listing per property item
        'sold_transactions_count' => 0,
        'street_address' => prop['Location'], # Best guess, new schema lacks full street address
        'street_number' => nil,
        'year_construction' => year_built
      }.transform_values { |v| v.is_a?(String) ? v.strip : v } # Optional: strip whitespace from string values
    end

    def map_property_to_listing_schema(buenavista_json)
      prop = buenavista_json['property']
      auto_title = buenavista_json['auto_title']
      auto_desc = buenavista_json.dig('auto_desc', 'description')

      # Extract all property features
      property_feature_categories = prop.dig('PropertyFeatures', 'Category')
      all_property_features = extract_all_features(property_feature_categories)

      price_raw = prop['Price'].is_a?(Numeric) ? prop['Price'].to_f : clean_string_to_float(prop['Price'])
      original_price_raw = prop['OriginalPrice'].is_a?(Numeric) ? prop['OriginalPrice'].to_f : clean_string_to_float(prop['OriginalPrice'])

      service_charge_yearly_val = prop['Community_Fees_Year'] # e.g., "1,560"
      service_charge_cents = service_charge_yearly_val ? (clean_string_to_float(service_charge_yearly_val) * 100).to_i : 0

      currency = prop['Currency'] || 'EUR' # Default to EUR if not specified, though it should be

      is_furnished = false
      # Search within the 'Category' array for 'Furniture' type
      if property_feature_categories.is_a?(Array)
        furniture_feature_group = property_feature_categories.find { |f_group| f_group['Type'] == 'Furniture' }
        is_furnished = furniture_feature_group['Value'].any? { |v| v.to_s.downcase.include?('furnished') && !v.to_s.downcase.include?('unfurnished') } if furniture_feature_group && furniture_feature_group['Value'].is_a?(Array)
      end

      {
        'title' => auto_title || "#{prop.dig('PropertyType', 'NameType')} in #{prop['Location']}",
        'description' => auto_desc || prop['Description'],
        'archived' => prop.dig('Status', 'system')&.downcase != 'available', # Assuming 'Available' means not archived
        'commission_cents' => 0, # Default
        'commission_currency' => currency,
        'currency' => currency,
        'design_style' => nil,
        'details_of_rooms' => {}, # New schema doesn't have structured room data
        'discarded_at' => nil,
        'extra_sale_details' => {},
        'furnished' => is_furnished,
        'hide_map' => false, # Default
        'highlighted' => false, # No direct equivalent in new schema
        'host_on_create' => 'unknown_host', # Default
        'is_ai_generated_listing' => false, # Default (auto_desc might be AI, but this flag is for internal marking)
        'listing_pages_count' => 0,
        'listing_slug' => prop['Reference'], # Use the property reference as slug
        'listing_tags' => all_property_features,
        'main_video_url' => nil, # No direct equivalent for video URL in example
        'obscure_map' => false, # Default
        'page_section_listings_count' => 0,
        'position_in_list' => nil,
        'price_sale_current_cents' => (price_raw * 100).to_i,
        'price_sale_current_currency' => currency,
        'price_sale_original_cents' => (original_price_raw * 100).to_i,
        'price_sale_original_currency' => currency,
        'property_board_items_count' => 0,
        'publish_from' => nil,
        'publish_till' => nil,
        'reference' => prop['Reference'],
        'related_urls' => {},
        'reserved' => false, # No direct equivalent, assume false
        'sale_listing_features' => all_property_features.map { |feature| [feature.to_s.downcase.gsub(/\s+/, '-'), feature.to_s] }.to_h,
        'sale_listing_flags' => 0,
        'sale_listing_gen_prompt' => nil,
        'service_charge_yearly_cents' => service_charge_cents,
        'service_charge_yearly_currency' => currency,
        'sl_photos_count' => prop.dig('Pictures', 'Picture')&.is_a?(Array) ? prop.dig('Pictures', 'Picture').size : 0,
        'visible' => prop.dig('Status', 'system')&.downcase == 'available' # Assuming 'Available' means visible
      }.transform_values { |v| v.is_a?(String) ? v.strip : v } # Optional: strip whitespace from string values
    end

    def asset_attributes
      %w[
        title categories city city_search_key constructed_area count_bathrooms count_bedrooms
        count_garages count_toilets country description details discarded_at energy_performance
        energy_rating floor has_rental_listings has_sale_listings has_sold_transactions
        host_on_create is_ai_generated_realty_asset latitude longitude neighborhood
        neighborhood_search_key plot_area postal_code prop_state_key prop_type_key province
        ra_photos_count realty_asset_flags realty_asset_tags reference region
        rental_listings_count sale_listings_count sold_transactions_count street_address
        street_number year_construction
      ]
    end

    def listing_attributes
      %w[
        title description archived commission_cents commission_currency currency design_style
        details_of_rooms discarded_at extra_sale_details furnished hide_map highlighted
        host_on_create is_ai_generated_listing listing_pages_count listing_slug listing_tags
        main_video_url obscure_map page_section_listings_count position_in_list
        price_sale_current_cents price_sale_current_currency price_sale_original_cents
        price_sale_original_currency property_board_items_count publish_from publish_till
        reference related_urls reserved sale_listing_features sale_listing_flags
        sale_listing_gen_prompt service_charge_yearly_cents service_charge_yearly_currency
        sl_photos_count visible
      ]
    end
  end
end 