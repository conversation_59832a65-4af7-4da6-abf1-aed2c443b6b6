# frozen_string_literal: true

module Pasarelas
  class ZillowPasarela
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      # For Zillow, we need to parse HTML content from full_content_before_js
      html_content = @realty_scraped_item.full_content_before_js
      return unless html_content.present? && html_content.length > 100

      begin
        # Parse the HTML to find JSON data or extract directly from HTML
        doc = Nokogiri::HTML(html_content)
  zillow_data = extract_zillow_data(doc)
  return unless zillow_data.is_a?(Hash)
  # Augment with any missing fields derivable from HTML (price, beds, baths, images, lat/long)
  augment_with_html_fallbacks(zillow_data, doc)

        # Map the data to our schema
        listing_data = map_property_to_listing_schema(zillow_data)
        asset_data = map_property_to_asset_schema(zillow_data)

        # Extract image URLs
        extracted_image_urls = extract_image_urls(zillow_data, doc)

        # Save the extracted data directly to the database columns
  # As a safety net, ensure minimal hashes present
  @realty_scraped_item.extracted_asset_data = (asset_data.presence || { 'title' => extract_title_from_data(zillow_data) })
  @realty_scraped_item.extracted_listing_data = (listing_data.presence || { 'title' => extract_title_from_data(zillow_data) })
        @realty_scraped_item.extracted_image_urls = extracted_image_urls || []
        
        @realty_scraped_item.save!

        Rails.logger.info("Successfully processed Zillow data for scrape item #{@realty_scraped_item.id}")
      rescue JSON::ParserError => e
        Rails.logger.error("Failed to parse Zillow JSON: #{e.message}")
        return
      rescue StandardError => e
        Rails.logger.error("Error processing Zillow data: #{e.message}")
        return
      end
    end

    private

    def extract_zillow_data(doc)
      # Try to find JSON data in script tags first
  json_data = extract_json_from_scripts(doc)
  return json_data if json_data
  # Fallback to HTML extraction returns a hash shaped like property data but not nested
  html_data = extract_data_from_html(doc)
  # Wrap html_data so downstream dig('props','pageProps','property') works if possible
  wrap_in_property(html_data)
    end

    def extract_json_from_scripts(doc)
      # Look for __NEXT_DATA__ script first (common in React apps like Zillow)
      next_data_script = doc.at('script#__NEXT_DATA__')
      if next_data_script
        begin
          return JSON.parse(next_data_script.text)
        rescue JSON::ParserError
          # Continue to other methods
        end
      end

      # Look for JSON in script tags with specific types
      script_tags = doc.css('script[type="application/json"]')
      script_tags.each do |script|
        begin
          json_content = JSON.parse(script.text)
          return json_content if json_content && json_content.is_a?(Hash)
        rescue JSON::ParserError
          next
        end
      end

      # Look for JSON in regular script tags with common patterns
      script_tags = doc.css('script')
      script_tags.each do |script|
        script_text = script.text

        # Fast path: attempt to directly extract large embedded JSON objects that contain Zillow property keys
        if script_text.include?('atAGlanceFacts') || script_text.include?('bathroomsFloat') || script_text.include?('livingArea')
          candidate_objects = robust_json_object_scan(script_text)
          candidate_objects.each do |obj_str|
            begin
              parsed = JSON.parse(obj_str)
              if likely_zillow_property_hash?(parsed)
                return wrap_in_property(parsed)
              elsif parsed.is_a?(Hash)
                # Sometimes property nested under keys
                deep_prop = find_deep_property_hash(parsed)
                return wrap_in_property(deep_prop) if deep_prop
              end
            rescue JSON::ParserError
              next
            end
          end
        end

        # Look for window.__INITIAL_STATE__ or similar patterns
        if script_text.include?('window.__INITIAL_STATE__')
          json_match = script_text.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/)
          if json_match
            begin
              return JSON.parse(json_match[1])
            rescue JSON::ParserError
              next
            end
          end
        end

        # Look for other common patterns
        if script_text.include?('window.dataLayer') || script_text.include?('window.utag_data')
          json_match = script_text.match(/(window\.dataLayer\s*=\s*\[.*?\]|window\.utag_data\s*=\s*{.*?})/)
          if json_match
            begin
              clean_json = json_match[1].gsub(/^window\.(dataLayer|utag_data)\s*=\s*/, '')
              return JSON.parse(clean_json)
            rescue JSON::ParserError
              next
            end
          end
        end

        # Look for Zillow-specific patterns in script content
        # Check for property data in various formats
        if script_text.include?('zpid') || script_text.include?('property') || script_text.include?('homedetails')
          # Try to extract JSON objects that contain property data
          json_matches = robust_json_object_scan(script_text)
          json_matches.each do |match|
            obj_str = match.is_a?(Array) ? match[0] : match
            begin
              parsed = JSON.parse(obj_str)
              if parsed.is_a?(Hash) && (parsed['zpid'] || parsed['property'] || parsed.dig('props', 'pageProps'))
                # If this is a flat property hash (has bedrooms, bathrooms, etc) wrap it similar to __NEXT_DATA__ shape for later unified extraction
                return wrap_in_property(parsed)
              end
              deep_prop = find_deep_property_hash(parsed)
              return wrap_in_property(deep_prop) if deep_prop
            rescue JSON::ParserError
              next
            end
          end
        end
      end

      nil
    end

    # Attempt to find a deeply nested hash that looks like a Zillow property
    def find_deep_property_hash(obj, depth = 0)
      return nil if depth > 6
      if obj.is_a?(Hash)
        return obj if likely_zillow_property_hash?(obj)
        obj.each_value do |v|
          found = find_deep_property_hash(v, depth + 1)
          return found if found
        end
      elsif obj.is_a?(Array)
        obj.each do |v|
          found = find_deep_property_hash(v, depth + 1)
          return found if found
        end
      end
      nil
    end

    def likely_zillow_property_hash?(hash)
      return false unless hash.is_a?(Hash)
      indicative_keys = %w[zpid bedrooms bathrooms livingArea latitude longitude atAGlanceFacts homeStatus homeType address]
      (indicative_keys & hash.keys).length >= 3
    end

    def wrap_in_property(property_hash)
      return property_hash if property_hash.key?('props') # Already in a larger structure
      # Normalize simple HTML-extracted shape into something with basic keys expected downstream
      normalized = property_hash.dup
      if normalized['location'].is_a?(Hash)
        addr = normalized['location']
        normalized['address'] ||= {
          'streetAddress' => addr['address'],
          'city' => addr['city'],
          'state' => addr['state'],
            'zipcode' => addr['zip_code']
        }
      end
      {
        'props' => {
          'pageProps' => {
            'property' => normalized
          }
        }
      }
    end

    # More robust JSON object scanner that balances braces and ignores obvious JS fragments
    def robust_json_object_scan(text)
      results = []
      stack = []
      start_idx = nil
      in_string = false
      escape = false
      text.each_char.with_index do |ch, i|
        if start_idx
          if in_string
            if escape
              escape = false
            elsif ch == '\\'
              escape = true
            elsif ch == '"'
              in_string = false
            end
          else
            if ch == '"'
              in_string = true
            elsif ch == '{'
              stack << '{'
            elsif ch == '}'
              stack.pop
              if stack.empty?
                obj_str = text[start_idx..i]
                # Heuristic: ignore very small objects
                results << obj_str if obj_str.length > 50 && obj_str.include?(':')
                start_idx = nil
              end
            end
          end
        else
          if ch == '{'
            start_idx = i
            stack = ['{']
            in_string = false
            escape = false
          end
        end
      end
      results.uniq.first(40) # limit to avoid huge memory usage
    end

    def extract_data_from_html(doc)
      # Enhanced fallback HTML extraction with more comprehensive selectors
      location_text = extract_location_from_html(doc)

      # Try to extract ZPID from URL patterns in the HTML
      zpid = extract_zpid_from_html(doc)

      # Look for structured data in meta tags or data attributes
      structured_data = extract_structured_data(doc)

      base_data = {
        'title' => extract_title_from_html_enhanced(doc),
        'price' => extract_price_from_html_enhanced(doc),
        'description' => extract_description_from_html_enhanced(doc),
        'bedrooms' => extract_bedrooms_from_html_enhanced(doc),
        'bathrooms' => extract_bathrooms_from_html_enhanced(doc),
        'area' => extract_area_from_html_enhanced(doc),
        'location' => {
          'address' => location_text,
          'city' => extract_city_from_location_text(location_text),
          'state' => extract_state_from_location_text(location_text),
          'zip_code' => extract_zip_from_location_text(location_text)
        },
        'property_type' => extract_property_type_from_html_enhanced(doc),
        'features' => extract_features_from_html_enhanced(doc),
        'year_built' => extract_year_built_from_html_enhanced(doc),
        'lot_size' => extract_lot_size_from_html_enhanced(doc),
        'zpid' => zpid
      }

      # Merge with any structured data found
      base_data.merge(structured_data)
    end

    def map_property_to_asset_schema(zillow_data)
  # Extract property and location data
  property_hash = zillow_data.dig('props', 'pageProps', 'property') || {}
  location_data = zillow_data['location'] || property_hash['address'] || {}
  # Allow latitude/longitude at top-level of property hash
  location_data = location_data.merge({ 'latitude' => property_hash['latitude'], 'longitude' => property_hash['longitude'] }.compact)
      
      {
  'title' => extract_title_from_data(zillow_data) || property_hash['streetAddress'] || property_hash['address'] || 'Property',
        'categories' => extract_categories(zillow_data),
        'city' => extract_city(location_data),
        'city_search_key' => extract_city(location_data)&.downcase&.gsub(/\s+/, '-') || '',
        'constructed_area' => extract_area_value(zillow_data),
        'count_bathrooms' => extract_bathroom_count(zillow_data),
        'count_bedrooms' => extract_bedroom_count(zillow_data),
        'count_garages' => extract_garage_count(zillow_data),
        'count_toilets' => 0, # Not typically available in Zillow data
        'country' => 'US', # Zillow is US-focused
        'description' => extract_description_from_data(zillow_data),
        'details' => extract_property_details(zillow_data),
        'discarded_at' => nil,
        'energy_performance' => extract_energy_performance(zillow_data),
        'energy_rating' => extract_energy_rating(zillow_data),
        'floor' => extract_floor(zillow_data),
        'has_rental_listings' => false,
        'has_sale_listings' => true,
        'has_sold_transactions' => false,
        'host_on_create' => 'zillow.com',
        'is_ai_generated_realty_asset' => false,
        'latitude' => extract_latitude(location_data),
        'longitude' => extract_longitude(location_data),
        'neighborhood' => extract_neighborhood(location_data),
        'plot_area' => extract_plot_area(zillow_data),
        'postal_code' => extract_postal_code(location_data),
        'prop_type' => extract_property_type(zillow_data),
        'prop_type_key' => extract_property_type(zillow_data)&.downcase&.gsub(/\s+/, '-') || '',
        'province' => extract_state(location_data),
        'ra_photos_count' => extract_photo_count(zillow_data),
        'realty_asset_flags' => 0,
        'realty_asset_tags' => extract_tags(zillow_data),
        'reference' => extract_reference(zillow_data),
        'region' => extract_region(location_data),
        'rental_listings_count' => 0,
        'sale_listings_count' => 1,
        'sold_transactions_count' => 0,
        'street_address' => extract_street_address(location_data),
        'street_number' => extract_street_number(location_data),
        'year_construction' => extract_year_construction(zillow_data)
      }
    end

    def map_property_to_listing_schema(zillow_data)
      price_data = extract_price_data(zillow_data)
  property_hash = zillow_data.dig('props', 'pageProps', 'property') || {}
      
      {
        'agency_name' => extract_agency_name(zillow_data),
        'agency_reference' => extract_agency_reference(zillow_data),
        'area_unit' => 'sqft',
        'constructed_area' => extract_area_value(zillow_data),
        'count_bathrooms' => extract_bathroom_count(zillow_data),
        'count_bedrooms' => extract_bedroom_count(zillow_data),
        'count_garages' => extract_garage_count(zillow_data),
        'count_toilets' => 0,
        'description' => extract_description_from_data(zillow_data),
        'discarded_at' => nil,
        'energy_performance' => extract_energy_performance(zillow_data),
        'energy_rating' => extract_energy_rating(zillow_data),
        'floor' => extract_floor(zillow_data),
        'has_virtual_tour' => extract_virtual_tour_status(zillow_data),
        'is_ai_generated_listing' => false,
        'listing_pages_count' => 0,
        'listing_slug' => extract_listing_slug(zillow_data),
        'listing_tags' => extract_listing_tags(zillow_data),
        'main_video_url' => extract_video_url(zillow_data),
        'obscure_map' => false,
        'page_section_listings_count' => 0,
        'position_in_list' => nil,
        'price_sale_current_cents' => price_data[:current_cents],
        'price_sale_current_currency' => price_data[:currency],
        'price_sale_original_cents' => price_data[:original_cents],
        'price_sale_original_currency' => price_data[:currency],
        'property_board_items_count' => 0,
        'property_reference' => extract_reference(zillow_data),
        'sale_listing_flags' => 0,
  'title' => extract_title_from_data(zillow_data) || property_hash['streetAddress'] || 'Property',
        'visible' => true,
        'year_construction' => extract_year_construction(zillow_data)
      }
    end

    def extract_image_urls(zillow_data, doc)
      images = []
      
      # Try to get images from JSON data first
      if zillow_data['images'].is_a?(Array)
        images = zillow_data['images']
      elsif zillow_data.dig('props', 'pageProps', 'property', 'photos').is_a?(Array)
        images = zillow_data.dig('props', 'pageProps', 'property', 'photos')
      elsif zillow_data.dig('property', 'photos').is_a?(Array)
        images = zillow_data.dig('property', 'photos')
      else
        # Fallback to HTML extraction
        doc.css('img').each do |img|
          src = img['src'] || img['data-src'] || img['data-original']
          if src && (src.include?('zillow') || src.start_with?('http'))
            images << src
          end
        end
      end
      
      # Clean and validate image URLs
      images.compact.uniq.select { |url| url.is_a?(String) && url.length > 10 }
    end

    # Helper methods for data extraction
    def extract_title_from_data(data)
  data.dig('props', 'pageProps', 'property', 'streetAddress') || 
  data.dig('props', 'pageProps', 'property', 'address', 'streetAddress') ||
  data.dig('property', 'streetAddress') ||
  data.dig('property', 'address') ||
  data['title']
    end

    def extract_description_from_data(data)
      data.dig('props', 'pageProps', 'property', 'description') || 
      data.dig('property', 'description') ||
      data['description']
    end

    def extract_categories(data)
      property_type = extract_property_type(data)
      property_type ? [property_type] : []
    end

    def extract_city(location_data)
      location_data['city'] || location_data['locality'] || location_data['municipality']
    end

    def extract_area_value(data)
      area = data.dig('props', 'pageProps', 'property', 'livingArea') || 
             data.dig('property', 'livingArea') || 
             data['area']
      area.is_a?(Numeric) ? area.to_f : area.to_f rescue 0.0
    end

    def extract_bathroom_count(data)
  bathrooms = data.dig('props', 'pageProps', 'property', 'bathrooms') || 
      data.dig('props', 'pageProps', 'property', 'bathroomsFloat') || 
      data.dig('property', 'bathrooms') || 
      data.dig('property', 'bathroomsFloat') || 
      data['bathrooms'] || data['bathroomsFloat']
      bathrooms.is_a?(Numeric) ? bathrooms.to_f : bathrooms.to_f rescue 0.0
    end

    def extract_bedroom_count(data)
      bedrooms = data.dig('props', 'pageProps', 'property', 'bedrooms') || 
                 data.dig('property', 'bedrooms') || 
                 data['bedrooms']
      bedrooms.is_a?(Numeric) ? bedrooms.to_i : bedrooms.to_i rescue 0
    end

    def extract_garage_count(data)
      garages = data.dig('props', 'pageProps', 'property', 'parkingSpaces') || 
                data.dig('property', 'parkingSpaces') || 
                data['garages']
      garages.is_a?(Numeric) ? garages.to_i : garages.to_i rescue 0
    end

    def extract_property_details(data)
      details = {}
      
      # Extract various property details
      if data['features'].is_a?(Array)
        data['features'].each_with_index do |feature, index|
          details["feature_#{index}"] = feature
        end
      end
      
      if data['amenities'].is_a?(Hash)
        details.merge!(data['amenities'])
      end
      
      details
    end

    def extract_energy_performance(data)
      data.dig('energy', 'performance') || data.dig('energy_info', 'performance')
    end

    def extract_energy_rating(data)
      data.dig('energy', 'rating') || data.dig('energy_info', 'rating')
    end

    def extract_floor(data)
      data['floor'] || data.dig('property', 'floor')
    end

    def extract_latitude(location_data)
      lat = location_data['latitude'] || location_data['lat'] || location_data.dig('coordinates', 'latitude')
      lat.is_a?(Numeric) ? lat.to_f : lat.to_f rescue nil
    end

    def extract_longitude(location_data)
      lng = location_data['longitude'] || location_data['lng'] || location_data['lon'] || location_data.dig('coordinates', 'longitude')
      lng.is_a?(Numeric) ? lng.to_f : lng.to_f rescue nil
    end

    def extract_neighborhood(location_data)
      location_data['neighborhood'] || location_data['district'] || location_data['area']
    end

    def extract_plot_area(data)
      plot = data.dig('props', 'pageProps', 'property', 'lotSize') || 
             data.dig('property', 'lotSize') || 
             data['lot_size']
      plot.is_a?(Numeric) ? plot.to_f : plot.to_f rescue 0.0
    end

    def extract_postal_code(location_data)
      location_data['postal_code'] || location_data['zip_code'] || location_data['zipcode']
    end

    def extract_property_type(data)
      data.dig('props', 'pageProps', 'property', 'homeType') || 
      data.dig('property', 'homeType') || 
      data['property_type']
    end

    def extract_state(location_data)
      location_data['state'] || location_data['region'] || location_data['province']
    end

    def extract_photo_count(data)
      if data['images'].is_a?(Array)
        data['images'].length
      elsif data.dig('props', 'pageProps', 'property', 'photos').is_a?(Array)
        data.dig('props', 'pageProps', 'property', 'photos').length
      else
        0
      end
    end

    def extract_tags(data)
      tags = []
      tags << data['property_type'] if data['property_type']
      tags += data['features'] if data['features'].is_a?(Array)
      tags.compact.uniq
    end

    def extract_reference(data)
      data.dig('props', 'pageProps', 'property', 'zpid') || 
      data.dig('property', 'zpid') || 
      data['reference'] || 
      data['id']
    end

    def extract_region(location_data)
      location_data['region'] || location_data['state'] || location_data['province']
    end

    def extract_street_address(location_data)
      location_data['address'] || location_data['street_address'] || location_data['streetAddress']
    end

    def extract_street_number(location_data)
      location_data['street_number'] || location_data['number'] || location_data['streetNumber']
    end

    def extract_year_construction(data)
      year = data.dig('props', 'pageProps', 'property', 'yearBuilt') || 
             data.dig('property', 'yearBuilt') || 
             data['year_built']
      year.is_a?(Numeric) ? year.to_i : year.to_i rescue nil
    end

    def extract_price_data(data)
      price = data.dig('props', 'pageProps', 'property', 'price') || 
              data.dig('property', 'price') || 
              data['price']
      
      if price.is_a?(Hash)
        amount = price['amount'] || price['value']
        currency = price['currency'] || 'USD'
      else
        amount = price
        currency = 'USD'
      end
      
      # Convert to cents
      cents = amount.is_a?(Numeric) ? (amount * 100).to_i : amount.to_i * 100 rescue 0
      
      {
        current_cents: cents,
        original_cents: cents,
        currency: currency
      }
    end

    # Augment missing property fields after initial JSON/HTML parse
    def augment_with_html_fallbacks(zillow_data, doc)
      return unless zillow_data.is_a?(Hash)
      property_hash = zillow_data.dig('props', 'pageProps', 'property')
      return unless property_hash.is_a?(Hash)

      # Price fallback
      if property_hash['price'].blank?
        price_from_html = extract_price_from_html_enhanced(doc)
        if price_from_html && price_from_html > 0
          property_hash['price'] = { 'value' => price_from_html, 'currency' => 'USD' }
        else
          # Try meta description
            meta_desc = doc.at('meta[name="description"]')&.[]('content')
            if meta_desc && (m = meta_desc.match(/\$([\d,]+(?:\.\d+)?)/))
              property_hash['price'] = { 'value' => m[1].gsub(',', '').to_f, 'currency' => 'USD' }
            end
        end
      end

      # Bathrooms fallback (HTML extraction returns float)
      if property_hash['bathrooms'].blank? && property_hash['bathroomsFloat'].blank?
        baths = extract_bathrooms_from_html_enhanced(doc)
        property_hash['bathrooms'] = baths if baths && baths > 0
      end

      # Bedrooms fallback
      if property_hash['bedrooms'].blank?
        beds = extract_bedrooms_from_html_enhanced(doc)
        property_hash['bedrooms'] = beds if beds && beds > 0
      end

      # Living area fallback
      if property_hash['livingArea'].blank?
        area = extract_area_from_html_enhanced(doc)
        property_hash['livingArea'] = area if area && area > 0
      end

      # Lat/Long fallback if not present
      if property_hash['latitude'].blank? || property_hash['longitude'].blank?
        # Sometimes coordinates appear in inline scripts outside property hash; try regex across full doc
        raw = doc.to_s
        if property_hash['latitude'].blank? && (lat_match = raw.match(/"latitude"\s*:\s*(-?\d+\.\d+)/))
          property_hash['latitude'] = lat_match[1].to_f
        end
        if property_hash['longitude'].blank? && (lng_match = raw.match(/"longitude"\s*:\s*(-?\d+\.\d+)/))
          property_hash['longitude'] = lng_match[1].to_f
        end
      end

      # Photos fallback
      if property_hash['photos'].blank?
        og_images = doc.css('meta[property="og:image"]').map { |m| m['content'] }.compact
        inline_imgs = doc.css('img').map { |i| i['src'] }.compact.select { |u| u.include?('zillowstatic') }
        combined = (og_images + inline_imgs).uniq
        property_hash['photos'] = combined if combined.any?
      end

      # Description fallback
      if property_hash['description'].blank?
        desc = extract_description_from_html_enhanced(doc)
        property_hash['description'] = desc if desc.present?
      end
    end

    def extract_agency_name(data)
      data.dig('agent', 'name') || data.dig('listing_agent', 'name') || 'Unknown Agency'
    end

    def extract_agency_reference(data)
      data.dig('agent', 'reference') || data.dig('listing_agent', 'reference')
    end

    def extract_virtual_tour_status(data)
      !!(data['virtual_tour'] || data.dig('media', 'virtual_tour'))
    end

    def extract_listing_slug(data)
      reference = extract_reference(data)
      reference ? reference.to_s.downcase.gsub(/[^a-z0-9]/, '-') : nil
    end

    def extract_listing_tags(data)
      extract_tags(data)
    end

    def extract_video_url(data)
      data.dig('media', 'video') || data.dig('videos', 0, 'url')
    end

    # HTML extraction fallback methods
    def extract_price_from_html(doc)
      price_element = doc.css('[data-testid="price"], .price, [class*="price"]').first
      price_text = price_element&.text&.strip
      return nil unless price_text
      
      # Extract numeric value from price text
      price_match = price_text.match(/[\d,]+/)
      price_match ? price_match[0].gsub(',', '').to_f : 0.0
    end

    def extract_description_from_html(doc)
      desc_element = doc.css('[data-testid="description"], .description, [class*="description"]').first
      desc_element&.text&.strip
    end

    def extract_bedrooms_from_html(doc)
      bedroom_text = doc.text
      bedroom_match = bedroom_text.match(/(\d+)\s*(bed|bedroom)/i)
      bedroom_match ? bedroom_match[1].to_i : 0
    end

    def extract_bathrooms_from_html(doc)
      bathroom_text = doc.text
      bathroom_match = bathroom_text.match(/(\d+)\s*(bath|bathroom)/i)
      bathroom_match ? bathroom_match[1].to_i : 0
    end

    def extract_area_from_html(doc)
      area_text = doc.text
      area_match = area_text.match(/(\d+,?\d*)\s*sqft/i)
      area_match ? area_match[1].gsub(',', '').to_i : 0
    end

    def extract_location_from_html(doc)
      location_element = doc.css('h1, [data-testid="address"], .address').first
      location_element&.text&.strip
    end

    def extract_property_type_from_html(doc)
      # Look for property type indicators
      type_text = doc.text
      if type_text.match(/single.family/i)
        'SingleFamily'
      elsif type_text.match(/condo|condominium/i)
        'Condo'
      elsif type_text.match(/townhouse|townhome/i)
        'Townhouse'
      elsif type_text.match(/apartment/i)
        'Apartment'
      else
        'Unknown'
      end
    end

    def extract_features_from_html(doc)
      features = []
      feature_elements = doc.css('.features, [class*="feature"], [data-testid*="feature"]')
      feature_elements.each do |element|
        features << element.text.strip if element.text.present?
      end
      features
    end

    def extract_year_built_from_html(doc)
      year_text = doc.text
      year_match = year_text.match(/built.{0,10}(\d{4})/i)
      year_match ? year_match[1].to_i : nil
    end

    def extract_lot_size_from_html(doc)
      lot_text = doc.text
      lot_match = lot_text.match(/([\d.]+)\s*acre/i)
      lot_match ? lot_match[1].to_f : 0.0
    end

    def extract_city_from_location_text(location_text)
      return nil unless location_text
      
      # Try to extract city from location text like "Chappaqua, NY 10514"
      parts = location_text.split(',').map(&:strip)
      if parts.length >= 2
        # The city is usually the first part before the first comma
        parts.first
      else
        parts.first
      end
    end

    def extract_state_from_location_text(location_text)
      return nil unless location_text
      
      # Try to extract state from location text like "Chappaqua, NY 10514"
      state_match = location_text.match(/,\s*([A-Z]{2})\s*\d{5}/)
      state_match ? state_match[1] : nil
    end

    def extract_zip_from_location_text(location_text)
      return nil unless location_text

      # Try to extract ZIP code from location text like "Chappaqua, NY 10514"
      zip_match = location_text.match(/(\d{5})/)
      zip_match ? zip_match[1] : nil
    end

    # Enhanced HTML extraction methods
    def extract_zpid_from_html(doc)
      # Try to find ZPID in URLs, data attributes, or text content
      zpid_patterns = [
        /\/(\d{8,})_zpid\//,
        /zpid["\s]*[:=]["\s]*(\d{8,})/i,
        /property[_-]?id["\s]*[:=]["\s]*(\d{8,})/i
      ]

      # Check all text content and attributes
      all_text = doc.text + doc.to_s
      zpid_patterns.each do |pattern|
        match = all_text.match(pattern)
        return match[1] if match
      end

      nil
    end

    def extract_structured_data(doc)
      structured = {}

      # Look for JSON-LD structured data
      json_ld_scripts = doc.css('script[type="application/ld+json"]')
      json_ld_scripts.each do |script|
        begin
          data = JSON.parse(script.text)
          if data.is_a?(Hash) && data['@type'] == 'RealEstateListing'
            structured.merge!(extract_from_json_ld(data))
          end
        rescue JSON::ParserError
          next
        end
      end

      # Look for Open Graph meta tags
      og_data = extract_open_graph_data(doc)
      structured.merge!(og_data) if og_data.any?

      structured
    end

    def extract_from_json_ld(data)
      result = {}
      result['title'] = data['name'] if data['name']
      result['description'] = data['description'] if data['description']

      if data['offers'] && data['offers']['price']
        result['price'] = data['offers']['price'].to_f
      end

      if data['address']
        addr = data['address']
        result['location'] = {
          'address' => addr['streetAddress'],
          'city' => addr['addressLocality'],
          'state' => addr['addressRegion'],
          'zip_code' => addr['postalCode']
        }
      end

      result
    end

    def extract_open_graph_data(doc)
      og_data = {}

      # Extract Open Graph meta tags
      doc.css('meta[property^="og:"]').each do |meta|
        property = meta['property']
        content = meta['content']

        case property
        when 'og:title'
          og_data['title'] = content
        when 'og:description'
          og_data['description'] = content
        when 'og:image'
          og_data['images'] ||= []
          og_data['images'] << content
        end
      end

      og_data
    end

    # Enhanced HTML extraction methods with better selectors
    def extract_title_from_html_enhanced(doc)
      selectors = [
        'h1[data-testid="property-title"]',
        'h1[class*="title"]',
        'h1[class*="address"]',
        '.property-title',
        '.listing-title',
        'h1',
        'title'
      ]

      selectors.each do |selector|
        element = doc.css(selector).first
        if element && element.text.strip.length > 5
          return element.text.strip
        end
      end

      # Fallback: try to extract from URL or page title
      title_tag = doc.css('title').first
      if title_tag
        title_text = title_tag.text.strip
        # Clean up common Zillow title patterns
        title_text = title_text.gsub(/\s*\|\s*Zillow.*$/i, '')
        title_text = title_text.gsub(/\s*-\s*Zillow.*$/i, '')
        return title_text if title_text.length > 5
      end

      'Unknown Property'
    end

    def extract_price_from_html_enhanced(doc)
      selectors = [
        '[data-testid="price"]',
        '.price-current',
        '.listing-price',
        '.property-price',
        '[class*="price"]'
      ]

      selectors.each do |selector|
        element = doc.css(selector).first
        if element
          price_text = element.text.strip
          price_match = price_text.match(/[\d,]+/)
          if price_match
            return price_match[0].gsub(',', '').to_f
          end
        end
      end

      # Fallback: search all text for price patterns
      all_text = doc.text
      price_patterns = [
        /\$\s*([\d,]+(?:\.\d{2})?)/,
        /Price:\s*\$?\s*([\d,]+)/i,
        /Listed at:\s*\$?\s*([\d,]+)/i
      ]

      price_patterns.each do |pattern|
        match = all_text.match(pattern)
        if match
          return match[1].gsub(',', '').to_f
        end
      end

      0.0
    end

    def extract_description_from_html_enhanced(doc)
      selectors = [
        '[data-testid="description"]',
        '.property-description',
        '.listing-description',
        '[class*="description"]',
        '.summary',
        '.overview'
      ]

      selectors.each do |selector|
        element = doc.css(selector).first
        if element && element.text.strip.length > 20
          return element.text.strip
        end
      end

      # Fallback: look for meta description
      meta_desc = doc.css('meta[name="description"]').first
      return meta_desc['content'] if meta_desc && meta_desc['content']

      ''
    end

    def extract_bedrooms_from_html_enhanced(doc)
      patterns = [
        /(\d+)\s*bed(?:room)?s?/i,
        /bed(?:room)?s?:\s*(\d+)/i,
        /(\d+)\s*br/i
      ]

      # Check data attributes first
      bed_attrs = doc.css('[data-bedrooms], [data-beds]').first
      if bed_attrs
        beds = bed_attrs['data-bedrooms'] || bed_attrs['data-beds']
        return beds.to_i if beds
      end

      # Search text content
      all_text = doc.text
      patterns.each do |pattern|
        match = all_text.match(pattern)
        return match[1].to_i if match
      end

      0
    end

    def extract_bathrooms_from_html_enhanced(doc)
      patterns = [
        /(\d+(?:\.\d+)?)\s*bath(?:room)?s?/i,
        /bath(?:room)?s?:\s*(\d+(?:\.\d+)?)/i,
        /(\d+(?:\.\d+)?)\s*ba/i
      ]

      # Check data attributes first
      bath_attrs = doc.css('[data-bathrooms], [data-baths]').first
      if bath_attrs
        baths = bath_attrs['data-bathrooms'] || bath_attrs['data-baths']
        return baths.to_f if baths
      end

      # Search text content
      all_text = doc.text
      patterns.each do |pattern|
        match = all_text.match(pattern)
        return match[1].to_f if match
      end

      0.0
    end

    def extract_area_from_html_enhanced(doc)
      patterns = [
        /([\d,]+)\s*sq\.?\s*ft\.?/i,
        /([\d,]+)\s*sqft/i,
        /(\d+(?:,\d{3})*)\s*square\s*feet/i
      ]

      # Check data attributes first
      area_attrs = doc.css('[data-area], [data-sqft]').first
      if area_attrs
        area = area_attrs['data-area'] || area_attrs['data-sqft']
        return area.gsub(',', '').to_f if area
      end

      # Search text content
      all_text = doc.text
      patterns.each do |pattern|
        match = all_text.match(pattern)
        return match[1].gsub(',', '').to_f if match
      end

      0.0
    end

    def extract_property_type_from_html_enhanced(doc)
      # Look for property type indicators in various places
      type_patterns = [
        /single.family/i => 'SingleFamily',
        /condo|condominium/i => 'Condo',
        /townhouse|townhome/i => 'Townhouse',
        /apartment/i => 'Apartment',
        /mobile.home/i => 'MobileHome',
        /manufactured/i => 'Manufactured',
        /multi.family/i => 'MultiFamily'
      ]

      all_text = doc.text
      type_patterns.each do |pattern, type|
        return type if all_text.match(pattern)
      end

      'Unknown'
    end

    def extract_features_from_html_enhanced(doc)
      features = []

      # Look for feature lists
      feature_selectors = [
        '.features li',
        '.amenities li',
        '[class*="feature"] li',
        '.property-features li'
      ]

      feature_selectors.each do |selector|
        doc.css(selector).each do |element|
          feature_text = element.text.strip
          features << feature_text if feature_text.length > 2
        end
      end

      # Look for common features in text
      common_features = [
        'hardwood floors', 'granite countertops', 'stainless steel appliances',
        'fireplace', 'central air', 'garage', 'pool', 'deck', 'patio',
        'basement', 'attic', 'walk-in closet', 'master suite'
      ]

      all_text = doc.text.downcase
      common_features.each do |feature|
        features << feature.titleize if all_text.include?(feature)
      end

      features.uniq
    end

    def extract_year_built_from_html_enhanced(doc)
      patterns = [
        /built.{0,10}(\d{4})/i,
        /year.built.{0,10}(\d{4})/i,
        /constructed.{0,10}(\d{4})/i,
        /(\d{4}).built/i
      ]

      all_text = doc.text
      patterns.each do |pattern|
        match = all_text.match(pattern)
        if match
          year = match[1].to_i
          return year if year > 1800 && year <= Date.current.year
        end
      end

      nil
    end

    def extract_lot_size_from_html_enhanced(doc)
      patterns = [
        /([\d.]+)\s*acres?/i,
        /lot.size.{0,20}([\d.]+)\s*acres?/i,
        /([\d,]+)\s*sq\.?\s*ft\.?\s*lot/i
      ]

      all_text = doc.text
      patterns.each do |pattern|
        match = all_text.match(pattern)
        if match
          size = match[1].gsub(',', '').to_f
          return size if size > 0
        end
      end

      0.0
    end
  end
end
