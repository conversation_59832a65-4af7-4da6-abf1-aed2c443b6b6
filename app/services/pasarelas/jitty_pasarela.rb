# frozen_string_literal: true

require 'nokogiri'

module Pasarelas
  class Jitty<PERSON><PERSON><PERSON><PERSON>
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      # For Jitty, we need to parse HTML content from full_content_before_js
      html_content = @realty_scraped_item.full_content_before_js
      return unless html_content.present?

      begin
        # Parse the HTML content
        @doc = Nokogiri::HTML(html_content)
        
        # Extract the data using the same methods as the original model
        listing_data = extract_listing_data
        asset_data = extract_asset_data
        extracted_image_urls = extract_image_urls

        # Save the extracted data
        @realty_scraped_item.extracted_asset_data = asset_data || {}
        @realty_scraped_item.extracted_listing_data = listing_data || {}
        @realty_scraped_item.extracted_image_urls = extracted_image_urls || []
        @realty_scraped_item.save!

      rescue Exception => e
        Rails.logger.error("Error processing Jitty data: #{e.message}")
      end
    end

    private

    def extract_listing_data
      data = {
        'archived' => false,
        'commission_cents' => 0,
        'commission_currency' => 'GBP',
        'currency' => 'GBP',
        'design_style' => nil,
        'details_of_rooms' => {},
        'discarded_at' => nil,
        'extra_sale_details' => {},
        'furnished' => extract_furnished,
        'hide_map' => false,
        'highlighted' => false,
        'host_on_create' => 'unknown_host',
        'is_ai_generated_listing' => false,
        'listing_pages_count' => 0,
        'listing_slug' => extract_listing_id,
        'listing_tags' => [],
        'main_video_url' => extract_video_url,
        'obscure_map' => false,
        'page_section_listings_count' => 0,
        'position_in_list' => nil,
        'price_sale_current_cents' => extract_price_cents,
        'price_sale_current_currency' => 'GBP',
        'price_sale_original_cents' => extract_price_cents,
        'price_sale_original_currency' => 'GBP',
        'property_board_items_count' => 0,
        'publish_from' => nil,
        'publish_till' => nil,
        'reference' => extract_listing_id,
        'related_urls' => {},
        'reserved' => false,
        'sale_listing_features' => extract_features,
        'sale_listing_flags' => 0,
        'sale_listing_gen_prompt' => nil,
        'service_charge_yearly_cents' => extract_service_charge_cents,
        'service_charge_yearly_currency' => 'GBP',
        'sl_photos_count' => count_photos,
        'visible' => true
      }

      data
    end

    def extract_asset_data
      data = {
        'categories' => extract_categories,
        'city' => extract_city,
        'city_search_key' => extract_city&.downcase,
        'constructed_area' => 0.0,
        'count_bathrooms' => extract_bathrooms,
        'count_bedrooms' => extract_bedrooms,
        'count_garages' => extract_garages,
        'count_toilets' => 0,
        'country' => 'England',
        'description' => extract_description,
        'details' => {},
        'discarded_at' => nil,
        'energy_performance' => nil,
        'energy_rating' => extract_energy_rating,
        'floor' => nil,
        'has_rental_listings' => false,
        'has_sale_listings' => true,
        'has_sold_transactions' => false,
        'host_on_create' => 'unknown_host',
        'is_ai_generated_realty_asset' => false,
        'latitude' => extract_latitude,
        'longitude' => extract_longitude,
        'neighborhood' => nil,
        'neighborhood_search_key' => '',
        'plot_area' => 0.0,
        'postal_code' => extract_postal_code,
        'prop_state_key' => 'new',
        'prop_type_key' => extract_property_type,
        'province' => nil,
        'ra_photos_count' => count_photos,
        'realty_asset_flags' => 0,
        'realty_asset_tags' => extract_tags,
        'reference' => extract_listing_id,
        'region' => nil,
        'rental_listings_count' => 0,
        'sale_listings_count' => 1,
        'sold_transactions_count' => 0,
        'street_address' => extract_address,
        'street_number' => nil,
        'title' => extract_title,
        'year_construction' => 0
      }

      data
    end

    def extract_listing_id
      # Extract from URL or a visible ID
      url = extract_import_url
      return ::Regexp.last_match(1) if url =~ %r{/(\d+)(?:/|$)}

      # Fallback to anything in the page that looks like a reference
      @doc.css('div:contains("Reference")').each do |el|
        return ::Regexp.last_match(1) if el.text =~ /Reference[:\s]+(\w+)/i
      end

      # Get from share URL if available
      share_url = @doc.css('input[value*="properties/"]').first&.attr('value')
      return ::Regexp.last_match(1) if share_url && share_url =~ %r{properties/([^/?]+)}

      'unknown'
    end

    def extract_price_cents
      price_text = @doc.css('div.flex.flex-row.gap-2.-mt-2.text-lg.font-medium').text.strip
      if price_text =~ /£([\d,]+)/
        price = ::Regexp.last_match(1).gsub(',', '').to_i
        return price * 100
      end
      0
    end

    def extract_features
      features = {}

      feature_elements =
        @doc.css('div.flex.flex-row.items-center.w-fit.content-center.font-medium.text-tertiary-700.bg-tertiary-100')

      feature_elements.each_with_index do |element, index|
        feature_text = element.text.strip
        features[index + 1] = feature_text
      end

      # If no features are found using the above selector, try another approach
      if features.empty?
        feature_items = @doc.css('section:contains("Features") div.text-nowrap')
        feature_items.each_with_index do |item, index|
          features[index + 1] = item.text.strip
        end
      end

      # Add standard features if available
      tenure_text = extract_tenure
      features[1] = "Tenure: #{tenure_text}" if tenure_text && features[1].nil?

      features
    end

    def extract_categories
      features = extract_features
      categories = []

      features.each do |id, name|
        categories << {
          'id' => id,
          'name' => name
        }
      end

      categories
    end

    def extract_service_charge_cents
      service_charge_text = @doc.css('div:contains("Service Charge")').last

      if service_charge_text
        parent_div = service_charge_text.parent.parent
        amount_div = parent_div.css('div.justify-self-end.whitespace-nowrap').first

        if amount_div && amount_div.text =~ /£(\d+)/
          return ::Regexp.last_match(1).to_i * 100 * 12 # Converting monthly to yearly
        end
      end

      0
    end

    def extract_furnished
      @doc.css('div:contains("Furnished")').any? do |el|
        el.text.downcase.include?('furnished')
      end
    end

    def extract_video_url
      video_element = @doc.css('a[href*="youtu"]').first
      video_element&.attr('href') || nil
    end

    def count_photos
      @doc.css('a[href*="/gallery"] img').count
    end

    def extract_bedrooms
      bedroom_div = @doc.css('div:contains("Bedrooms")').last

      if bedroom_div
        bedroom_text = bedroom_div.parent.parent.css('div').last.text.strip
        if bedroom_text =~ /(\d+)/
          return ::Regexp.last_match(1).to_i
        elsif bedroom_text.downcase.include?('studio')
          return 0
        end
      end

      0
    end

    def extract_bathrooms
      bathroom_div = @doc.css('div:contains("Bathrooms")').last

      if bathroom_div
        bathroom_text = bathroom_div.parent.parent.css('div').last.text.strip
        return ::Regexp.last_match(1).to_f if bathroom_text =~ /(\d+)/
      end

      0
    end

    def extract_garages
      garage_element = @doc.css('div.text-nowrap:contains("Garage")').first
      garage_element ? 1 : 0
    end

    def extract_address
      address = @doc.css('h1.w-full.text-2xl.font-medium').text.strip
      address
    end

    def extract_city
      address = extract_address
      return ::Regexp.last_match(1) if address =~ /,\s*([^,]+)$/

      'Unknown'
    end

    def extract_title
      property_type = extract_property_type_text
      bedrooms = extract_bedrooms

      if bedrooms > 0
        "#{bedrooms} bedroom #{property_type}"
      else
        "Studio #{property_type}"
      end
    end

    def extract_property_type
      type_text = extract_property_type_text.downcase

      case type_text
      when /flat/
        'flat'
      when /detached/
        'detached-house'
      when /semi-detached/
        'semi-detached-house'
      when /terraced/
        'terraced-house'
      when /bungalow/
        'bungalow'
      when /apartment/
        'apartment'
      else
        'flat' # Default
      end
    end

    def extract_property_type_text
      type_div = @doc.css('div:contains("Type")').last

      if type_div
        type_div.parent.parent.css('div').last.text.strip
      else
        # Try to find from meta tags
        meta_description = @doc.css('meta[property="og:description"]').first&.attr('content')
        if meta_description && meta_description =~ /(Flat|House|Bungalow|Apartment|Detached|Semi-Detached|Terraced)/i
          return ::Regexp.last_match(1)
        end

        # Check page title
        title = @doc.css('title').text
        return ::Regexp.last_match(1) if title =~ /(Flat|House|Bungalow|Apartment|Detached|Semi-Detached|Terraced)/i

        'Flat' # Default
      end
    end

    def extract_tenure
      tenure_div = @doc.css('div:contains("Tenure")').last

      if tenure_div
        tenure_text = tenure_div.parent.parent.parent.css('div').last.text.strip
        return tenure_text.gsub(/^Tenure:\s*/, '')
      end

      # Try to find it in features
      @doc.css('div.text-nowrap').each do |element|
        return ::Regexp.last_match(1) if element.text =~ /Tenure:\s*(.+)/i
      end

      'Leasehold' # Default
    end

    def extract_description
      desc_section = @doc.css('section.flex.flex-col.gap-5.pt-6').find do |section|
        section.css('h3').text.include?('Description')
      end

      if desc_section
        desc_section.css('div').text.strip
      else
        # Look for a large block of text that might be the description
        paragraphs = @doc.css('p, div').select do |el|
          el.text.length > 200 && !el.text.include?('script') && !el.text.include?('function')
        end

        paragraphs.first&.text&.strip || ''
      end
    end

    def extract_latitude
      map_element = @doc.css('div[data-controller="map"]').first
      map_element&.attr('data-map-lat-value')&.to_f || 0.0
    end

    def extract_longitude
      map_element = @doc.css('div[data-controller="map"]').first
      map_element&.attr('data-map-lon-value')&.to_f || 0.0
    end

    def extract_tags
      tags = []

      # Virtual tour
      tags << 'Virtual tour' if @doc.css('a[href*="youtu"]').any? || @doc.css('iframe[src*="youtu"]').any?

      tags
    end

    def extract_image_urls
      image_elements = @doc.css('a[href*="/gallery"] img')
      urls = image_elements.map do |img|
        img['src']
      end.uniq

      urls
    end

    def extract_postal_code
      # Try to find postal code in the page
      text = @doc.text
      return ::Regexp.last_match(1) if text =~ /([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})/

      # Check if it's in a specific element
      @doc.css('div:contains("Postal Code")').each do |el|
        return ::Regexp.last_match(1) if el.text =~ /([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})/
      end

      # Get it from og:image if it contains postcode
      og_image = @doc.css('meta[property="og:image"]').first&.attr('content')
      return ::Regexp.last_match(1) if og_image && og_image =~ /([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})/

      nil
    end

    def extract_energy_rating
      energy_div = @doc.css('div:contains("Energy Rating")').last

      if energy_div
        rating_text = energy_div.parent.parent.css('div').last.text.strip
        return ::Regexp.last_match(1) if rating_text =~ /([A-G])/
      end

      nil
    end

    def extract_import_url
      # Check for canonical URL
      canonical = @doc.css('link[rel="canonical"]').first&.attr('href')
      return canonical if canonical

      # Try to find the original listing URL
      listing_url = @doc.css('a:contains("View listing")').first&.attr('href')
      return listing_url if listing_url

      # Fallback to share URL
      share_url = @doc.css('input[value*="properties/"]').first&.attr('value')
      return share_url if share_url

      nil
    end

    def asset_attributes
      %w[
        title categories city city_search_key constructed_area count_bathrooms count_bedrooms
        count_garages count_toilets country description details discarded_at energy_performance
        energy_rating floor has_rental_listings has_sale_listings has_sold_transactions
        host_on_create is_ai_generated_realty_asset latitude longitude neighborhood
        neighborhood_search_key plot_area postal_code prop_state_key prop_type_key province
        ra_photos_count realty_asset_flags realty_asset_tags reference region
        rental_listings_count sale_listings_count sold_transactions_count street_address
        street_number year_construction
      ]
    end

    def listing_attributes
      %w[
        title description archived commission_cents commission_currency currency design_style
        details_of_rooms discarded_at extra_sale_details furnished hide_map highlighted
        host_on_create is_ai_generated_listing listing_pages_count listing_slug listing_tags
        main_video_url obscure_map page_section_listings_count position_in_list
        price_sale_current_cents price_sale_current_currency price_sale_original_cents
        price_sale_original_currency property_board_items_count publish_from publish_till
        reference related_urls reserved sale_listing_features sale_listing_flags
        sale_listing_gen_prompt service_charge_yearly_cents service_charge_yearly_currency
        sl_photos_count visible
      ]
    end
  end
end 