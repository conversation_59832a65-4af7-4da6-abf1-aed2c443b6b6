# frozen_string_literal: true

module Pasarelas
  class RightmovePasarela
    # much of this came from ScrapeItemFromRightmove
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      script_json = @realty_scraped_item.script_json
      return unless script_json.present?

      # If script_json is a string, try to parse it as JSON or Ruby hash string
      rightmove_json = nil
      if script_json.is_a?(String)
        begin
          # Try JSON first
          rightmove_json = JSON.parse(script_json)
        rescue JSON::ParserError
          begin
            # Try to eval Ruby hash string (with symbol keys)
            rightmove_json = eval(script_json)
          rescue Exception => e
            Rails.logger.error("Failed to parse script_json: #{e.message}")
            nil
          end
        end
      elsif script_json.is_a?(Hash)
        rightmove_json = script_json
      else
        Rails.logger.error("script_json is not a String or Hash: #{script_json.class}")
        nil
      end


      # rightmove_json = script_json

      # property_data = {}

      listing_data = map_property_to_listing_schema(rightmove_json)
      asset_data = map_property_to_asset_schema(rightmove_json)

      # property_data[:listing_data] = listing_data
      # property_data[:asset_data] = asset_data
      # property_data[:listing_image_urls]
      extracted_image_urls = rightmove_json['propertyData']['images'].map { |img| img['url'] }

      # property_data.stringify_keys!
      # property_data

      # Extract asset_attributes and listing_attributes
      # asset_attributes = data['propertyData'] || data[:propertyData]
      # listing_attributes = data['prices'] || data[:prices]

      # Save to extracted_asset_data and extracted_listing_data
      @realty_scraped_item.extracted_asset_data = asset_data || {}
      @realty_scraped_item.extracted_listing_data =  listing_data || {}
      @realty_scraped_item.extracted_image_urls = extracted_image_urls || []
      @realty_scraped_item.save!
    end

    private

    def map_property_to_asset_schema(property)
      property_data = property['propertyData']
      address = property_data['address'] || {}
      display_address = address['displayAddress'] || ''
      city = display_address.split(',').last&.strip || ''
      city_search_key = city.downcase.gsub(/\s+/, '-') rescue ''
      outcode = address['outcode'] || ''
      incode = address['incode'] || ''
      postal_code = (outcode.empty? && incode.empty?) ? '' : [outcode, incode].reject(&:empty?).join(' ')
      images = property_data['images'] || []
      tags = property_data['tags'] || []
      key_features = property_data['keyFeatures'] || []
      rooms = property_data['rooms'] || []
      description = property_data.dig('text', 'description') || ''
      page_title = property_data.dig('text', 'pageTitle') || ''
      latitude = property_data.dig('location', 'latitude')
      longitude = property_data.dig('location', 'longitude')
      epc_graphs = property_data['epcGraphs'] || []
      energy_rating = epc_graphs[0]&.dig('caption') == 'EPC' ? 'Available' : nil
      prop_type_key = property_data.dig('infoReelItems', 0, 'primaryText')&.downcase&.gsub(/\s+/, '-') || ''
      {
        'title' => page_title,
        'categories' => key_features.map { |f| { 'id' => f.downcase.gsub(/\s+/, '-'), 'name' => f } },
        'city' => city,
        'city_search_key' => city_search_key,
        'constructed_area' => 0.0,
        'count_bathrooms' => property_data['bathrooms'].to_f || 0.0,
        'count_bedrooms' => property_data['bedrooms'] || 0,
        'count_garages' => description.downcase.include?('garage') ? 1 : 0,
        'count_toilets' => 0,
        'country' => address['ukCountry']&.capitalize,
        'description' => description,
        'details' => rooms.map { |room| [room['name'], room] }.to_h,
        'discarded_at' => nil,
        'energy_performance' => nil,
        'energy_rating' => energy_rating,
        'floor' => nil,
        'has_rental_listings' => false,
        'has_sale_listings' => true,
        'has_sold_transactions' => false,
        'host_on_create' => 'unknown_host',
        'latitude' => latitude,
        'longitude' => longitude,
        'neighborhood' => nil,
        'neighborhood_search_key' => '',
        'plot_area' => 0.0,
        'postal_code' => postal_code,
        'prop_origin_key' => '', # Not directly available
        'prop_state_key' => 'new',
        'prop_type_key' => prop_type_key,
        'province' => address['ukCountry']&.capitalize,
        'ra_photos_count' => images.size,
        'realty_asset_flags' => 0,
        'realty_asset_tags' => tags,
        'reference' => property_data['id'],
        'region' => address['ukCountry']&.capitalize,
        'rental_listings_count' => 0,
        'sale_listings_count' => 1,
        'site_visitor_token' => nil,
        'sold_transactions_count' => 0,
        'street_address' => display_address,
        'street_number' => nil,
        'year_construction' => 0
      }
    end

    def map_property_to_listing_schema(property)
      property_data = property['propertyData']
      price_raw = property_data.dig('prices', 'primaryPrice')&.gsub(/[^0-9.]/, '')&.to_f || 0.0
      rooms = property_data['rooms'] || []
      tags = property_data['tags'] || []
      images = property_data['images'] || []
      key_features = property_data['keyFeatures'] || []
      description = property_data.dig('text', 'description') || ''
      page_title = property_data.dig('text', 'pageTitle') || ''
      {
        'title' => page_title,
        'description' => description,
        'archived' => property_data.dig('status', 'archived') || false,
        'commission_cents' => 0,
        'commission_currency' => 'GBP',
        'currency' => 'GBP',
        'design_style' => nil,
        'details_of_rooms' => rooms.map { |room| [room['name'], room] }.to_h,
        'discarded_at' => nil,
        'extra_sale_details' => {},
        'furnished' => false,
        'hide_map' => false,
        'highlighted' => property_data.dig('misInfo', 'premiumDisplay') || false,
        'host_on_create' => 'unknown_host',
        'is_ai_generated_listing' => false,
        'listing_pages_count' => 0,
        'listing_slug' => property_data['id'],
        'listing_tags' => tags,
        'main_video_url' => property_data.dig('virtualTours', 0, 'url'),
        'obscure_map' => false,
        'page_section_listings_count' => 0,
        'position_in_list' => nil,
        'price_sale_current_cents' => (price_raw * 100).to_i,
        'price_sale_current_currency' => 'GBP',
        'price_sale_original_cents' => (price_raw * 100).to_i,
        'price_sale_original_currency' => 'GBP',
        'property_board_items_count' => 0,
        'publish_from' => nil,
        'publish_till' => nil,
        'reference' => property_data['id'],
        'related_urls' => {},
        'reserved' => false,
        'sale_listing_features' => key_features.map { |feature| [feature.downcase.gsub(/\s+/, '-'), feature] }.to_h,
        'sale_listing_flags' => 0,
        'sale_listing_gen_prompt' => nil,
        'service_charge_yearly_cents' => property_data.dig('livingCosts', 'annualServiceCharge') ? (property_data.dig('livingCosts', 'annualServiceCharge') * 100).to_i : 0,
        'service_charge_yearly_currency' => 'GBP',
        'site_visitor_token' => nil,
        'sl_photos_count' => images.size,
        'visible' => property_data.dig('status', 'published').nil? ? true : property_data.dig('status', 'published')
      }
    end

    def asset_attributes
      %w[title categories city city_search_key constructed_area count_bathrooms count_bedrooms count_garages count_toilets
         country description details discarded_at energy_performance energy_rating floor has_rental_listings has_sale_listings has_sold_transactions host_on_create latitude longitude neighborhood neighborhood_search_key plot_area postal_code prop_origin_key prop_state_key prop_type_key province ra_photos_count realty_asset_flags realty_asset_tags reference region rental_listings_count sale_listings_count site_visitor_token sold_transactions_count street_address street_number year_construction]
    end

    def listing_attributes
      %w[title description archived commission_cents commission_currency currency design_style details_of_rooms discarded_at extra_sale_details furnished hide_map highlighted host_on_create is_ai_generated_listing listing_pages_count listing_slug listing_tags main_video_url obscure_map page_section_listings_count position_in_list price_sale_current_cents price_sale_current_currency price_sale_original_cents price_sale_original_currency property_board_items_count publish_from publish_till reference related_urls reserved sale_listing_features sale_listing_flags sale_listing_gen_prompt service_charge_yearly_cents
         service_charge_yearly_currency site_visitor_token sl_photos_count visible]
    end
  end
end
