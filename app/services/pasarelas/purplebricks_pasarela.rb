# frozen_string_literal: true

module <PERSON>sar<PERSON>s
  class PurplebricksPasarela
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      # For Purplebricks, we need to parse JSON content from full_content_before_js
      json_content = @realty_scraped_item.full_content_before_js
      return unless json_content.present?

      begin
        # Parse the JSON content
        purplebricks_data = JSON.parse(json_content)
      rescue JSON::ParserError => e
        Rails.logger.error("Failed to parse Purplebricks JSON: #{e.message}")
        return
      end

      # Transform the data to our schema
      transformed_data = transform_json(purplebricks_data)

      # Extract the different data components
      listing_data = transformed_data[:listing_data]
      asset_data = transformed_data[:asset_data]
      extracted_image_urls = transformed_data[:listing_image_urls] || []

      # Save the extracted data
      @realty_scraped_item.extracted_asset_data = asset_data || {}
      @realty_scraped_item.extracted_listing_data = listing_data || {}
      @realty_scraped_item.extracted_image_urls = extracted_image_urls || []
      @realty_scraped_item.save!

    rescue Exception => e
      Rails.logger.error("Error processing Purplebricks data: #{e.message}")
    end

    private

    def transform_json(data)
      transformed_data = {
        listing_data: {
          'archived' => !data['isLive'], # Assuming isLive false means archived
          'commission_cents' => 0,
          'commission_currency' => 'GBP',
          'currency' => 'GBP',
          'design_style' => nil,
          'details_of_rooms' => {},
          'discarded_at' => nil,
          'extra_sale_details' => {},
          'furnished' => false,
          'hide_map' => false,
          'highlighted' => false,
          'host_on_create' => 'unknown_host',
          'is_ai_generated_listing' => false,
          'listing_pages_count' => 0,
          'listing_slug' => data['id'],
          'listing_tags' => [],
          'main_video_url' => data['videoTourUrl'] || '',
          'obscure_map' => false,
          'page_section_listings_count' => 0,
          'position_in_list' => nil,
          'price_sale_current_cents' => (data['marketPrice'] * 100).to_i,
          'price_sale_current_currency' => 'GBP',
          'price_sale_original_cents' => (data['marketPrice'] * 100).to_i,
          'price_sale_original_currency' => 'GBP',
          'property_board_items_count' => 0,
          'publish_from' => nil,
          'publish_till' => nil,
          'reference' => data['id'],
          'related_urls' => {},
          'reserved' => false,
          'sale_listing_features' => data['starPoints'].each_with_index.with_object({}) { |(point, index), hash| hash[index + 1] = point },
          'sale_listing_flags' => 0,
          'sale_listing_gen_prompt' => nil,
          'service_charge_yearly_cents' => (data['tenureDetails']['serviceChargeAnnualAmount'] * 100).to_i,
          'service_charge_yearly_currency' => 'GBP',
          'sl_photos_count' => data['images'].count,
          'visible' => true
        },
        asset_data: {
          'categories' => data['starPoints'].each_with_index.map { |point, index| { 'id' => index + 1, 'name' => point } },
          'city' => data['location']['postTown'].capitalize,
          'city_search_key' => data['location']['postTown'].downcase,
          'constructed_area' => data['features']['propertyArea'].to_f,
          'count_bathrooms' => data['features']['bathrooms'].to_i,
          'count_bedrooms' => data['features']['bedrooms'].to_i,
          'count_garages' => data['features']['carSpaces'].to_i,
          'count_toilets' => 0,
          'country' => data['regionType'],
          'description' => data['description'],
          'details' => {},
          'discarded_at' => nil,
          'energy_performance' => nil,
          'energy_rating' => data['tenureDetails']['taxBand'],
          'floor' => nil,
          'has_rental_listings' => false,
          'has_sale_listings' => true,
          'has_sold_transactions' => false,
          'host_on_create' => 'unknown_host',
          'is_ai_generated_realty_asset' => false,
          'latitude' => data['latitude'],
          'longitude' => data['longitude'],
          'neighborhood' => data['location']['dependentLocality'],
          'neighborhood_search_key' => data['location']['dependentLocality'].downcase,
          'plot_area' => data['features']['landArea'].to_f,
          'postal_code' => data['postcode'],
          'prop_state_key' => data['isLive'] ? 'live' : 'not_live',
          'prop_type_key' => data['title'].downcase.include?('flat') ? 'flat' : 'unknown',
          'province' => nil,
          'ra_photos_count' => data['images'].count,
          'realty_asset_flags' => 0,
          'realty_asset_tags' => [],
          'reference' => data['id'],
          'region' => data['propertyExpertData']['region'],
          'rental_listings_count' => 0,
          'sale_listings_count' => 1,
          'sold_transactions_count' => 0,
          'street_address' => data['address'],
          'street_number' => nil,
          'title' => data['title'],
          'year_construction' => 0
        },
        listing_image_urls: data['images'].map { |image| image['url'] }
      }

      transformed_data
    end

    def asset_attributes
      %w[
        title categories city city_search_key constructed_area count_bathrooms count_bedrooms
        count_garages count_toilets country description details discarded_at energy_performance
        energy_rating floor has_rental_listings has_sale_listings has_sold_transactions
        host_on_create is_ai_generated_realty_asset latitude longitude neighborhood
        neighborhood_search_key plot_area postal_code prop_state_key prop_type_key province
        ra_photos_count realty_asset_flags realty_asset_tags reference region
        rental_listings_count sale_listings_count sold_transactions_count street_address
        street_number year_construction
      ]
    end

    def listing_attributes
      %w[
        title description archived commission_cents commission_currency currency design_style
        details_of_rooms discarded_at extra_sale_details furnished hide_map highlighted
        host_on_create is_ai_generated_listing listing_pages_count listing_slug listing_tags
        main_video_url obscure_map page_section_listings_count position_in_list
        price_sale_current_cents price_sale_current_currency price_sale_original_cents
        price_sale_original_currency property_board_items_count publish_from publish_till
        reference related_urls reserved sale_listing_features sale_listing_flags
        sale_listing_gen_prompt service_charge_yearly_cents service_charge_yearly_currency
        sl_photos_count visible
      ]
    end
  end
end 