# frozen_string_literal: true

module Pasarelas
  class IdealistaPasarela
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      # For Idealista, prefer JSON if present; otherwise parse whatever HTML we have
      html_content = @realty_scraped_item.full_content_before_js
      json_blob = @realty_scraped_item.script_json
      puts "IdealistaPasarela#call - HTML length: #{html_content&.length || 0}, JSON present?: #{json_blob.present?}"

      idealista_data = nil
      doc = nil

      if json_blob.present? && json_blob.is_a?(Hash)
        puts 'IdealistaPasarela#call - Using previously stored script_json hash.'
        idealista_data = json_blob
      elsif html_content.present?
        begin
          # Even if HTML is short, try to parse it
          doc = Nokogiri::HTML(html_content)
          idealista_data = extract_idealista_data(doc)
        rescue StandardError => e
          Rails.logger.error("IdealistaPasarela HTML parse error: #{e.message}")
          idealista_data = nil
        end
      end

      # Absolute fallback: build a minimal hash so downstream mapping produces non-empty hashes
      if idealista_data.nil?
        doc ||= (html_content.present? ? Nokogiri::HTML(html_content) : nil)
        idealista_data = build_minimal_fallback_from_html(doc, html_content)
      end

      # Initialize containers to ensure rescue/ensure blocks can reference them
      asset_data = {}
      listing_data = {}
      extracted_image_urls = []

      begin
        # If we only have HTML fallback, ensure a Nokogiri doc for image extraction
        doc ||= (html_content.present? ? Nokogiri::HTML(html_content) : nil)

        # Map the data to our schema (these methods return full hashes even with many nils)
        listing_data = map_property_to_listing_schema(idealista_data || {})
        asset_data = map_property_to_asset_schema(idealista_data || {})

        # Extract image URLs
        extracted_image_urls = extract_image_urls(idealista_data || {}, doc) rescue []

        # If mapping yielded empty hashes (unlikely), derive from minimal fallback
        if asset_data.blank? || listing_data.blank?
          minimal = build_minimal_fallback_from_html(doc, html_content)
          listing_data = map_property_to_listing_schema(minimal || {}) if listing_data.blank?
          asset_data = map_property_to_asset_schema(minimal || {}) if asset_data.blank?
          extracted_image_urls = extract_image_urls(minimal || {}, doc) if extracted_image_urls.blank?
        end

        # Save the extracted data directly to the database columns
        puts "Asset data: #{asset_data}"
        puts "Listing data: #{listing_data}"
        puts "Image URLs: #{extracted_image_urls}"

        @realty_scraped_item.extracted_asset_data = asset_data.presence || {'title' => extract_title_from_data(idealista_data || {})}
        @realty_scraped_item.extracted_listing_data = listing_data.presence || {'title' => extract_title_from_data(idealista_data || {})}
        @realty_scraped_item.extracted_image_urls = extracted_image_urls || []

        @realty_scraped_item.save!

        Rails.logger.info("Successfully processed Idealista data for scrape item #{@realty_scraped_item.id}")
      rescue JSON::ParserError => e
        Rails.logger.error("Failed to parse Idealista JSON: #{e.message}")
        # Still persist minimal structures to avoid upstream blank? checks failing
        @realty_scraped_item.update(
          extracted_asset_data: asset_data.presence || {'title' => extract_title_from_data(idealista_data || {})},
          extracted_listing_data: listing_data.presence || {'title' => extract_title_from_data(idealista_data || {})},
          extracted_image_urls: extracted_image_urls || []
        ) rescue nil
      rescue StandardError => e
        Rails.logger.error("Error processing Idealista data: #{e.message}\n#{e.backtrace.first(10).join('\n')}")
        # Do not early-return with nothing; ensure minimal hashes are present
        @realty_scraped_item.update(
          extracted_asset_data: asset_data.presence || {'title' => extract_title_from_data(idealista_data || {})},
          extracted_listing_data: listing_data.presence || {'title' => extract_title_from_data(idealista_data || {})},
          extracted_image_urls: extracted_image_urls || []
        ) rescue nil
      end
    end

    private


    # Build a minimal data structure from whatever HTML we have so mapping produces non-empty hashes
    def build_minimal_fallback_from_html(doc, html_content)
      begin
        doc ||= (html_content.present? ? Nokogiri::HTML(html_content) : nil)
        return {} unless doc

        # Reuse HTML extraction helpers to assemble a minimal structure
        {
          'title' => doc.css('h1').first&.text&.strip,
          'price' => extract_price_from_html(doc),
          'description' => extract_description_from_html(doc),
          'bedrooms' => extract_bedrooms_from_html(doc),
          'bathrooms' => extract_bathrooms_from_html(doc),
          'area' => extract_area_from_html(doc),
          'location' => {
            'address' => extract_location_from_html(doc)
          },
          'property_type' => extract_property_type_from_html(doc),
          'features' => extract_features_from_html(doc)
        }
      rescue StandardError => e
        Rails.logger.warn("IdealistaPasarela#build_minimal_fallback_from_html failed: #{e.message}")
        {}
      end
    end

    def extract_idealista_data(doc)
      # Try to find JSON data in script tags first
      json_data = extract_json_from_scripts(doc)

      if json_data
        Rails.logger.info("Found JSON data in scripts")
        return json_data
      else
        Rails.logger.info("No JSON data found, falling back to HTML extraction")
        # Fallback to HTML extraction
        html_data = extract_data_from_html(doc)
        Rails.logger.info("HTML extraction result: #{html_data}")
        return html_data
      end
    end

    def extract_json_from_scripts(doc)
      # 1) Look for JSON-LD first (Idealista commonly uses it)
      ld_scripts = doc.css('script[type="application/ld+json"]')
      ld_scripts.each do |script|
        begin
          parsed = JSON.parse(script.text)
          # JSON-LD can be an Array or a Hash
          candidates = parsed.is_a?(Array) ? parsed : [parsed]
          normalized = candidates.map { |obj| normalize_ld_json(obj) }.compact.find { |h| h.any? }
          return normalized if normalized.present?
        rescue JSON::ParserError
          next
        end
      end

      # 2) Look for JSON in script tags with generic application/json
      script_tags = doc.css('script[type="application/json"]')
      script_tags.each do |script|
        begin
          json_content = JSON.parse(script.text)
          return json_content if json_content && json_content.is_a?(Hash)
        rescue JSON::ParserError
          next
        end
      end

      # 3) Look for window.__INITIAL_STATE__ or dataLayer style globals
      script_tags = doc.css('script')
      script_tags.each do |script|
        script_text = script.text

        if script_text.include?('window.__INITIAL_STATE__')
          json_match = script_text.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/)
          if json_match
            begin
              return JSON.parse(json_match[1])
            rescue JSON::ParserError
              # ignore and continue
            end
          end
        end

        if script_text.include?('window.dataLayer') || script_text.include?('window.utag_data')
          json_match = script_text.match(/(window\.dataLayer\s*=\s*\[.*?\]|window\.utag_data\s*=\s*{.*?})/)
          if json_match
            begin
              clean_json = json_match[1].gsub(/^window\.(dataLayer|utag_data)\s*=\s*/, '')
              return JSON.parse(clean_json)
            rescue JSON::ParserError
              # ignore and continue
            end
          end
        end
      end

      nil
    end

    # Normalize JSON-LD graph entries into a flat schema we can map
    def normalize_ld_json(obj)
      return {} unless obj.is_a?(Hash)

      # If graph, try to pull Offer and Place/PostalAddress details
      if obj['@graph'].is_a?(Array)
        candidates = obj['@graph']
        offer = candidates.find { |n| n['@type'].to_s.downcase.include?('offer') }
        place = candidates.find { |n| n['@type'].to_s.downcase.include?('place') || n['@type'].to_s.downcase.include?('residence') }
        address = (place && (place['address'] || place.dig('geo', 'address'))) || candidates.find { |n| n['@type'].to_s.downcase.include?('postaladdress') }

        normalized = {}
        normalized['title'] = place && (place['name'] || place['description'])
        normalized['description'] = place && place['description']
        normalized['price'] = offer && (offer['price'] || offer.dig('priceSpecification', 'price'))
        normalized['images'] = place && (place['image'] || [])
        if address
          normalized['location'] = {
            'address' => [address['streetAddress'], address['addressLocality'], address['postalCode']].compact.join(', '),
            'city' => address['addressLocality'],
            'province' => address['addressRegion'],
            'postal_code' => address['postalCode']
          }
        end
        return normalized
      end

      # If it looks like a single Offer or Product or Residence
      normalized = {}
      normalized['title'] = obj['name'] || obj['description']
      normalized['description'] = obj['description']
      normalized['price'] = obj['price'] || obj.dig('offers', 'price')
      normalized['images'] = obj['image'] || []

      if (addr = obj['address']).is_a?(Hash)
        normalized['location'] = {
          'address' => [addr['streetAddress'], addr['addressLocality'], addr['postalCode']].compact.join(', '),
          'city' => addr['addressLocality'],
          'province' => addr['addressRegion'],
          'postal_code' => addr['postalCode']
        }
      end
      normalized
    end


    def extract_data_from_html(doc)
      # Fallback HTML extraction
      location_text = extract_location_from_html(doc)

      {
        'title' => doc.css('h1').first&.text&.strip,
        'price' => extract_price_from_html(doc),
        'description' => extract_description_from_html(doc),
        'bedrooms' => extract_bedrooms_from_html(doc),
        'bathrooms' => extract_bathrooms_from_html(doc),
        'area' => extract_area_from_html(doc),
        'location' => {
          'address' => location_text,
          'city' => extract_city_from_location_text(location_text)
        },
        'property_type' => extract_property_type_from_html(doc),
        'features' => extract_features_from_html(doc)
      }
    end

    def map_property_to_asset_schema(idealista_data)
      # Extract location data
      location_data = idealista_data['location'] || idealista_data.dig('property', 'location') || {}

      {
        'title' => extract_title_from_data(idealista_data),
        'categories' => extract_categories(idealista_data),
        'city' => extract_city(location_data),
        'city_search_key' => extract_city(location_data)&.downcase&.gsub(/\s+/, '-') || '',
        'constructed_area' => extract_area_value(idealista_data),
        'count_bathrooms' => extract_bathroom_count(idealista_data),
        'count_bedrooms' => extract_bedroom_count(idealista_data),
        'count_garages' => extract_garage_count(idealista_data),
        'count_toilets' => 0, # Not typically available in Idealista data
        'country' => 'ES', # Idealista is primarily Spanish
        'description' => extract_description_from_data(idealista_data),
        'details' => extract_property_details(idealista_data),
        'discarded_at' => nil,
        'energy_performance' => extract_energy_performance(idealista_data),
        'energy_rating' => extract_energy_rating(idealista_data),
        'floor' => extract_floor(idealista_data),
        'has_rental_listings' => false,
        'has_sale_listings' => true,
        'has_sold_transactions' => false,
        'host_on_create' => 'idealista.com',
        'is_ai_generated_realty_asset' => false,
        'latitude' => extract_latitude(location_data),
        'longitude' => extract_longitude(location_data),
        'neighborhood' => extract_neighborhood(location_data),
        'plot_area' => extract_plot_area(idealista_data),
        'postal_code' => extract_postal_code(location_data),
        'prop_type' => extract_property_type(idealista_data),
        'prop_type_key' => extract_property_type(idealista_data)&.downcase&.gsub(/\s+/, '-') || '',
        'province' => extract_province(location_data),
        'ra_photos_count' => extract_photo_count(idealista_data),
        'realty_asset_flags' => 0,
        'realty_asset_tags' => extract_tags(idealista_data),
        'reference' => extract_reference(idealista_data),
        'region' => extract_region(location_data),
        'rental_listings_count' => 0,
        'sale_listings_count' => 1,
        'sold_transactions_count' => 0,
        'street_address' => extract_street_address(location_data),
        'street_number' => extract_street_number(location_data),
        'year_construction' => extract_year_construction(idealista_data)
      }
    end

    def map_property_to_listing_schema(idealista_data)
      price_data = extract_price_data(idealista_data)

      {
        'agency_name' => extract_agency_name(idealista_data),
        'agency_reference' => extract_agency_reference(idealista_data),
        'area_unit' => 'm²',
        'constructed_area' => extract_area_value(idealista_data),
        'count_bathrooms' => extract_bathroom_count(idealista_data),
        'count_bedrooms' => extract_bedroom_count(idealista_data),
        'count_garages' => extract_garage_count(idealista_data),
        'count_toilets' => 0,
        'description' => extract_description_from_data(idealista_data),
        'discarded_at' => nil,
        'energy_performance' => extract_energy_performance(idealista_data),
        'energy_rating' => extract_energy_rating(idealista_data),
        'floor' => extract_floor(idealista_data),
        'has_virtual_tour' => extract_virtual_tour_status(idealista_data),
        'is_ai_generated_listing' => false,
        'listing_pages_count' => 0,
        'listing_slug' => extract_listing_slug(idealista_data),
        'listing_tags' => extract_listing_tags(idealista_data),
        'main_video_url' => extract_video_url(idealista_data),
        'obscure_map' => false,
        'page_section_listings_count' => 0,
        'position_in_list' => nil,
        'price_sale_current_cents' => price_data[:current_cents],
        'price_sale_current_currency' => price_data[:currency],
        'price_sale_original_cents' => price_data[:original_cents],
        'price_sale_original_currency' => price_data[:currency],
        'property_board_items_count' => 0,
        'property_reference' => extract_reference(idealista_data),
        'sale_listing_flags' => 0,
        'title' => extract_title_from_data(idealista_data),
        'visible' => true,
        'year_construction' => extract_year_construction(idealista_data)
      }
    end

    def extract_image_urls(idealista_data, doc)
      images = []

      # Try to get images from JSON data first
      if idealista_data['images'].is_a?(Array)
        images = idealista_data['images']
      elsif idealista_data.dig('property', 'images').is_a?(Array)
        images = idealista_data.dig('property', 'images')
      else
        # Fallback to HTML extraction
        doc.css('img').each do |img|
          src = img['src'] || img['data-src'] || img['data-original']
          if src && (src.include?('idealista') || src.start_with?('http'))
            images << src
          end
        end
      end

      # Clean and validate image URLs
      images.compact.uniq.select { |url| url.is_a?(String) && url.length > 10 }
    end

    # Helper methods for data extraction
    def extract_title_from_data(data)
      data.dig('property', 'title') ||
      data.dig('listing', 'title') ||
      data['title']
    end

    def extract_description_from_data(data)
      data.dig('property', 'description') ||
      data.dig('listing', 'description') ||
      data['description']
    end

    def extract_categories(data)
      property_type = extract_property_type(data)
      property_type ? [property_type] : []
    end

    def extract_city(location_data)
      location_data['city'] || location_data['municipality'] || location_data['localidad']
    end

    def extract_area_value(data)
      area = data['area'] || data.dig('property', 'area') || data.dig('surface', 'built')
      area.is_a?(Numeric) ? area.to_f : area.to_f rescue 0.0
    end

    def extract_bathroom_count(data)
      bathrooms = data['bathrooms'] || data.dig('property', 'bathrooms') || data.dig('rooms', 'bathrooms')
      bathrooms.is_a?(Numeric) ? bathrooms.to_f : bathrooms.to_f rescue 0.0
    end

    def extract_bedroom_count(data)
      bedrooms = data['bedrooms'] || data.dig('property', 'bedrooms') || data.dig('rooms', 'bedrooms')
      bedrooms.is_a?(Numeric) ? bedrooms.to_i : bedrooms.to_i rescue 0
    end

    def extract_garage_count(data)
      garages = data['garages'] || data.dig('property', 'garages') || data.dig('parking', 'spaces')
      garages.is_a?(Numeric) ? garages.to_i : garages.to_i rescue 0
    end

    def extract_property_details(data)
      details = {}

      # Extract various property details
      if data['features'].is_a?(Array)
        data['features'].each_with_index do |feature, index|
          details["feature_#{index}"] = feature
        end
      end

      if data['characteristics'].is_a?(Hash)
        details.merge!(data['characteristics'])
      end

      details
    end

    def extract_energy_performance(data)
      data.dig('energy', 'performance') ||
      data.dig('energy_certificate', 'performance') ||
      data.dig('property', 'energy', 'performance')
    end

    def extract_energy_rating(data)
      data.dig('energy', 'rating') ||
      data.dig('energy_certificate', 'rating') ||
      data.dig('property', 'energy', 'rating')
    end

    def extract_floor(data)
      data['floor'] || data.dig('property', 'floor') || data.dig('location', 'floor')
    end

    def extract_latitude(location_data)
      lat = location_data['latitude'] || location_data['lat'] || location_data.dig('coordinates', 'latitude')
      lat.is_a?(Numeric) ? lat.to_f : lat.to_f rescue nil
    end

    def extract_longitude(location_data)
      lng = location_data['longitude'] || location_data['lng'] || location_data['lon'] || location_data.dig('coordinates', 'longitude')
      lng.is_a?(Numeric) ? lng.to_f : lng.to_f rescue nil
    end

    def extract_neighborhood(location_data)
      location_data['neighborhood'] || location_data['district'] || location_data['barrio']
    end

    def extract_plot_area(data)
      plot = data.dig('surface', 'plot') || data.dig('area', 'plot')
      plot.is_a?(Numeric) ? plot.to_f : plot.to_f rescue 0.0
    end

    def extract_postal_code(location_data)
      location_data['postal_code'] || location_data['zip_code'] || location_data['cp']
    end

    def extract_property_type(data)
      data['property_type'] ||
      data.dig('property', 'property_type') ||
      data.dig('property', 'type') ||
      data['type']
    end

    def extract_province(location_data)
      location_data['province'] || location_data['region'] || location_data['provincia']
    end

    def extract_photo_count(data)
      if data['images'].is_a?(Array)
        data['images'].length
      elsif data.dig('property', 'images').is_a?(Array)
        data.dig('property', 'images').length
      else
        0
      end
    end

    def extract_tags(data)
      tags = []
      tags << data['property_type'] if data['property_type']
      tags += data['features'] if data['features'].is_a?(Array)
      tags.compact.uniq
    end

    def extract_reference(data)
      data['reference'] || data['id'] || data.dig('property', 'reference') || data.dig('property', 'id')
    end

    def extract_region(location_data)
      location_data['region'] || location_data['autonomous_community'] || location_data['comunidad_autonoma']
    end

    def extract_street_address(location_data)
      location_data['address'] || location_data['street_address'] || location_data['direccion']
    end

    def extract_street_number(location_data)
      location_data['street_number'] || location_data['number'] || location_data['numero']
    end

    def extract_year_construction(data)
      year = data['year_built'] || data.dig('property', 'year_built') || data.dig('construction', 'year')
      year.is_a?(Numeric) ? year.to_i : year.to_i rescue nil
    end

    def extract_price_data(data)
      price = data['price'] || data.dig('property', 'price')

      if price.is_a?(Hash)
        amount = price['amount'] || price['value']
        currency = price['currency'] || 'EUR'
      else
        amount = price
        currency = 'EUR'
      end

      # Convert to cents
      cents = amount.is_a?(Numeric) ? (amount * 100).to_i : amount.to_i * 100 rescue 0

      {
        current_cents: cents,
        original_cents: cents,
        currency: currency
      }
    end

    def extract_agency_name(data)
      data.dig('agency', 'name') || data.dig('contact', 'agency') || 'Unknown Agency'
    end

    def extract_agency_reference(data)
      data.dig('agency', 'reference') || data.dig('contact', 'reference')
    end

    def extract_virtual_tour_status(data)
      !!(data['virtual_tour'] || data.dig('media', 'virtual_tour'))
    end

    def extract_listing_slug(data)
      reference = extract_reference(data)
      reference ? reference.to_s.downcase.gsub(/[^a-z0-9]/, '-') : nil
    end

    def extract_listing_tags(data)
      extract_tags(data)
    end

    def extract_video_url(data)
      data.dig('media', 'video') || data.dig('videos', 0, 'url')
    end

    # HTML extraction fallback methods
    def extract_price_from_html(doc)
      price_element = doc.css('.price, .precio, [class*="price"], [class*="precio"]').first
      price_text = price_element&.text&.strip
      return nil unless price_text

      # Extract numeric value from price text
      price_match = price_text.match(/[\d.,]+/)
      price_match ? price_match[0].gsub(',', '').to_f : 0.0
    end

    def extract_description_from_html(doc)
      desc_element = doc.css('.description, .descripcion, [class*="description"], [class*="descripcion"]').first
      desc_element&.text&.strip
    end

    def extract_bedrooms_from_html(doc)
      bedroom_text = doc.text
      bedroom_match = bedroom_text.match(/(\d+)\s*(habitacion|dormitor|bedroom)/i)
      bedroom_match ? bedroom_match[1].to_i : 0
    end

    def extract_bathrooms_from_html(doc)
      bathroom_text = doc.text
      bathroom_match = bathroom_text.match(/(\d+)\s*(baño|bathroom)/i)
      bathroom_match ? bathroom_match[1].to_i : 0
    end

    def extract_area_from_html(doc)
      area_text = doc.text
      area_match = area_text.match(/(\d+)\s*m[²2]/i)
      area_match ? area_match[1].to_i : 0
    end

    def extract_location_from_html(doc)
      location_element = doc.css('.location, .ubicacion, [class*="location"], [class*="ubicacion"]').first
      location_element&.text&.strip
    end

    def extract_property_type_from_html(doc)
      # Look for property type indicators
      type_text = doc.text
      if type_text.match(/piso|apartamento/i)
        'Apartment'
      elsif type_text.match(/casa|chalet/i)
        'House'
      elsif type_text.match(/local|oficina/i)
        'Commercial'
      else
        'Unknown'
      end
    end

    def extract_features_from_html(doc)
      features = []
      feature_elements = doc.css('.features, .caracteristicas, [class*="feature"], [class*="caracteristica"]')
      feature_elements.each do |element|
        features << element.text.strip if element.text.present?
      end
      features
    end

    def extract_city_from_location_text(location_text)
      return nil unless location_text

      # Try to extract city from location text like "Barcelona, 08001" or "Calle Mayor, Madrid"
      parts = location_text.split(',').map(&:strip)
      if parts.length >= 2
        # If the last part looks like a postal code, take the second to last
        if parts.last.match?(/^\d{5}$/)
          parts[-2]
        else
          parts.last
        end
      else
        parts.first
      end
    end
  end
end
