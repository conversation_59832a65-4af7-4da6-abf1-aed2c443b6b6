require 'logger'
require 'httparty'
require 'json'

module RealtyPunts
  # 16 june 2025: the idea of this class is to call to the backend on the server
  # so that methods can be called from my local desktop but the data
  # gets created on the server
  # Service class to handle dossier and asset creation
  class RealtyPuntCreator
    def initialize(api_prefix = nil, logger = Logger.new(STDOUT))
      @api_prefix = api_prefix
      @logger = logger
    end

    def create_realty_game(input_file = 'db/realty_punts/latest.json')
      @logger.info("Starting realty game creation from file: #{input_file}")
      input_data = read_and_validate_input(input_file)
      property_urls = input_data['property_urls'] || []
      validate_property_urls(property_urls)

      @logger.info("Found #{property_urls.size} property URL(s)")
      realty_game_id = create_initial_game(input_data, property_urls.first)
      add_remaining_listings(input_data, property_urls.drop(1), realty_game_id)

      @logger.info("All listings processed. Realty game ID: #{realty_game_id}")
      realty_game_id
    end

    # 03 july 2025: this method is used to create a realty game with pre-scraped content
    # This is what is currently being used in the rake task
    # to create a realty game and add listings to it
    def create_realty_game_with_pre_scraped_content(input_file = 'db/realty_punts/latest.json')
      @logger.info("Starting realty game creation with pre-scraped content from file: #{input_file}")
      input_data = read_and_validate_input(input_file)
      property_urls = input_data['property_urls'] || []
      validate_property_urls(property_urls)

      @logger.info("Found #{property_urls.size} property URL(s)")
      
      # Handle both old format (array of strings) and new format (array of objects)
      first_property = property_urls.first
      first_url = extract_url_from_property(first_property)
      
      # Log contextual descriptions if available
      log_property_details(property_urls)
      
      realty_game_id = create_initial_game_with_pre_scraped(input_data, first_url)
      add_remaining_pre_scraped_listings(input_data, property_urls.drop(1), realty_game_id)

      @logger.info("All pre-scraped listings processed. Realty game ID: #{realty_game_id}")
      realty_game_id
    end

    def create_dossier_from_url(retrieval_end_point, retrieval_portal)
      response = make_post_request(
        "#{@api_prefix}/dossiers_mgmt/dossier_from_url",
        {
          retrieval_end_point: retrieval_end_point,
          retrieval_portal: retrieval_portal
        }
      )
      JSON.parse(response.body)['realty_dossier']['id']
    end

    def add_asset_to_dossier(dossier_id, retrieval_end_point, retrieval_portal)
      response = make_put_request(
        "#{@api_prefix}/dossiers_mgmt/add_asset_from_url",
        {
          dossier_id: dossier_id,
          retrieval_end_point: retrieval_end_point,
          retrieval_portal: retrieval_portal
        }
      )
      JSON.parse(response.body)
    end

    private

    def read_and_validate_input(input_file)
      unless File.exist?(input_file)
        @logger.error("Input file not found: #{input_file}")
        raise "Input file not found: #{input_file}"
      end

      @logger.info("Reading input file: #{input_file}")
      input_data = JSON.parse(File.read(input_file))
      input_data['api_prefix'] ||= 'https://be-medo.propertywebbuilder.com/api_mgmt/v4'
      input_data
    end

    def validate_property_urls(property_urls)
      if property_urls.empty?
        @logger.error('No property URLs found in input file')
        raise 'No property URLs found in input file'
      end
    end

    def determine_retrieval_portal_from_url(url)
      portal = case url
      when /buenavistahomes\.eu/
        'buenavista'
      when /onthemarket\.com/
        'onthemarket'
      when /zoopla\.co\.uk/
        'zoopla'
      when /rightmove\.co\.uk/
        'rightmove'
      when /purplebricks\.co\.uk/
        'purplebricks'
      else
        @logger.warn("Unknown retrieval portal for URL: #{url}")
        'unknown'
      end
      @logger.info("Detected portal '#{portal}' for URL: #{url}")
      portal
    end

    def make_post_request(url, body)
      response = HTTParty.post(
        url,
        body: body.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        }
      )
      validate_response(response, "Failed to perform POST request to #{url}")
      response
    end

    def make_put_request(url, body)
      response = HTTParty.put(
        url,
        body: body.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        }
      )
      validate_response(response, "Failed to perform PUT request to #{url}")
      response
    end

    def validate_response(response, error_message)
      return if response.success?

      @logger.error("#{error_message}: #{response.body}")
      raise "#{error_message}: #{response.body}"
    end

    def create_initial_game(input_data, first_url)
      retrieval_portal = determine_retrieval_portal_from_url(first_url)
      @logger.info("Creating initial game with first listing: #{first_url} (Portal: #{retrieval_portal})")

      init_response = make_post_request(
        "#{input_data['api_prefix']}/realty_games_mgmt/init_game_with_listing",
        {
          realty_game_slug: input_data['realty_game_slug'] || 'regular-game',
          vendor_name: input_data['vendor_name'],
          game_title: input_data['game_title'],
          game_description: input_data['game_description'],
          game_bg_image_url: input_data['background_image'],
          scoot_subdomain: input_data['scoot_subdomain'],
          retrieval_portal: retrieval_portal,
          retrieval_end_point: first_url
        }
      )

      realty_game_id = JSON.parse(init_response.body)['realty_game_id']
      @logger.info("Created realty game ID: #{realty_game_id} with first listing")
      realty_game_id
    end

    def add_remaining_listings(input_data, property_urls, realty_game_id)
      if property_urls.any?
        @logger.info('Sleeping for 30 seconds before adding more listings...')
        sleep 30
      end

      property_urls.each_with_index do |url, index|
        retrieval_portal = determine_retrieval_portal_from_url(url)
        @logger.info("Adding listing #{index + 2}/#{property_urls.size + 1}: #{url} (Portal: #{retrieval_portal})")

        response = make_post_request(
          "#{input_data['api_prefix']}/realty_games_mgmt/add_listing_to_game",
          {
            realty_game_id: realty_game_id,
            retrieval_portal: retrieval_portal,
            retrieval_end_point: url
          }
        )

        @logger.info("Successfully added listing: #{url}")
        @logger.info('Sleeping for another 30 seconds...')
        sleep 30
      end
    end

    def create_initial_game_with_pre_scraped(input_data, first_url)
      retrieval_portal = determine_retrieval_portal_from_url(first_url)
      @logger.info("Pre-scraping and creating initial game with first listing: #{first_url} (Portal: #{retrieval_portal})")

      first_scrape_item_data = create_and_serialize_scrape_item(first_url, retrieval_portal)

      init_response = make_post_request(
        "#{input_data['api_prefix']}/realty_games_mgmt/init_game_with_pre_scraped_listing",
        {
          realty_game_slug: input_data['realty_game_slug'] || 'regular-game',
          vendor_name: input_data['vendor_name'],
          game_title: input_data['game_title'],
          game_description: input_data['game_description'],
          game_bg_image_url: input_data['background_image'],
          scoot_subdomain: input_data['scoot_subdomain'],
          retrieval_portal: retrieval_portal,
          retrieval_end_point: first_url,
          scrape_item: first_scrape_item_data
        }
      )

      realty_game_id = JSON.parse(init_response.body)['realty_game_id']
      @logger.info("Created realty game ID: #{realty_game_id} with pre-scraped first listing")
      realty_game_id
    end

    def add_remaining_pre_scraped_listings(input_data, property_urls, realty_game_id)
      if property_urls.any?
        @logger.info('Sleeping for 30 seconds before adding more pre-scraped listings...')
        sleep 30
      end

      property_urls.each_with_index do |property, index|
        url = extract_url_from_property(property)
        contextual_description = extract_description_from_property(property)
        
        retrieval_portal = determine_retrieval_portal_from_url(url)
        @logger.info("Adding pre-scraped listing #{index + 2}/#{property_urls.size + 1}: #{url} (Portal: #{retrieval_portal})")
        @logger.info("Context: #{contextual_description}") if contextual_description

        scrape_item_data = create_and_serialize_scrape_item(url, retrieval_portal)
        response = make_post_request(
          "#{input_data['api_prefix']}/realty_games_mgmt/add_pre_scraped_listing_to_game",
          {
            realty_game_id: realty_game_id,
            retrieval_portal: retrieval_portal,
            retrieval_end_point: url,
            scrape_item: scrape_item_data
          }
        )

        @logger.info("Successfully added pre-scraped listing: #{url}")
        @logger.info('Sleeping for another 30 seconds...')
        sleep 30
      end
    end

    def create_and_serialize_scrape_item(url, portal)
      @logger.info("Creating scrape item locally for: #{url} (Portal: #{portal})")
      scrape_item = create_scrape_item_locally(url, portal)
      serialize_scrape_item(scrape_item)
    end

    # Creates a scrape item locally using the same logic as RealtyGameListingCreator
    def create_scrape_item_locally(url, portal)
      puts "🔍 Really Creating scrape item locally for: #{url}"

      # Use the same portal configuration as RealtyGameListingCreator
      portal_config = {
        'buenavista' => {
          scrape_class: 'ScrapeItemFromBuenavista',
          connector: 'ScraperConnectors::Json',
          method: :find_or_create_for_h2c_buenavista,
          include_trailing_slash: false
        },
        'onthemarket' => {
          scrape_class: 'ScrapeItemFromOtm',
          connector: 'ScraperConnectors::Regular',
          method: :find_or_create_for_h2c_onthemarket,
          include_trailing_slash: true
        },
        'zoopla' => {
          scrape_class: 'ScrapeItem',
          connector: 'ScraperConnectors::LocalPlaywright',
          method: :find_or_create_for_h2c,
          include_trailing_slash: false
        },
        'rightmove' => {
          scrape_class: 'ScrapeItemFromRightmove',
          connector: 'ScraperConnectors::LocalPlaywright',
          method: :find_or_create_for_h2c_rightmove,
          include_trailing_slash: false
        },
        'purplebricks' => {
          scrape_class: 'ScrapeItemFromPurplebricks',
          connector: 'ScraperConnectors::Purplebricks',
          method: :find_or_create_for_h2c_purplebricks,
          include_trailing_slash: false
        }
      }

      p_config = portal_config[portal]
      raise "Unknown portal: #{portal}" unless p_config

      scrape_class = p_config[:scrape_class].constantize
      scrape_item = scrape_class.send(p_config[:method], url)

      puts "🔍 retrieve_and_set_content_object for: #{p_config}"

      scrape_item.retrieve_and_set_content_object(
        p_config[:connector],
        include_trailing_slash: p_config[:include_trailing_slash],
        force_retrieval: false
      )

      # Validation: ensure we actually scraped something meaningful
      if (scrape_item.full_content_before_js.nil? || scrape_item.full_content_before_js.strip.empty?) &&
         (scrape_item.script_json.nil? || (scrape_item.script_json.respond_to?(:empty?) && scrape_item.script_json.empty?))
        error_msg = "Scraping failed for #{url}: No HTML or JSON content was retrieved."
        puts "❌ #{error_msg}"
        @logger.error(error_msg) if @logger
        raise error_msg
      end

      puts '✅ Successfully created scrape item locally'
      scrape_item
    end

    # Serializes a scrape item to a hash that can be sent via JSON
    def serialize_scrape_item(scrape_item)
      {
        scrape_class: scrape_item.class.name,
        scrapable_url: scrape_item.scrapable_url,
        scrape_unique_url: scrape_item.scrape_unique_url,
        full_content_before_js: scrape_item.full_content_before_js,
        full_content_after_js: scrape_item.full_content_after_js,
        title: scrape_item.title,
        description: scrape_item.description,
        page_locale_code: scrape_item.page_locale_code,
        is_valid_scrape: scrape_item.is_valid_scrape,
        content_is_html: scrape_item.content_is_html,
        content_is_json: scrape_item.content_is_json,
        content_is_xml: scrape_item.content_is_xml,
        all_page_images: scrape_item.all_page_images,
        script_json: begin
          json_data = scrape_item.script_json
          json_data.is_a?(String) ? JSON.parse(json_data) : json_data
        rescue JSON::ParserError
          nil # Handle invalid JSON strings gracefully
        end,
        # Portal-specific flags
        scrape_is_buenavista: scrape_item.respond_to?(:scrape_is_buenavista) ? scrape_item.scrape_is_buenavista : false,
        scrape_is_onthemarket: scrape_item.respond_to?(:scrape_is_onthemarket) ? scrape_item.scrape_is_onthemarket : false,
        scrape_is_zoopla: scrape_item.respond_to?(:scrape_is_zoopla) ? scrape_item.scrape_is_zoopla : false,
        scrape_is_rightmove: scrape_item.respond_to?(:scrape_is_rightmove) ? scrape_item.scrape_is_rightmove : false,
        scrape_is_purplebricks: scrape_item.respond_to?(:scrape_is_purplebricks) ? scrape_item.scrape_is_purplebricks : false
      }
    end

    # Helper method to extract URL from property (handles both string and object formats)
    def extract_url_from_property(property)
      if property.is_a?(Hash)
        property['url']
      else
        property
      end
    end

    # Helper method to extract contextual description from property object
    def extract_description_from_property(property)
      if property.is_a?(Hash)
        property['contextual_description']
      else
        nil
      end
    end

    # Helper method to log property details with contextual descriptions
    def log_property_details(property_urls)
      property_urls.each_with_index do |property, index|
        url = extract_url_from_property(property)
        description = extract_description_from_property(property)
        
        if description
          @logger.info("Property #{index + 1}: #{url} - #{description}")
        else
          @logger.info("Property #{index + 1}: #{url}")
        end
      end
    end
  end
end
