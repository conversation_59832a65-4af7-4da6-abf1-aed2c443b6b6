module RealtyPunts
  # app/services/game_results_calculator.rb
  # older - superceeded by GameSessionResultsCalculator
  class GameResultsCalculator
    # A simple class to hold the result structure
    Result = Struct.new(:success?, :data, :error_message, keyword_init: true)

    def initialize(game_session_id)
      @game_session_id = game_session_id
      @player_estimates = PriceEstimate.where(
        "estimate_details->>'game_session_id' = ?", @game_session_id
      ).order(Arel.sql("estimate_details->>'property_index' ASC"))
    end

    def call
      return Result.new(success?: false, error_message: 'Game session not found or has no estimates.') if @player_estimates.empty?

      player_results = calculate_player_overall_performance
      comparison_summary = calculate_comparison_summary
      Result.new(
        success?: true,
        data: {
          player_results: player_results,
          comparison_summary: comparison_summary
        }
      )
    end

    private

    def calculate_player_overall_performance
      total_score = @player_estimates.sum { |e| e.estimate_details['game_score'].to_i }
      max_possible_score = @player_estimates.count * 100 # Assuming 100 is the max score per question
      session_date = @player_estimates.first.created_at

      {
        total_score: total_score,
        max_possible_score: max_possible_score,
        performance_rating: get_performance_rating(total_score, max_possible_score),
        session_date: session_date,
        game_results: @player_estimates.as_json # This contains the detailed breakdown for the player's table
      }
    end

    def get_performance_rating(score, max_score)
      percentage = max_score > 0 ? (score.to_f / max_score * 100).round : 0

      case percentage
      when 90..100
        { rating: 'Excellent!', icon: 'military_tech', color: 'positive' }
      when 70..89
        { rating: 'Great Job!', icon: 'emoji_events', color: 'positive' }
      when 50..69
        { rating: 'Good Effort', icon: 'thumb_up', color: 'info' }
      when 30..49
        { rating: 'Not Bad', icon: 'sentiment_satisfied', color: 'warning' }
      else
        { rating: 'Needs Improvement', icon: 'sentiment_dissatisfied', color: 'negative' }
      end
    end

    def calculate_comparison_summary
      @player_estimates.map do |player_estimate|
        listing_uuid = player_estimate.listing_uuid
        all_estimates_for_property = PriceEstimate.where(listing_uuid: listing_uuid)

        # Skip if no other estimates are available for some reason
        next if all_estimates_for_property.empty?

        # Calculate average guess
        total_guess_value = all_estimates_for_property.sum(:estimated_price_cents)
        average_guess_cents = total_guess_value / all_estimates_for_property.count

        # Calculate player's ranking
        sorted_estimates = all_estimates_for_property.sort_by { |e| -e.estimate_details['game_score'].to_i }
        rank = sorted_estimates.find_index { |e| e.uuid == player_estimate.uuid } + 1

        result = {
          property_title: player_estimate.estimate_title,
          property_vicinity: player_estimate.estimate_vicinity,
          your_guess_cents: player_estimate.estimated_price_cents,
          your_guess_formatted: format_currency(player_estimate.estimated_price_cents, player_estimate.estimate_currency),
          average_guess_cents: average_guess_cents,
          average_guess_formatted: format_currency(average_guess_cents, player_estimate.estimate_currency),
          actual_price_cents: player_estimate.price_at_time_of_estimate_cents,
          actual_price_formatted: format_currency(player_estimate.price_at_time_of_estimate_cents, player_estimate.estimate_currency),
          currency: player_estimate.estimate_currency,
          ranking: get_ranking_details(rank, all_estimates_for_property.count)
        }

        # Add property_url if present
        result[:property_url] = player_estimate.listing.listing_display_url if player_estimate.scoot.should_show_out_links && player_estimate.listing&.listing_display_url&.present?
        result
      end.compact
    end

    def get_ranking_details(rank, total_players)
      # percentage_rank = (rank.to_f / total_players * 100)
      # text, color = case percentage_rank
      #               when 0..5, 100.0 # Include 100.0 explicitly for top rank
      #                 ["Outstanding! You're in the top 5% of players!", 'deep-purple']
      #               when 5.01..10
      #                 ["Great work! You're in the top 10%!", 'positive']
      #               when 10.01..20
      #                 ['Excellent! You beat most players.', 'teal']
      #               when 20.01..35
      #                 ["Nice! You're well above average.", 'primary']
      #               when 35.01..50
      #                 ["Decent! You're in the top half.", 'info']
      #               when 50.01..70
      #                 ["You're in the lower half. Room to grow!", 'warning']
      #               when 70.01..90
      #                 ["Tough competition! You're in the bottom 25%.", 'orange']
      #               else
      #                 ['You’re in the bottom 10%. Don’t give up!', 'negative']
      #               end

      if total_players <= 10
        # Special cases for tiny groups
        text, color = case rank
                      when 1
                        ["You're top of the leaderboard!", 'deep-purple']
                      when 2
                        ['Nice try! Just behind the leader.', 'primary']
                      else
                        ['', 'info']
                      end
      else
        percentile_from_top = ((total_players - rank).to_f / total_players) * 100

        text, color = case percentile_from_top
                      when 95..100
                        ["Outstanding! You're in the top 5% of players!", 'deep-purple']
                      when 90...95
                        ["Great work! You're in the top 10%!", 'positive']
                      when 80...90
                        ['Excellent! You beat most players.', 'teal']
                      when 65...80
                        ["Nice! You're well above average.", 'primary']
                      when 50...65
                        ["Decent! You're in the top half.", 'info']
                      when 30...50
                        ["You're in the lower half. Room to grow!", 'warning']
                      when 10...30
                        ["Tough competition! You're in the bottom 25%.", 'orange']
                      else
                        ['You’re in the bottom 10%. Don’t give up!', 'negative']
                      end
      end
      # percentile_from_top = ((total_players - rank).to_f / total_players) * 100

      # text, color = case percentile_from_top
      #               when 95..100
      #                 ["Outstanding! You're in the top 5% of players!", 'deep-purple']
      #               when 90..95
      #                 ["Great work! You're in the top 10%!", 'positive']
      #               when 80..90
      #                 ['Excellent! You beat most players.', 'teal']
      #               when 65..80
      #                 ["Nice! You're well above average.", 'primary']
      #               when 50..65
      #                 ["Decent! You're in the top half.", 'info']
      #               when 30..50
      #                 ["You're in the lower half. Room to grow!", 'warning']
      #               when 10..30
      #                 ["Tough competition! You're in the bottom 25%.", 'orange']
      #               else
      #                 ['You’re in the bottom 10%. Don’t give up!', 'negative']
      #               end
      # text = 'Come back after more people have played to see how you really rank.' if total_players < 3

      {
        rank: rank,
        total_players: total_players,
        performance_text: text,
        color: color
      }
    end

    def format_currency(cents, currency_code)
      # Using Money gem is recommended for robust currency handling
      # gem 'money'
      Money.from_cents(cents, currency_code).format(no_cents: true)

      # # Simple fallback formatter
      # amount = cents / 100.0
      # symbol = currency_code == 'GBP' ? '£' : '$' # Add more as needed
      # "#{symbol}#{'%.2f' % amount}"
    end
  end
end
