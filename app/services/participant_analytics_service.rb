class ParticipantAnalyticsService
  CACHE_DURATION = 1.hour

  def initialize(date_range: 30.days.ago..Time.current)
    @date_range = date_range
    @participants = Participant.active_in_period(@date_range.begin, @date_range.end)
  end

  # Overview metrics
  def overview_metrics
    Rails.cache.fetch("participant_overview_#{cache_key}", expires_in: CACHE_DURATION) do
      {
        total_participants: Participant.count,
        active_participants: @participants.count,
        new_participants: Participant.first_visit_in_period(@date_range.begin, @date_range.end).count,
        returning_participants: @participants.returning.count,
        average_visits_per_participant: @participants.average(:total_visits).to_f.round(2),
        average_events_per_participant: @participants.average(:total_events).to_f.round(2),
        average_session_duration: @participants.average(:average_session_duration).to_f.round(2),
        high_engagement_count: @participants.where('total_visits > ?', 5).count
      }
    end
  end

  # Chart data for total participants over time
  def participants_over_time_data
    Rails.cache.fetch("participants_over_time_#{cache_key}", expires_in: CACHE_DURATION) do
      data = Participant.where(first_visit_at: @date_range)
                       .group_by_day(:first_visit_at, range: @date_range)
                       .count

      format_time_series_data(data, 'New Participants')
    end
  end

  # Chart data for visit distribution
  def visit_distribution_data
    Rails.cache.fetch("visit_distribution_#{cache_key}", expires_in: CACHE_DURATION) do
      visit_ranges = {
        '1 visit' => @participants.where(total_visits: 1).count,
        '2-3 visits' => @participants.where(total_visits: 2..3).count,
        '4-10 visits' => @participants.where(total_visits: 4..10).count,
        '11-20 visits' => @participants.where(total_visits: 11..20).count,
        '20+ visits' => @participants.where('total_visits > ?', 20).count
      }

      format_pie_chart_data(visit_ranges)
    end
  end

  # Chart data for engagement scores
  def engagement_score_distribution
    Rails.cache.fetch("engagement_scores_#{cache_key}", expires_in: CACHE_DURATION) do
      scores = @participants.map(&:engagement_score)
      
      score_ranges = {
        'Low (0-25)' => scores.count { |s| s <= 25 },
        'Medium (26-50)' => scores.count { |s| s > 25 && s <= 50 },
        'High (51-75)' => scores.count { |s| s > 50 && s <= 75 },
        'Very High (76-100)' => scores.count { |s| s > 75 && s <= 100 },
        'Exceptional (100+)' => scores.count { |s| s > 100 }
      }

      format_pie_chart_data(score_ranges)
    end
  end

  # Chart data for behavior categories
  def behavior_categories_data
    Rails.cache.fetch("behavior_categories_#{cache_key}", expires_in: CACHE_DURATION) do
      categories = @participants.group_by(&:behavior_category)
                              .transform_values(&:count)

      format_pie_chart_data(categories)
    end
  end

  # Chart data for device types
  def device_type_distribution
    Rails.cache.fetch("device_types_#{cache_key}", expires_in: CACHE_DURATION) do
      device_data = @participants.where.not(first_device_type: nil)
                                .group(:first_device_type)
                                .count

      format_pie_chart_data(device_data)
    end
  end

  # Chart data for traffic sources
  def traffic_sources_data
    Rails.cache.fetch("traffic_sources_#{cache_key}", expires_in: CACHE_DURATION) do
      # Categorize traffic sources
      sources = {}
      
      @participants.each do |participant|
        source = categorize_traffic_source(participant)
        sources[source] = (sources[source] || 0) + 1
      end

      format_pie_chart_data(sources)
    end
  end

  # Chart data for geographic distribution
  def geographic_distribution
    Rails.cache.fetch("geographic_distribution_#{cache_key}", expires_in: CACHE_DURATION) do
      country_data = @participants.where.not(first_country: nil)
                                .group(:first_country)
                                .count
                                .sort_by { |_, count| -count }
                                .first(10)
                                .to_h

      format_bar_chart_data(country_data)
    end
  end

  # Chart data for visit frequency analysis
  def visit_frequency_analysis
    Rails.cache.fetch("visit_frequency_#{cache_key}", expires_in: CACHE_DURATION) do
      frequency_data = @participants.returning
                                  .map { |p| p.engagement_metrics['visit_frequency'] || 0 }
                                  .compact
                                  .group_by { |freq| frequency_bucket(freq) }
                                  .transform_values(&:count)

      format_bar_chart_data(frequency_data)
    end
  end

  # Chart data for session duration distribution
  def session_duration_distribution
    Rails.cache.fetch("session_duration_#{cache_key}", expires_in: CACHE_DURATION) do
      durations = @participants.where.not(average_session_duration: nil)
                              .pluck(:average_session_duration)
                              .map(&:to_f)

      duration_ranges = {
        '< 1 min' => durations.count { |d| d < 1 },
        '1-2 min' => durations.count { |d| d >= 1 && d < 2 },
        '2-5 min' => durations.count { |d| d >= 2 && d < 5 },
        '5-10 min' => durations.count { |d| d >= 5 && d < 10 },
        '10+ min' => durations.count { |d| d >= 10 }
      }

      format_bar_chart_data(duration_ranges)
    end
  end

  # Top participants by engagement
  def top_participants(limit = 10)
    Rails.cache.fetch("top_participants_#{limit}_#{cache_key}", expires_in: CACHE_DURATION) do
      @participants.includes(:ahoy_visits)
                   .sort_by(&:engagement_score)
                   .reverse
                   .first(limit)
                   .map do |participant|
        {
          visitor_token: participant.visitor_token,
          engagement_score: participant.engagement_score,
          total_visits: participant.total_visits,
          total_events: participant.total_events,
          behavior_category: participant.behavior_category,
          first_visit_at: participant.first_visit_at,
          last_visit_at: participant.last_visit_at,
          days_active: participant.days_since_first_visit.to_i
        }
      end
    end
  end

  # Cohort analysis data
  def cohort_analysis(period = :week)
    Rails.cache.fetch("cohort_analysis_#{period}_#{cache_key}", expires_in: CACHE_DURATION) do
      cohorts = {}
      
      Participant.where(first_visit_at: @date_range).find_each do |participant|
        cohort_period = participant.first_visit_at.beginning_of_week
        cohorts[cohort_period] ||= { total: 0, returning: 0 }
        cohorts[cohort_period][:total] += 1
        cohorts[cohort_period][:returning] += 1 if participant.returning_visitor?
      end

      cohorts.map do |period, data|
        {
          period: period.strftime('%Y-%m-%d'),
          total_participants: data[:total],
          returning_participants: data[:returning],
          retention_rate: data[:total] > 0 ? (data[:returning].to_f / data[:total] * 100).round(2) : 0
        }
      end.sort_by { |c| c[:period] }
    end
  end

  private

  def cache_key
    "#{@date_range.begin.to_date}_#{@date_range.end.to_date}"
  end

  def format_time_series_data(data, series_name = 'Data')
    data.map { |date, count| [date.strftime('%Y-%m-%d'), count] }
  end

  def format_pie_chart_data(data)
    data.reject { |_, count| count.zero? }
  end

  def format_bar_chart_data(data)
    data.to_a
  end

  def categorize_traffic_source(participant)
    return 'Direct' if participant.first_referrer.blank?
    return 'Email' if participant.first_utm_medium == 'email'
    return 'Social Media' if participant.first_referrer&.match?(/facebook|twitter|linkedin|instagram/)
    return 'Search Engine' if participant.first_referrer&.match?(/google|bing|yahoo|duckduckgo/)
    return 'Paid Ads' if participant.first_utm_medium&.match?(/cpc|ppc|paid/)
    
    'Other'
  end

  def frequency_bucket(frequency)
    case frequency
    when 0...0.1 then 'Very Low (< 0.1/day)'
    when 0.1...0.5 then 'Low (0.1-0.5/day)'
    when 0.5...1.0 then 'Medium (0.5-1/day)'
    when 1.0...2.0 then 'High (1-2/day)'
    else 'Very High (2+/day)'
    end
  end
end
