require 'playwright'
require 'fileutils'
require 'securerandom'
require 'uri'

module <PERSON>raperConnect<PERSON>
  class <PERSON><PERSON>lare<PERSON>lay<PERSON>
    # Extended timeout for Cloudflare challenges
    DEFAULT_PLAYWRIGHT_TIMEOUT = 60_000
    DEFAULT_VIEWPORT = { width: 1366, height: 768 }.freeze
    DEFAULT_LOCALE = 'en-US'.freeze
    BASE_USER_DATA_DIR = File.join(Dir.pwd, 'playwright_cloudflare_data').freeze

    # Enhanced browser arguments for bypassing Cloudflare detection
    CLOUDFLARE_BROWSER_ARGS = [
      '--disable-blink-features=AutomationControlled',
      '--no-first-run',
      '--no-service-autorun',
      '--password-store=basic',
      '--use-mock-keychain',
      '--disable-background-networking',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--disable-extensions',
      '--disable-default-apps',
      '--disable-component-extensions-with-background-pages',
      '--disable-background-downloads',
      '--disable-add-to-shelf',
      '--disable-client-side-phishing-detection',
      '--disable-datasaver-prompt',
      '--disable-desktop-notifications',
      '--disable-domain-reliability',
      '--disable-features=VizDisplayCompositor',
      '--disable-hang-monitor',
      '--disable-prompt-on-repost',
      '--disable-sync',
      '--disable-web-security',
      '--metrics-recording-only',
      '--no-default-browser-check',
      '--no-pings',
      '--use-gl=swiftshader',
      '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ].freeze

    def self.force_cleanup_all_user_data
      if Dir.exist?(BASE_USER_DATA_DIR)
        puts "[INFO] Force cleaning all Cloudflare user data directories in #{BASE_USER_DATA_DIR}"
        begin
          FileUtils.rm_rf(BASE_USER_DATA_DIR)
          puts '[INFO] All Cloudflare user data directories cleaned successfully'
        rescue StandardError => e
          puts "[ERROR] Failed to force clean Cloudflare user data directories: #{e.message}"
        end
      else
        puts '[INFO] No Cloudflare user data directory found to clean'
      end
    end

    def initialize(_scrape_instance = nil, headless: true)
      puts '[INFO] Initializing CloudflarePlaywright...'

      @headless = headless
      @user_data_dir = create_unique_user_data_dir
      puts "[INFO] Using unique Cloudflare user data directory: #{@user_data_dir}"

      @playwright_execution = nil
      @playwright_api = nil
      @browser = nil
      @context = nil

      puts '[INFO] Setting up Cloudflare-resistant browser...'
      setup_browser
      if @context
        puts '[INFO] CloudflarePlaywright initialized successfully.'
      else
        puts '[ERROR] CloudflarePlaywright initialization failed.'
        raise StandardError, 'Failed to initialize CloudflarePlaywright: Browser context not set up.'
      end
    end

    def retrieve_data_from_connector(
      incoming_uri, include_trailing_slash: false, is_search_scrape: false
    )
      puts "[INFO] Starting Cloudflare-resistant retrieval for URI: #{incoming_uri}"
      uri = URI.parse(incoming_uri.to_s)

      unless @context
        puts '[ERROR] Browser context is not initialized.'
        return handle_error(StandardError.new('Browser context not initialized.'))
      end

      content = fetch_content_with_cloudflare_handling(uri, is_search_scrape: is_search_scrape)
      target_field = content.is_a?(Hash) ? 'script_json' : 'full_content_before_js'

      response = {
        returned_content: content,
        scrape_item_target: target_field
      }
      puts "[INFO] Cloudflare-resistant retrieval finished for URI: #{incoming_uri}"
      response
    rescue StandardError => e
      puts "[ERROR] Error in Cloudflare-resistant retrieval: #{e.class} - #{e.message}"
      handle_error(e)
    end

    def cleanup
      puts '[INFO] Starting CloudflarePlaywright cleanup...'
      
      if @context
        begin
          @context.close
          puts '[INFO] Browser context closed.'
        rescue Playwright::Error => e
          puts "[WARN] Error closing context: #{e.message}"
        end
        @context = nil
        @browser = nil
      end

      if @playwright_execution
        begin
          @playwright_execution.stop
          puts '[INFO] Playwright execution stopped.'
        rescue Playwright::Error => e
          puts "[WARN] Error stopping Playwright: #{e.message}"
        end
        @playwright_execution = nil
      end

      cleanup_user_data_dir
      puts '[INFO] CloudflarePlaywright cleanup finished.'
    end

    private

    def create_unique_user_data_dir
      FileUtils.mkdir_p(BASE_USER_DATA_DIR)
      timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
      random_id = SecureRandom.hex(4)
      unique_dir = File.join(BASE_USER_DATA_DIR, "cf_session_#{timestamp}_#{random_id}")
      cleanup_old_user_data_dirs
      FileUtils.mkdir_p(unique_dir)
      unique_dir
    end

    def cleanup_old_user_data_dirs
      return unless Dir.exist?(BASE_USER_DATA_DIR)

      session_dirs = Dir.glob(File.join(BASE_USER_DATA_DIR, 'cf_session_*'))
                        .select { |path| File.directory?(path) }
                        .sort_by { |path| File.ctime(path) }
                        .reverse

      dirs_to_remove = session_dirs[3..-1] || []
      dirs_to_remove.each do |dir|
        puts "[INFO] Removing old Cloudflare data directory: #{dir}"
        FileUtils.rm_rf(dir)
      rescue StandardError => e
        puts "[WARN] Failed to remove old directory #{dir}: #{e.message}"
      end
    end

    def cleanup_user_data_dir
      return unless @user_data_dir && Dir.exist?(@user_data_dir)

      begin
        FileUtils.rm_rf(@user_data_dir)
        puts '[INFO] Cloudflare user data directory cleaned successfully'
      rescue StandardError => e
        puts "[WARN] Failed to clean Cloudflare user data directory: #{e.message}"
      end
    end

    def setup_browser
      puts '[INFO] Starting Cloudflare-resistant browser setup...'

      cli_path = ENV['PLAYWRIGHT_CLI_PATH'] || './node_modules/.bin/playwright'
      @playwright_execution = Playwright.create(playwright_cli_executable_path: cli_path)
      @playwright_api = @playwright_execution.playwright

      puts "[INFO] Launching Cloudflare-resistant browser with enhanced stealth mode..."
      
      max_retries = 3
      retry_count = 0

      loop do
        @browser = @playwright_api.chromium.launch_persistent_context(
          @user_data_dir,
          headless: @headless,
          args: CLOUDFLARE_BROWSER_ARGS,
          viewport: DEFAULT_VIEWPORT,
          locale: DEFAULT_LOCALE
        )
        @context = @browser
        
        # Apply additional stealth modifications
        apply_stealth_modifications
        
        puts '[INFO] Cloudflare-resistant browser context launched successfully.'
        break
      rescue StandardError => e
        retry_count += 1
        raise unless retry_count <= max_retries

        puts "[WARN] Browser startup failed (attempt #{retry_count}/#{max_retries}): #{e.message}"
        cleanup_user_data_dir
        @user_data_dir = create_unique_user_data_dir
        sleep(2)
      end
    rescue StandardError => e
      puts "[ERROR] Failed to setup Cloudflare-resistant browser: #{e.message}"
      cleanup_on_error
      raise
    end

    def apply_stealth_modifications
      return unless @context

      puts '[INFO] Applying stealth modifications...'
      
      # Note: In Playwright Ruby, event listeners work differently
      # We'll apply stealth modifications directly when creating pages
      # rather than using context-level event listeners
    end

    def apply_stealth_to_page(page)
      # Remove webdriver property
      page.add_init_script(<<~JS)
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });
      JS

      # Override plugins
      page.add_init_script(<<~JS)
        Object.defineProperty(navigator, 'plugins', {
          get: () => [1, 2, 3, 4, 5],
        });
      JS

      # Mock Chrome runtime
      page.add_init_script(<<~JS)
        window.chrome = {
          runtime: {},
        };
      JS
    end

    def fetch_content_with_cloudflare_handling(uri, is_search_scrape: false)
      puts "[INFO] Fetching content with Cloudflare handling for: #{uri}"
      
      page = @context.new_page
      
      begin
        puts "[INFO] Applying stealth modifications to page..."
        # Temporarily comment out stealth modifications to test
        # apply_stealth_to_page(page)
        
        puts "[INFO] Setting realistic headers..."
        # Set realistic headers - comment out for now to test
        # page.set_extra_http_headers({...})
        
        puts "[INFO] Navigating to #{uri} with Cloudflare challenge handling..."
        page.goto(uri.to_s)
        
        # Wait for potential Cloudflare challenge to complete
        if cloudflare_challenge_detected?(page)
          puts '[INFO] Cloudflare challenge detected, waiting for completion...'
          wait_for_cloudflare_challenge(page)
        end
        
        # Additional wait for JavaScript execution
        puts '[INFO] Waiting for JavaScript execution...'
        sleep(3)
        
        # Check if we're still being blocked
        if still_blocked_by_cloudflare?(page)
          puts '[WARN] Still blocked by Cloudflare after challenge, attempting additional wait...'
          sleep(5)
        end
        
        content_result = if is_search_scrape
          page.content
        else
          extract_content(page, uri)
        end
        
        puts "[INFO] Content successfully extracted from #{uri}"
        content_result
        
      rescue Playwright::Error => e
        puts "[ERROR] Playwright error during Cloudflare handling: #{e.message}"
        raise
      ensure
        if page && !page.closed?
          begin
            page.close
          rescue Playwright::Error => e
            puts "[WARN] Error closing page: #{e.message}"
          end
        end
      end
    end

    def cloudflare_challenge_detected?(page)
      # Common indicators of Cloudflare challenge
      cloudflare_indicators = [
        'Just a moment...',
        'Checking your browser',
        'Please wait while we check your browser',
        'DDoS protection by Cloudflare',
        'cf-browser-verification',
        'cf-challenge-running'
      ]
      
      page_content = page.content
      title = page.title rescue ''
      
      cloudflare_indicators.any? do |indicator|
        page_content.include?(indicator) || title.include?(indicator)
      end
    end

    def wait_for_cloudflare_challenge(page)
      max_wait_time = 30_000 # 30 seconds
      start_time = Time.now
      
      loop do
        elapsed_time = (Time.now - start_time) * 1000
        break if elapsed_time > max_wait_time
        
        # Check if challenge is completed
        unless cloudflare_challenge_detected?(page)
          puts '[INFO] Cloudflare challenge appears to be completed'
          break
        end
        
        # Wait a bit before checking again
        sleep(1)
      end
      
      # Additional buffer time after challenge completion
      sleep(2)
    end

    def still_blocked_by_cloudflare?(page)
      blocked_indicators = [
        'Access denied',
        'Error 1020',
        'Error 1015',
        'Rate limited',
        'Please complete the security check'
      ]
      
      page_content = page.content
      blocked_indicators.any? { |indicator| page_content.include?(indicator) }
    end

    def extract_content(page, uri)
      puts "[INFO] Extracting content for host: #{uri.host}"
      
      case uri.host
      when /rightmove\.co\.uk/
        puts '[INFO] Extracting window.PAGE_MODEL for Rightmove'
        page.evaluate('window.PAGE_MODEL')
      when /onthemarket\.com/
        puts '[INFO] Extracting window.__NEXT_DATA__ for OnTheMarket'
        page.evaluate('window.__NEXT_DATA__')
      else
        puts '[INFO] Returning full page content'
        page.content
      end
    end

    def cleanup_on_error
      @context = nil
      @browser = nil
      @playwright_api = nil
      
      if @playwright_execution
        begin
          @playwright_execution.stop
        rescue StandardError
          puts '[WARN] Failed to stop Playwright during error cleanup'
        end
        @playwright_execution = nil
      end
      
      cleanup_user_data_dir
    end

    def handle_error(error)
      puts "[INFO] Handling error: #{error.class} - #{error.message}"
      {
        returned_content: nil,
        scrape_item_target: 'full_content_before_js',
        error: "Cloudflare scraping failed: #{error.class} - #{error.message}"
      }
    end
  end
end

# Test script
if __FILE__ == $PROGRAM_NAME
  puts '--- Starting CloudflarePlaywright Test ---'
  connector = nil
  
  begin
    connector = ScraperConnectors::CloudflarePlaywright.new(headless: false)
    
    if connector.instance_variable_get(:@context)
      puts "\n--- Test 1: Site potentially protected by Cloudflare ---"
      # Replace with actual Cloudflare-protected site
      test_url = 'https://example.com'
      data = connector.retrieve_data_from_connector(test_url)
      
      if data[:error]
        puts "Error: #{data[:error]}"
      else
        puts "Success! Content length: #{data[:returned_content]&.length}"
      end
    else
      puts '[FATAL] Connector context not initialized'
    end
    
  rescue StandardError => e
    puts "[FATAL] Test error: #{e.class} - #{e.message}"
    puts e.backtrace.join("\n")
  ensure
    puts "\n--- Cleaning up ---"
    connector&.cleanup
    puts '--- CloudflarePlaywright Test Finished ---'
  end
end
