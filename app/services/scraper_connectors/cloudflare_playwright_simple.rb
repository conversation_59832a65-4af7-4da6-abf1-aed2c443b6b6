require 'playwright'
require 'fileutils'
require 'securerandom'
require 'uri'

module ScraperConnectors
  class CloudflarePlaywrightSimple
    DEFAULT_VIEWPORT = { width: 1366, height: 768 }.freeze
    DEFAULT_LOCALE = 'en-US'.freeze
    BASE_USER_DATA_DIR = File.join(Dir.pwd, 'playwright_cloudflare_simple').freeze

    # Essential browser arguments for bypassing Cloudflare detection
    CLOUDFLARE_BROWSER_ARGS = [
      '--disable-blink-features=AutomationControlled',
      '--no-first-run',
      '--disable-extensions',
      '--disable-default-apps',
      '--disable-sync',
      '--disable-web-security',
      '--no-default-browser-check'
    ].freeze

    def self.force_cleanup_all_user_data
      if Dir.exist?(BASE_USER_DATA_DIR)
        puts "[INFO] Force cleaning all Cloudflare Simple user data directories"
        FileUtils.rm_rf(BASE_USER_DATA_DIR)
      end
    end

    def initialize(_scrape_instance = nil, headless: true)
      puts '[INFO] Initializing CloudflarePlaywrightSimple...'
      @headless = headless
      @user_data_dir = create_unique_user_data_dir
      @playwright_execution = nil
      @playwright_api = nil
      @browser = nil
      @context = nil

      setup_browser
      puts '[INFO] CloudflarePlaywrightSimple initialized successfully.'
    end

    def retrieve_data_from_connector(
      incoming_uri, include_trailing_slash: false, is_search_scrape: false
    )
      puts "[INFO] CloudflarePlaywrightSimple retrieving: #{incoming_uri}"
      uri = URI.parse(incoming_uri.to_s)

      unless @context
        return handle_error(StandardError.new('Browser context not initialized.'))
      end

      content = fetch_content_simple(uri, is_search_scrape: is_search_scrape)
      target_field = content.is_a?(Hash) ? 'script_json' : 'full_content_before_js'

      {
        returned_content: content,
        scrape_item_target: target_field
      }
    rescue StandardError => e
      puts "[ERROR] Error in CloudflarePlaywrightSimple: #{e.class} - #{e.message}"
      handle_error(e)
    end

    def cleanup
      puts '[INFO] CloudflarePlaywrightSimple cleanup...'
      
      @context&.close rescue nil
      @context = nil
      @browser = nil
      
      @playwright_execution&.stop rescue nil
      @playwright_execution = nil
      
      cleanup_user_data_dir
      puts '[INFO] CloudflarePlaywrightSimple cleanup finished.'
    end

    private

    def create_unique_user_data_dir
      FileUtils.mkdir_p(BASE_USER_DATA_DIR)
      timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
      random_id = SecureRandom.hex(4)
      unique_dir = File.join(BASE_USER_DATA_DIR, "simple_#{timestamp}_#{random_id}")
      FileUtils.mkdir_p(unique_dir)
      unique_dir
    end

    def cleanup_user_data_dir
      return unless @user_data_dir && Dir.exist?(@user_data_dir)
      FileUtils.rm_rf(@user_data_dir) rescue nil
    end

    def setup_browser
      puts '[INFO] Setting up simple Cloudflare-resistant browser...'

      cli_path = ENV['PLAYWRIGHT_CLI_PATH'] || './node_modules/.bin/playwright'
      @playwright_execution = Playwright.create(playwright_cli_executable_path: cli_path)
      @playwright_api = @playwright_execution.playwright

      @browser = @playwright_api.chromium.launch_persistent_context(
        @user_data_dir,
        headless: @headless,
        args: CLOUDFLARE_BROWSER_ARGS,
        viewport: DEFAULT_VIEWPORT,
        locale: DEFAULT_LOCALE
      )
      @context = @browser
      
      puts '[INFO] Simple Cloudflare-resistant browser launched successfully.'
    rescue StandardError => e
      puts "[ERROR] Failed to setup simple browser: #{e.message}"
      cleanup_user_data_dir
      raise
    end

    def fetch_content_simple(uri, is_search_scrape: false)
      puts "[INFO] Fetching content from: #{uri}"
      
      page = @context.new_page
      
      begin
        # Apply basic stealth
        page.add_init_script('Object.defineProperty(navigator, "webdriver", { get: () => undefined });')
        page.add_init_script('window.chrome = { runtime: {} };')
        
        puts "[INFO] Navigating to #{uri}..."
        page.goto(uri.to_s)
        
        puts '[INFO] Waiting for page to load...'
        sleep(2)
        
        content_result = if is_search_scrape
          page.content
        else
          extract_content_simple(page, uri)
        end
        
        puts "[INFO] Content extracted successfully from #{uri}"
        content_result
        
      ensure
        page&.close rescue nil
      end
    end

    def extract_content_simple(page, uri)
      case uri.host
      when /rightmove\.co\.uk/
        page.evaluate('window.PAGE_MODEL') rescue page.content
      when /onthemarket\.com/
        page.evaluate('window.__NEXT_DATA__') rescue page.content
      else
        page.content
      end
    end

    def handle_error(error)
      {
        returned_content: nil,
        scrape_item_target: 'full_content_before_js',
        error: "Cloudflare scraping failed: #{error.class} - #{error.message}"
      }
    end
  end
end
