require 'httparty'
require 'json'

module Geo
  class GeoJsonExtractor
    def initialize(url = nil)
      @url = url
      @html_snippet = nil
    end

    def call
      fetch_and_extract_geojson
    rescue StandardError => e
      { error: "Error: #{e.message}" }
    end

    def fetch_geojson_from_nominatim(query)
      response = fetch_nominatim_response(query)
      geojson_data = extract_nominatim_geojson(response)
      build_nominatim_geojson(geojson_data, query)
    rescue StandardError => e
      { error: "Error fetching Nominatim GeoJSON: #{e.message}" }
    end

    private

    def fetch_and_extract_geojson
      fetch_content
      coordinates = extract_coordinates
      build_geojson(coordinates)
    end

    def fetch_content
      response = HTTParty.get(@url)
      raise "Failed to fetch #{@url}: #{response.code} #{response.message}" unless response.success?

      @html_snippet = response.body
    end

    def extract_coordinates
      points_string = extract_coordinates_string
      raise 'No boundary points found in the JavaScript content' unless points_string

      coords = points_string.split(',').each_slice(2).map do |lat, lon|
        [lon.to_f, lat.to_f] # GeoJSON uses [longitude, latitude] order
      end

      # Ensure the polygon is closed
      coords << coords.first unless coords.first == coords.last
      coords
    end

    def extract_coordinates_string
      # Primary regex match
      match = @html_snippet.match(/<input[^>]*id=\\?["']boundary_points\\?["'][^>]*value=\\?["']([^"']+)\\?["']/)
      return match[1] if match

      # Fallback regex match
      match = @html_snippet.match(/id=\\?"boundary_points\\?".*?value=\\?"(.*?)\\?"/)
      match ? match[1] : nil
    end

    def build_geojson(coordinates)
      {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [coordinates]
        },
        properties: {
          source: "Extracted from #{@url}",
          extracted_at: Time.now.utc.to_s
        }
      }
    end

    def fetch_nominatim_response(query)
      nominatim_url = "https://nominatim.openstreetmap.org/search?q=#{URI.encode_www_form_component(query)}&format=json&polygon_geojson=1"
      response = HTTParty.get(nominatim_url, headers: { 'User-Agent' => 'GeoJsonExtractor/1.0' })
      raise "Failed to fetch Nominatim data: #{response.code} #{response.message}" unless response.success?

      JSON.parse(response.body)
    end

    def extract_nominatim_geojson(response)
      result = response.find { |r| r['geojson'] && r['geojson']['type'].in?(%w[Polygon MultiPolygon]) }
      raise 'No valid GeoJSON boundary found for the query' unless result && result['geojson']

      result['geojson']
    end

    def build_nominatim_geojson(geojson_data, query)
      {
        type: 'Feature',
        geometry: geojson_data,
        properties: {
          source: "Nominatim API query: #{query}",
          extracted_at: Time.now.utc.to_s
        }
      }
    end
  end
end
