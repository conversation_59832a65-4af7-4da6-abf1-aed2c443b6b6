# app/services/postcode_data_parser.rb
require 'nokogiri'
require 'open-uri'
require 'net/http'
require 'json'
# 25 feb 2025: initial implementation by brand new claude opus 3.7 via public ui
module RealtyParsers
  class ParseGetthedata # PostcodeDataParser
    attr_reader :doc, :html_content

    def initialize(html_content)
      @html_content = html_content
      @doc = Nokogiri::HTML(html_content)
    end

    def parse
      data = {}

      data[:page_metadata] = parse_page_metadata
      data[:postcode_header] = doc.at_css('div.container h1')&.text&.strip
      data[:location_summary] = parse_location_summary
      data[:maps] = parse_maps
      data[:geodata] = parse_geodata
      data[:location_details] = parse_location_details_table
      data[:elevation] = parse_elevation
      data[:politics] = parse_politics
      data[:house_prices] = parse_house_prices
      data[:transport] = parse_transport
      data[:broadband] = parse_broadband
      data[:energy_consumption] = parse_energy_consumption
      data[:deprivation] = parse_deprivation
      data[:food_standards_ratings] = parse_food_standards
      data[:nearest_post_boxes] = parse_post_boxes
      data[:administrative_areas] = parse_administrative_areas
      data[:census_areas] = parse_census_areas
      data[:nearest_postcodes] = parse_nearest_postcodes

      data.to_json
    end

    private

    def parse_page_metadata
      {
        title: doc.at_css('title')&.text&.strip,
        description: doc.at_css('meta[name="description"]')&.[]('content')&.strip,
        keywords: doc.at_css('meta[name="keywords"]')&.[]('content')&.strip,
        canonical_url: doc.at_css('link[rel="canonical"]')&.[]('href')&.strip
      }
    end

    def parse_location_summary
      summary_p = doc.at_css('ol.breadcrumb + p')
      return nil unless summary_p

      text = summary_p.text.strip
      # Example: "TR16 5RQ lies on Menakarne in Carharrack, Redruth. TR16 5RQ is located in the Redruth Central, Carharrack & St Day electoral ward..."
      # This can be quite complex to parse perfectly with regex, so we'll take a simpler approach
      # or rely on the more structured tables later. For now, just the raw text.
      # A more robust approach would use the "Where is TR16 5RQ?" table.
      {
        full_text: text,
        postcode: text.match(/^(\S+)/)&.[](1)
        # Further parsing would require more specific regex based on patterns
      }
    end

    def parse_maps
      doc.css('div.row img.scale').map do |img|
        {
          title: img['title']&.strip,
          alt: img['alt']&.strip,
          src: img['src']&.strip
        }
      end
    end

    def parse_geodata
      geo_header = doc.at_xpath("//h2[contains(., 'geodata')]")
      return {} unless geo_header

      table = geo_header.xpath("./following-sibling::div[contains(@itemtype, 'GeoCoordinates')]/table[1]")
      return {} unless table

      data = {}
      table.css('tr').each do |row|
        key_node = row.at_css('td:first-child')
        value_node = row.at_css('td:last-child')
        if key_node && value_node
          key = key_node.text.strip.downcase.gsub(/\s+/, '_').to_sym
          data[key] = value_node.text.strip
        end
      end
      data
    end

    def parse_location_details_table
      header = doc.at_xpath("//h2[contains(., 'Where is TR16 5RQ?')]")
      return {} unless header

      table = header.xpath('./following-sibling::table[1]')
      return {} unless table

      details = {}
      table.css('tr').each do |row|
        key_node = row.at_css('td:first-child')
        value_node = row.at_css('td:nth-child(2)') # Could be 2nd td or colspan
        if key_node && value_node
          key = key_node.text.strip.downcase.gsub(/\s+/, '_').to_sym
          details[key] = value_node.text.strip
        end
      end
      details
    end

    def parse_elevation
      header = doc.at_xpath("//h2[contains(., 'Elevation')]")
      return {} unless header

      table = header.xpath('./following-sibling::p[1]/following-sibling::table[1]') # p then table
      return {} unless table

      elevation_data = {}
      row = table.at_css('tr') # Assuming one row with headers and data
      if row
        # Headers are th, data is td in the same row in this specific HTML
        metres_th = row.at_xpath(".//th[contains(text(), 'Metres')]")
        feet_th = row.at_xpath(".//th[contains(text(), 'Feet')]")

        # The actual values are in td elements following the th
        # but in this case, they are in the same row but different cells
        # The data structure is actually: <tr><th></th><th>Metres</th><th>Feet</th></tr>
        #                            <tr><th>Elevation</th><td>100m</td><td>328ft</td></tr>
        # Let's re-target the actual data row
        data_row = table.at_xpath(".//td[contains(text(), '100m')]/parent::tr") # find row with elevation data
        if data_row
          elevation_data[:metres] = data_row.at_css('td:nth-child(2)')&.text&.strip
          elevation_data[:feet] = data_row.at_css('td:nth-child(3)')&.text&.strip
        end
      end
      elevation_data
    end

    def parse_politics
      header = doc.at_xpath("//h2[contains(., 'Politics')]")
      return {} unless header

      table = header.xpath('./following-sibling::table[1]')
      return {} unless table

      politics_data = {}
      table.css('tr').each do |row|
        key_node = row.at_css('td:first-child')
        value_node = row.at_css('td:last-child')
        if key_node && value_node
          key = key_node.text.strip.downcase.gsub(/\s+/, '_').to_sym
          politics_data[key] = value_node.text.strip
        end
      end
      politics_data
    end

    def parse_house_prices
      header = doc.at_xpath("//h3[contains(., 'Sales of detached houses')]") # Example, adjust if needed
      return [] unless header

      table = header.xpath('./following-sibling::table[1]')
      return [] unless table

      sales = []
      table.css('tr').each do |row|
        # Each sale has a complex div structure inside the td
        address_div = row.at_css('td > div:first-child') # The div that contains either text or an <a>
        date_div = row.at_css('td > div > div:nth-child(2)') # The flex div with year/date/price
        price_div = row.at_css('td > div > div:nth-child(3)')

        next unless address_div && date_div && price_div

        address_link = address_div.at_css('a')
        address = address_link ? address_link.text.strip : address_div.text.strip

        # Extract year from the first inner div of the date_div's parent
        year_text = date_div.parent.at_css('div:first-child div:first-child')&.text&.strip
        date_text = date_div.text.strip
        price_text = price_div.text.strip

        sales << {
          address: address,
          year: year_text, # This is actually the year repeated, date_div contains the full date part
          date_sold: date_text, # This might be like "24 AUG"
          price: price_text
        }
      end
      sales
    end

    def parse_transport
      transport_data = { bus_stops: [], railway_stations: [] }

      # Bus Stops
      bus_header = doc.at_xpath("//h3[contains(., 'Nearest bus stops')]")
      if bus_header
        table = bus_header.xpath('./following-sibling::table[1]')
        if table
          table.css('tr').each do |row|
            cells = row.css('td')
            next unless cells.length >= 3

            transport_data[:bus_stops] << {
              name: cells[0].text.strip,
              location: cells[1].text.strip,
              distance: cells[2].text.strip
            }
          end
        end
      end

      # Railway Stations
      rail_header = doc.at_xpath("//h3[contains(., 'Nearest railway stations')]")
      if rail_header
        table = rail_header.xpath('./following-sibling::table[1]')
        if table
          table.css('tr').each do |row|
            cells = row.css('td')
            next unless cells.length >= 2

            transport_data[:railway_stations] << {
              name: cells[0].text.strip,
              distance: cells[1].text.strip
            }
          end
        end
      end
      transport_data
    end

    def parse_broadband
      broadband_data = { access: {}, speed: { download: {}, upload: {} }, limitations: {} }

      # Access
      access_header = doc.at_xpath("//h3[contains(., 'Broadband access')]")
      if access_header
        table = access_header.xpath('./following-sibling::table[1]')
        table&.css('tr')&.each do |row|
          key = row.at_css('td:first-child')&.text&.strip&.downcase&.gsub(/\s+/, '_')&.gsub(/[^a-z0-9_]/, '')&.to_sym
          value = row.at_css('td:last-child')&.text&.strip
          broadband_data[:access][key] = value if key && value
        end
      end

      # Speed - Download
      download_header = doc.at_xpath("//h4[contains(., 'Download')]")
      if download_header
        table = download_header.xpath('./following-sibling::table[1]')
        table&.css('tr')&.each do |row|
          key = row.at_css('td:first-child')&.text&.strip&.downcase&.gsub(/\s+/, '_')&.to_sym
          value = row.at_css('td:last-child')&.text&.strip
          broadband_data[:speed][:download][key] = value if key && value
        end
      end

      # Speed - Upload
      upload_header = doc.at_xpath("//h4[contains(., 'Upload')]")
      if upload_header
        table = upload_header.xpath('./following-sibling::table[1]')
        table&.css('tr')&.each do |row|
          key = row.at_css('td:first-child')&.text&.strip&.downcase&.gsub(/\s+/, '_')&.to_sym
          value = row.at_css('td:last-child')&.text&.strip
          broadband_data[:speed][:upload][key] = value if key && value
        end
      end

      # Limitations
      limitations_header = doc.at_xpath("//h3[contains(., 'Broadband limitations')]")
      if limitations_header
        table = limitations_header.xpath('./following-sibling::table[1]')
        table&.css('tr')&.each do |row|
          key = row.at_css('td:first-child')&.text&.strip&.downcase&.gsub(/\s+/, '_')&.gsub(/[^a-z0-9_]/, '')&.to_sym
          value = row.at_css('td:last-child')&.text&.strip
          broadband_data[:limitations][key] = value if key && value
        end
      end
      broadband_data
    end

    def parse_energy_consumption
      energy_data = { electricity: {} } # Gas might be similar if present

      # Electricity
      elec_header = doc.at_xpath("//h2[contains(., 'gas and electricity consumption')]/following-sibling::h3[contains(., 'Electricity')]")
      if elec_header
        table = elec_header.xpath('./following-sibling::table[1]')
        table&.css('tr')&.each do |row|
          key_node = row.at_css('th, td:first-child') # Key can be in th or td
          value_node = row.at_css('td:last-child')
          next unless key_node && value_node

          key = key_node.text.strip.downcase.gsub(/\s+/, '_').gsub(/[()]/, '').to_sym
          value = value_node.text.strip
          energy_data[:electricity][key] = value
        end
      end
      energy_data
    end

    def parse_deprivation
      # "60.8% of English postcodes are less deprived than TR16 5RQ"
      deprivation_bold_tag = doc.at_xpath("//h2[contains(text(), 'Deprivation')]/following-sibling::b[1]")
      if deprivation_bold_tag
        percentage_less_deprived = deprivation_bold_tag.text.strip.to_f
        return { percentage_less_deprived: percentage_less_deprived }
      end
      {}
    end

    def parse_food_standards
      header = doc.at_xpath("//h2[contains(., 'Food Standards Agency')]")
      return [] unless header

      ratings = []
      # Each rating is in a set of divs: name_div, then details_div
      # We look for the name div, then its immediate next sibling for details
      header.xpath("./following-sibling::h3[contains(., 'nearest food hygiene ratings')]/following-sibling::div[b]").each do |name_div_container|
        name = name_div_container.at_css('b')&.text&.strip
        details_div = name_div_container.xpath('./following-sibling::div[1]') # The div with img and address

        next unless name && details_div

        rating_img_alt = details_div.at_css('img')&.[]('alt')&.strip
        address_lines = details_div.css('div > div:last-child > div') # Two divs here usually
        address = address_lines.map(&:text).map(&:strip).join(', ')
        # Distance is usually the last part of the address div text
        distance = address_lines.last&.text&.strip if address_lines.count > 1

        ratings << {
          name: name,
          rating: rating_img_alt,
          address: address_lines.first&.text&.strip, # First line is usually the specific address
          distance: distance
        }
      end
      ratings
    end

    def parse_post_boxes
      header = doc.at_xpath("//h2[contains(., 'Nearest post box')]")
      return [] unless header

      table = header.xpath('./following-sibling::table[1]')
      return [] unless table

      post_boxes = []
      table.css('tr').each_with_index do |row, index|
        next if index == 0 # Skip header row

        cells = row.css('td')
        next unless cells.length >= 4

        post_boxes << {
          location: cells[0].text.strip,
          last_collection_mon_fri: cells[1].text.strip,
          last_collection_sat: cells[2].text.strip,
          distance: cells[3].text.strip
        }
      end
      post_boxes
    end

    def parse_administrative_areas
      header = doc.at_xpath("//h2[contains(., 'ITL') and contains(., 'LAU')]")
      return {} unless header

      table = header.xpath('./following-sibling::table[1]')
      return {} unless table

      admin_areas = { itl: {}, lau: {} }
      current_section = nil

      table.css('tr').each do |row|
        header_cell = row.at_css('th')
        if header_cell # This row defines a section
          header_text = header_cell.text.strip.downcase
          if header_text.include?('itl 1 code')
            current_section = :itl1
          elsif header_text.include?('itl 2 code')
            current_section = :itl2
          elsif header_text.include?('itl 3 code')
            current_section = :itl3
          elsif header_text.include?('lau 1 code')
            current_section = :lau1
          end
        else # This row is data for the current_section
          cells = row.css('td')
          if cells.length == 2 && current_section
            code = cells[0].text.strip
            name = cells[1].text.strip
            case current_section
            when :itl1
              admin_areas[:itl][:level1] = { code: code, name: name }
            when :itl2
              admin_areas[:itl][:level2] = { code: code, name: name }
            when :itl3
              admin_areas[:itl][:level3] = { code: code, name: name }
            when :lau1
              admin_areas[:lau][:level1] = { code: code, name: name }
            end
          end
        end
      end
      admin_areas
    end

    def parse_census_areas
      header = doc.at_xpath("//h2[contains(., 'census areas')]")
      return {} unless header

      table = header.xpath('./following-sibling::table[1]')
      return {} unless table

      census_data = {}
      table.css('tr').each_with_index do |row, index|
        next if index == 0 # Skip header row if it's a th, or if no th, assume data starts

        cells = row.css('td')
        next unless cells.length >= 2 # OA might only have 2, LSOA/MSOA have 3

        type = cells[0].text.strip.downcase.to_sym
        code = cells[1].text.strip
        name = cells[2]&.text&.strip # Name might be empty for OA
        census_data[type] = { code: code, name: name }.compact
      end
      census_data
    end

    def parse_nearest_postcodes
      header = doc.at_xpath("//h2[contains(., 'Nearest postcodes')]")
      return [] unless header

      table = header.xpath('./following-sibling::table[1]')
      return [] unless table

      postcodes = []
      table.css('tr').each do |row|
        cells = row.css('td')
        next unless cells.length >= 3

        postcode_link = cells[0].at_css('a')
        postcodes << {
          postcode: postcode_link&.text&.strip,
          url: postcode_link&.[]('href')&.strip,
          street_or_locality: cells[1].text.strip,
          distance: cells[2].text.strip
        }
      end
      postcodes
    end
  end

  # Example Usage (outside of Rails, for testing):
  # if __FILE__ == $PROGRAM_NAME
  #   html_file_path = 'path_to_your_html_file.html' # Replace with actual path
  #   html_content = File.read(html_file_path)
  #   parser = PostcodeDataParser.new(html_content)
  #   json_output = parser.parse
  #   puts json_output

  #   # To save to a file:
  #   # File.write('output.json', json_output)
  # end
end
