module RealtyParsers
  class ParseCapeverdepropertyListingsHtml
    def initialize(html_content)
      @html_content = html_content
      @doc = Nokogiri::HTML(html_content)
    end

    def self.property_hash_from_html(html_content)
      parser = new(html_content)
      parser.extract_property_data
    end

    def extract_property_data
      property_data = {}

      # Extract basic property information
      property_data.merge!(extract_basic_info)
      property_data.merge!(extract_price_info)
      property_data.merge!(extract_location_info)
      property_data.merge!(extract_features)
      property_data.merge!(extract_agent_info)
      property_data.merge!(extract_images)

      property_data
    end

    private

    attr_reader :html_content, :doc

    def extract_basic_info
      {
        'title' => extract_title,
        'description' => extract_description,
        'property_type' => extract_property_type,
        'bedrooms' => extract_bedrooms,
        'bathrooms' => extract_bathrooms,
        'reference' => extract_reference
      }
    end

    def extract_title
      title_element = doc.at('h1')
      title_element&.text&.strip || ''
    end

    def extract_description
      # Look for description in various possible locations
      description_selectors = [
        'p:contains("Sea Views")',
        '.property-description',
        '.description p',
        'p'
      ]

      description_selectors.each do |selector|
        elements = doc.css(selector)
        elements.each do |element|
          text = element.text.strip
          if text.length > 20 && !text.match?(/^\d+$/) && !text.include?('©') && !text.include?('cookie')
            return text
          end
        end
      end

      ''
    end

    def extract_property_type
      title = extract_title.downcase
      
      return 'apartment' if title.include?('apartment')
      return 'villa' if title.include?('villa')
      return 'plot' if title.include?('plot') || title.include?('land')
      return 'house' if title.include?('house')
      
      'property'
    end

    def extract_bedrooms
      title = extract_title
      bedroom_match = title.match(/(\d+)\s*bedroom/i)
      return bedroom_match[1].to_i if bedroom_match

      # Look in features or main content
      features_text = doc.css('.main-features, .property-features, .features').text
      bedroom_match = features_text.match(/(\d+)\s*bedroom/i)
      bedroom_match ? bedroom_match[1].to_i : 0
    end

    def extract_bathrooms
      title = extract_title
      bathroom_match = title.match(/(\d+)\s*bathroom/i)
      return bathroom_match[1].to_i if bathroom_match

      # Look in features or main content
      features_text = doc.css('.main-features, .property-features, .features').text
      bathroom_match = features_text.match(/(\d+)\s*bathroom/i)
      bathroom_match ? bathroom_match[1].to_i : 0
    end

    def extract_reference
      # Try to extract from URL or meta tags
      canonical_link = doc.at('link[rel="canonical"]')
      if canonical_link && canonical_link['href']
        url_parts = canonical_link['href'].split('/')
        reference_part = url_parts.find { |part| part.match?(/^[a-z0-9]+$/) && part.length > 5 }
        return reference_part if reference_part
      end

      # Fallback: generate from title
      title = extract_title.downcase.gsub(/[^a-z0-9]/, '')[0..10]
      "cvp_#{title}_#{SecureRandom.hex(3)}"
    end

    def extract_price_info
      price_info = {}
      
      # Find price element
      price_element = doc.css('h2, .price, .property-price').find { |el| el.text.include?('€') }
      
      if price_element
        price_text = price_element.text.strip
        price_number = price_text.gsub(/[^\d]/, '').to_i
        
        price_info['price_raw'] = price_number
        price_info['price_display'] = price_text
        price_info['currency'] = 'EUR'
      else
        price_info['price_raw'] = 0
        price_info['price_display'] = 'Price on request'
        price_info['currency'] = 'EUR'
      end
      
      price_info
    end

    def extract_location_info
      location_info = {}
      
      title = extract_title
      location_parts = title.split(',').map(&:strip)
      
      if location_parts.length >= 2
        location_info['city'] = location_parts[-2]
        location_info['region'] = location_parts[-1]
        location_info['country'] = 'Cape Verde'
      end
      
      # Try to extract coordinates from Google Maps
      map_img = doc.at('img[src*="maps.googleapis.com"]')
      if map_img && map_img['src']
        coord_match = map_img['src'].match(/center=([-\d.]+),([-\d.]+)/)
        if coord_match
          location_info['latitude'] = coord_match[1].to_f
          location_info['longitude'] = coord_match[2].to_f
        end
      end
      
      location_info
    end

    def extract_features
      features_info = {}
      features_list = []
      
      # Extract from list items
      doc.css('li').each do |li|
        feature_text = li.text.strip
        if feature_text.present? && feature_text.length < 100 && !feature_text.match?(/^\d+$/)
          features_list << feature_text
        end
      end
      
      # Extract from main features section
      main_features = doc.at('*:contains("MAIN FEATURES")')
      if main_features
        parent = main_features.parent
        parent.css('li').each do |li|
          feature_text = li.text.strip
          if feature_text.present? && feature_text.length < 100
            features_list << feature_text unless features_list.include?(feature_text)
          end
        end
      end
      
      # Check for common features in description
      description = extract_description.downcase
      additional_features = []
      additional_features << 'Sea view' if description.include?('sea view')
      additional_features << 'Furnished' if description.include?('furnished')
      additional_features << 'Garage' if description.include?('garage')
      additional_features << 'Balcony' if description.include?('balcony')
      additional_features << 'Terrace' if description.include?('terrace')
      
      features_list.concat(additional_features)
      
      features_info['features'] = features_list.uniq
      features_info['furnished'] = description.include?('furnished')
      features_info['sea_view'] = description.include?('sea view')
      
      features_info
    end

    def extract_agent_info
      agent_info = {}
      
      # Default agent information for Cape Verde Property
      agent_info['company_name'] = 'Cape Verde Property'
      agent_info['email'] = '<EMAIL>'
      
      # Try to extract specific contact details
      office_section = doc.css('h3:contains("OFFICE DETAILS")').first&.parent
      if office_section
        # Extract office address
        address_link = office_section.css('a[href*="offices"]').first
        agent_info['office_address'] = address_link&.text&.strip if address_link
        
        # Extract phone numbers
        phone_links = office_section.css('a[href^="tel:"]')
        phones = phone_links.map { |link| link.text.strip }.compact
        agent_info['phone'] = phones.first if phones.any?
        agent_info['phones'] = phones if phones.length > 1
        
        # Extract email
        email_link = office_section.css('a[href^="mailto:"]').first
        if email_link
          extracted_email = email_link.text.strip
          agent_info['email'] = extracted_email if extracted_email.include?('@')
        end
      end
      
      agent_info
    end

    def extract_images
      image_info = {}
      image_urls = []
      
      # Extract images from various selectors
      image_selectors = [
        'img[src*="wdcdn.co"]',
        '.property-images img',
        '.gallery img',
        'img[alt*="Property Image"]'
      ]
      
      image_selectors.each do |selector|
        doc.css(selector).each do |img|
          src = img['src'] || img['data-src']
          next unless src && src.include?('wdcdn.co')
          
          # Convert to higher quality if possible
          high_quality_src = src.gsub('/webp/l/', '/l/').gsub('.webp', '.jpg')
          image_urls << high_quality_src
        end
      end
      
      image_info['images'] = image_urls.uniq
      image_info['image_count'] = image_urls.length
      
      image_info
    end

    # Utility method to clean and normalize text
    def clean_text(text)
      return '' unless text
      
      text.strip
          .gsub(/\s+/, ' ')
          .gsub(/[^\w\s\-.,!?€£$]/, '')
          .strip
    end

    # Utility method to extract numeric value from text
    def extract_number(text)
      return 0 unless text
      
      number_match = text.match(/\d+/)
      number_match ? number_match[0].to_i : 0
    end
  end
end
