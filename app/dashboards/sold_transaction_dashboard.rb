require 'administrate/base_dashboard'

class SoldTransactionDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    archived: Field::Boolean,
    auction: Field::Boolean,
    cloned_from_uuid: Field::String,
    discarded_at: Field::DateTime,
    furnished: Field::Boolean,
    generic_property: Field::BelongsTo,
    generic_property_uuid: Field::String,
    geo_area_uuid: Field::String,
    highest_listed_price_cents: Field::Number,
    highest_listed_price_currency: Field::String,
    is_synthetic_transaction: Field::Boolean,
    leasehold_or_freehold: Field::Number,
    leasehold_years_remaining: Field::Number,
    likely_service_charge_yearly_cents: Field::Number,
    likely_service_charge_yearly_currency: Field::String,
    listing_photos: Field::<PERSON><PERSON>any,
    lowest_listed_price_cents: Field::Number,
    lowest_listed_price_currency: Field::String,
    new_home: Field::<PERSON>olean,
    ordered_visible_listing_photos: Field::HasMany,
    postcode_area: Field::BelongsTo,
    postcode_area_uuid: Field::String,
    potential_rental_daily_cents: Field::Number,
    potential_rental_daily_currency: Field::String,
    potential_rental_monthly_cents: Field::Number,
    potential_rental_monthly_currency: Field::String,
    published: Field::Boolean,
    realty_asset: Field::BelongsTo,
    realty_asset_uuid: Field::String,
    related_urls: Field::String.with_options(searchable: false),
    retirement_home: Field::Boolean,
    shared_ownership: Field::Boolean,
    site_visitor_token: Field::String,
    sold_date: Field::Date,
    sold_price_cents: Field::Number,
    sold_price_currency: Field::String,
    sold_transaction_currency: Field::String,
    sold_transaction_description: Field::String,
    sold_transaction_details: Field::String.with_options(searchable: false),
    sold_transaction_flags: Field::Number,
    sold_transaction_photos_count: Field::Number,
    sold_transaction_reference: Field::String,
    sold_transaction_slug: Field::String,
    sold_transaction_source: Field::String,
    sold_transaction_tags: Field::String,
    sold_transaction_title: Field::String,
    st_street: Field::String,
    st_saon: Field::String,
    st_paon: Field::String,
    st_property_type: Field::String,
    st_age_of_property: Field::String,
    st_position_in_list: Field::Number,
    translations: Field::String.with_options(searchable: false),
    user_uuid: Field::String,
    uuid: Field::String,
    versions: Field::String, # Field::HasMany,
    versions_count: Field::Number,
    visible: Field::Boolean,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
    st_latitude: Field::Number.with_options(decimals: 2),
    st_longitude: Field::Number.with_options(decimals: 2),
    st_postal_code: Field::String
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    created_at
    postcode_area
    sold_date
    is_synthetic_transaction
    sold_price_cents
    st_postal_code
    sold_transaction_reference
    sold_transaction_slug
    sold_transaction_source
    sold_transaction_title
    st_street
    st_saon
    st_paon
    st_property_type
    st_age_of_property
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    postcode_area
    is_synthetic_transaction
    sold_date
    sold_price_cents
    sold_price_currency
    sold_transaction_currency
    sold_transaction_description
    sold_transaction_details
    sold_transaction_flags
    sold_transaction_photos_count
    sold_transaction_reference
    sold_transaction_slug
    sold_transaction_source
    sold_transaction_tags
    sold_transaction_title
    st_position_in_list
    postcode_area_uuid
    agency_tenant
    agency_tenant_uuid
    archived
    auction
    cloned_from_uuid
    discarded_at
    furnished
    generic_property
    generic_property_uuid
    geo_area_uuid
    highest_listed_price_cents
    highest_listed_price_currency
    leasehold_or_freehold
    leasehold_years_remaining
    likely_service_charge_yearly_cents
    likely_service_charge_yearly_currency
    listing_photos
    lowest_listed_price_cents
    lowest_listed_price_currency
    new_home
    ordered_visible_listing_photos
    potential_rental_daily_cents
    potential_rental_daily_currency
    potential_rental_monthly_cents
    potential_rental_monthly_currency
    published
    realty_asset
    realty_asset_uuid
    related_urls
    retirement_home
    shared_ownership
    site_visitor_token
    translations
    user_uuid
    uuid
    versions
    versions_count
    visible
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    archived
    auction
    cloned_from_uuid
    discarded_at
    furnished
    generic_property
    generic_property_uuid
    geo_area_uuid
    highest_listed_price_cents
    highest_listed_price_currency
    is_synthetic_transaction
    leasehold_or_freehold
    leasehold_years_remaining
    likely_service_charge_yearly_cents
    likely_service_charge_yearly_currency
    listing_photos
    lowest_listed_price_cents
    lowest_listed_price_currency
    new_home
    ordered_visible_listing_photos
    postcode_area
    postcode_area_uuid
    potential_rental_daily_cents
    potential_rental_daily_currency
    potential_rental_monthly_cents
    potential_rental_monthly_currency
    published
    realty_asset
    realty_asset_uuid
    related_urls
    retirement_home
    shared_ownership
    site_visitor_token
    sold_date
    sold_price_cents
    sold_price_currency
    sold_transaction_currency
    sold_transaction_description
    sold_transaction_details
    sold_transaction_flags
    sold_transaction_photos_count
    sold_transaction_reference
    sold_transaction_slug
    sold_transaction_source
    sold_transaction_tags
    sold_transaction_title
    st_position_in_list
    translations
    user_uuid
    uuid
    versions
    versions_count
    visible
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how sold transactions are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(sold_transaction)
  #   "SoldTransaction ##{sold_transaction.id}"
  # end
end
