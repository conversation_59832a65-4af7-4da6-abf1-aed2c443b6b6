require 'administrate/base_dashboard'

class AiInsights::CompositeImageSourceDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    associated_listing_uuid: Field::String,
    batch_limit: Field::Number,
    batch_offset: Field::Number,
    composite_checksum: Field::String,
    composite_image_type: Field::String,
    composite_photo_capture_url: Field::String,
    composite_photo_file_path: Field::String,
    description_medium: Field::Text,
    discarded_at: Field::DateTime,
    dossier_asset: Field::BelongsTo,
    dossier_asset_uuid: Field::String,
    duration_ms: Field::Number,
    error_message: Field::Text,
    file_size_bytes: Field::Number,
    flags: Field::Number,
    is_for_most_comparable_property_to_primary: Field::Boolean,
    is_source_for_primary_property: Field::Boolean,
    llm_interaction: Field::BelongsTo,
    llm_interaction_uuid: Field::String,
    llm_model: Field::String,
    main_section_or_room_details: Field::String.with_options(searchable: false),
    other_section_or_room_details: Field::String.with_options(searchable: false),
    prompt_payload: Field::Text,
    prompt_template_version: Field::String,
    property_is_for_rent: Field::Boolean,
    property_is_for_sale: Field::Boolean,
    rating: Field::Number,
    realty_asset_photo: Field::BelongsTo,
    realty_asset_photo_uuid: Field::String,
    realty_dossier: Field::BelongsTo,
    realty_dossier_uuid: Field::String,
    realty_search_query_uuid: Field::String,
    response_status: Field::String,
    screenshot_dimensions: Field::String.with_options(searchable: false),
    screenshot_url: Field::String,
    source_is_epc: Field::Boolean,
    source_is_listing: Field::Boolean,
    source_is_photo: Field::Boolean,
    source_is_sold_transaction: Field::Boolean,
    sub_images: Field::String.with_options(searchable: false),
    sub_images_count: Field::Number,
    unique_rooms_or_sections: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    associated_listing_uuid
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    associated_listing_uuid
    batch_limit
    batch_offset
    composite_checksum
    composite_image_type
    composite_photo_capture_url
    composite_photo_file_path
    description_medium
    discarded_at
    dossier_asset
    dossier_asset_uuid
    duration_ms
    error_message
    file_size_bytes
    flags
    is_for_most_comparable_property_to_primary
    is_source_for_primary_property
    llm_interaction
    llm_interaction_uuid
    llm_model
    main_section_or_room_details
    other_section_or_room_details
    prompt_payload
    prompt_template_version
    property_is_for_rent
    property_is_for_sale
    rating
    realty_asset_photo
    realty_asset_photo_uuid
    realty_dossier
    realty_dossier_uuid
    realty_search_query_uuid
    response_status
    screenshot_dimensions
    screenshot_url
    source_is_epc
    source_is_listing
    source_is_photo
    source_is_sold_transaction
    sub_images
    sub_images_count
    unique_rooms_or_sections
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    associated_listing_uuid
    batch_limit
    batch_offset
    composite_checksum
    composite_image_type
    composite_photo_capture_url
    composite_photo_file_path
    description_medium
    discarded_at
    dossier_asset
    dossier_asset_uuid
    duration_ms
    error_message
    file_size_bytes
    flags
    is_for_most_comparable_property_to_primary
    is_source_for_primary_property
    llm_interaction
    llm_interaction_uuid
    llm_model
    main_section_or_room_details
    other_section_or_room_details
    prompt_payload
    prompt_template_version
    property_is_for_rent
    property_is_for_sale
    rating
    realty_asset_photo
    realty_asset_photo_uuid
    realty_dossier
    realty_dossier_uuid
    realty_search_query_uuid
    response_status
    screenshot_dimensions
    screenshot_url
    source_is_epc
    source_is_listing
    source_is_photo
    source_is_sold_transaction
    sub_images
    sub_images_count
    unique_rooms_or_sections
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how composite image sources are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(composite_image_source)
  #   "AiInsights::CompositeImageSource ##{composite_image_source.id}"
  # end
end
