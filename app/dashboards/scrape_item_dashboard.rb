require 'administrate/base_dashboard'

class ScrapeItemDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    all_page_images: Field::String.with_options(searchable: false),
    all_page_images_length: Field::Number,
    body: Field::Text,
    client_provided_html: Field::Text,
    confidence_score: Field::Number,
    content_is_binary: Field::Boolean,
    content_is_html: Field::Boolean,
    content_is_json: Field::Boolean,
    content_is_pdf: Field::Boolean,
    content_is_xml: Field::Boolean,
    currency: Field::String,
    description: Field::String,
    discarded_at: Field::DateTime,
    extra_scrape_item_details: Field::String.with_options(searchable: false),
    full_content_after_js: Field::Text,
    full_content_after_js_length: Field::Number,
    full_content_before_js: Field::Text,
    full_content_before_js_length: Field::Number,
    further_scrapable_urls: Field::String.with_options(searchable: false),
    guest_uuid: Field::String,
    has_screenshot: Field::Boolean,
    is_active_listing: Field::Boolean,
    is_paid_scrape: Field::Boolean,
    is_valid_scrape: Field::Boolean,
    last_realty_asset: Field::BelongsTo,
    listing_state: Field::String,
    llm_interaction: Field::BelongsTo,
    llm_interaction_uuid: Field::String,
    nokogiri_object: Field::Text,
    page_locale_code: Field::String,
    page_screenshot_uuid: Field::String,
    price_rental_monthly_standard_season_cents: Field::Number,
    price_rental_monthly_standard_season_currency: Field::String,
    price_sale_current_cents: Field::Number,
    price_sale_current_currency: Field::String,
    realty_asset_uuid: Field::String,
    related_scrape_items: Field::String.with_options(searchable: false),
    rental_listing_uuid: Field::String,
    request_object: Field::String.with_options(searchable: false),
    response_code: Field::String,
    response_object: Field::String.with_options(searchable: false),
    sale_listing: Field::BelongsTo,
    sale_listing_uuid: Field::String,
    scrapable_url: Field::String,
    scrape_cannonical_url: Field::String,
    scrape_failure_message: Field::String,
    scrape_item_flags: Field::Number,
    scrape_unique_url: Field::String,
    scrape_uri_host: Field::String,
    scrape_uri_scheme: Field::String,
    scraped_page_uuid: Field::String,
    scraper_connector_name: Field::String,
    scraper_host_uuid: Field::String,
    scraper_mapping_json: Field::String.with_options(searchable: false),
    scraper_mapping_name: Field::String,
    scraper_mapping_version: Field::String,
    script_json: Field::String.with_options(searchable: false),
    selectors_and_values: Field::String.with_options(searchable: false),
    title: Field::String,
    translations: Field::String.with_options(searchable: false),
    user_locale_code: Field::String,
    user_uuid: Field::String,
    uuid: Field::String,
    versions: Field::String, # Field::HasMany,
    web_scraper_name: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    full_content_before_js_length
    created_at
    scrapable_url
    is_valid_scrape
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    full_content_before_js_length
    title
    created_at
    updated_at
    scrapable_url
    scrape_cannonical_url
    scrape_failure_message
    scrape_item_flags
    scrape_unique_url
    is_active_listing
    is_paid_scrape
    is_valid_scrape
    agency_tenant
    agency_tenant_uuid
    all_page_images
    all_page_images_length
    body
    client_provided_html
    confidence_score
    content_is_binary
    content_is_html
    content_is_json
    content_is_pdf
    content_is_xml
    currency
    description
    discarded_at
    extra_scrape_item_details
    full_content_after_js
    full_content_after_js_length
    further_scrapable_urls
    guest_uuid
    has_screenshot
    last_realty_asset
    listing_state
    llm_interaction
    llm_interaction_uuid
    nokogiri_object
    page_locale_code
    page_screenshot_uuid
    price_rental_monthly_standard_season_cents
    price_rental_monthly_standard_season_currency
    price_sale_current_cents
    price_sale_current_currency
    realty_asset_uuid
    related_scrape_items
    rental_listing_uuid
    request_object
    response_code
    response_object
    sale_listing
    sale_listing_uuid
    scrape_uri_host
    scrape_uri_scheme
    scraped_page_uuid
    scraper_connector_name
    scraper_host_uuid
    scraper_mapping_json
    scraper_mapping_name
    scraper_mapping_version
    script_json
    selectors_and_values
    translations
    user_locale_code
    user_uuid
    uuid
    versions
    web_scraper_name
    full_content_before_js
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    all_page_images
    all_page_images_length
    body
    client_provided_html
    confidence_score
    content_is_binary
    content_is_html
    content_is_json
    content_is_pdf
    content_is_xml
    currency
    description
    discarded_at
    extra_scrape_item_details
    full_content_after_js
    full_content_after_js_length
    full_content_before_js
    full_content_before_js_length
    further_scrapable_urls
    guest_uuid
    has_screenshot
    is_active_listing
    is_paid_scrape
    is_valid_scrape
    last_realty_asset
    listing_state
    llm_interaction
    llm_interaction_uuid
    nokogiri_object
    page_locale_code
    page_screenshot_uuid
    price_rental_monthly_standard_season_cents
    price_rental_monthly_standard_season_currency
    price_sale_current_cents
    price_sale_current_currency
    realty_asset_uuid
    related_scrape_items
    rental_listing_uuid
    request_object
    response_code
    response_object
    sale_listing
    sale_listing_uuid
    scrapable_url
    scrape_cannonical_url
    scrape_failure_message
    scrape_item_flags
    scrape_unique_url
    scrape_uri_host
    scrape_uri_scheme
    scraped_page_uuid
    scraper_connector_name
    scraper_host_uuid
    scraper_mapping_json
    scraper_mapping_name
    scraper_mapping_version
    script_json
    selectors_and_values
    title
    translations
    user_locale_code
    user_uuid
    uuid
    versions
    web_scraper_name
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how scrape items are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(scrape_item)
  #   "ScrapeItem ##{scrape_item.id}"
  # end
end
