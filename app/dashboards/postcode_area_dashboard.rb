require 'administrate/base_dashboard'

class PostcodeAreaDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    admin_county: Field::String,
    admin_district: Field::String,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    altitude: Field::Number,
    area_households: Field::Number,
    area_population: Field::Number,
    average_household_income: Field::Number,
    bbox_max_latitude: Field::String.with_options(searchable: false),
    bbox_max_longitude: Field::String.with_options(searchable: false),
    bbox_min_latitude: Field::String.with_options(searchable: false),
    bbox_min_longitude: Field::String.with_options(searchable: false),
    boundary_coordinates: Field::String.with_options(searchable: false),
    built_up_area: Field::String,
    center_latitude: Field::String.with_options(searchable: false),
    center_longitude: Field::String.with_options(searchable: false),
    child_postal_codes: Field::HasMany,
    city: Field::String,
    country: Field::String,
    date_of_introduction: Field::Date,
    date_of_termination: Field::Date,
    discarded_at: Field::DateTime,
    doogal_details: Field::String.with_options(searchable: false),
    eastings: Field::Number,
    european_electoral_region: Field::String,
    feature_type: Field::String,
    generic_properties: Field::HasMany,
    geo_clusters: Field::HasMany,
    geometry_type: Field::String,
    in_use: Field::Boolean,
    incode: Field::String,
    index_of_multiple_deprivation: Field::Number,
    is_incomplete_postcode: Field::Boolean,
    is_outcode_only: Field::Boolean,
    kml_data: Field::Text,
    lsoa: Field::String,
    mapit_id: Field::String,
    median_generic_property: Field::BelongsTo,
    median_generic_property_uuid: Field::String,
    msoa: Field::String,
    nhs_ha: Field::String,
    northings: Field::Number,
    number_of_sales: Field::Number,
    outcode: Field::String,
    parent_postal_code: Field::BelongsTo,
    parent_uuid: Field::String,
    parish: Field::String,
    parliamentary_constituency: Field::String,
    percent_diff_outcode_price: Field::Number,
    place_type: Field::String,
    post_town: Field::String,
    postal_code: Field::String,
    postcode_area_codes: Field::String.with_options(searchable: false),
    postcode_area_description: Field::String,
    postcode_area_details: Field::String.with_options(searchable: false),
    postcode_area_flags: Field::Number,
    postcode_area_geojson: Field::String.with_options(searchable: false),
    postcode_area_geometry: Field::String.with_options(searchable: false),
    postcode_area_slug: Field::String,
    postcode_area_streets: Field::String,
    postcode_area_tags: Field::String,
    postcode_area_title: Field::String,
    postcode_average_property_price_cents: Field::Number,
    postcode_average_property_price_currency: Field::String,
    postcode_geo_clusters: Field::String, # HasMany,
    predominant_property_type: Field::String,
    primary_care_trust: Field::String,
    quality: Field::Number,
    real_sold_transactions: Field::HasMany,
    realty_assets: Field::HasMany,
    realty_assets_count: Field::Number,
    region: Field::String,
    relevance: Field::String.with_options(searchable: false),
    rural_urban: Field::String,
    sold_transactions: Field::HasMany,
    sold_transactions_count: Field::Number,
    suitable_for_ppu_analysis: Field::Boolean,
    synthetic_sold_transactions: Field::HasMany,
    uk_grid_reference: Field::String,
    uk_ward: Field::String,
    uk_ward_code: Field::String,
    uuid: Field::String,
    what_three_words: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    created_at
    postal_code
    postcode_average_property_price_cents
    generic_properties
    median_generic_property
    center_latitude
    sold_transactions_count
    predominant_property_type
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    what_three_words
    predominant_property_type
    created_at
    postal_code
    generic_properties
    median_generic_property
    median_generic_property_uuid
    postcode_average_property_price_cents
    postcode_average_property_price_currency
    sold_transactions_count
    sold_transactions
    altitude
    area_households
    area_population
    average_household_income
    bbox_max_latitude
    bbox_max_longitude
    bbox_min_latitude
    bbox_min_longitude
    boundary_coordinates
    built_up_area
    center_latitude
    center_longitude
    child_postal_codes
    city
    country
    date_of_introduction
    date_of_termination
    discarded_at
    doogal_details
    eastings
    admin_county
    admin_district
    agency_tenant
    agency_tenant_uuid
    european_electoral_region
    feature_type
    generic_properties
    geo_clusters
    geometry_type
    in_use
    incode
    index_of_multiple_deprivation
    is_incomplete_postcode
    is_outcode_only
    kml_data
    lsoa
    mapit_id
    median_generic_property
    median_generic_property_uuid
    msoa
    nhs_ha
    northings
    number_of_sales
    outcode
    parent_postal_code
    parent_uuid
    parish
    parliamentary_constituency
    percent_diff_outcode_price
    place_type
    post_town
    postal_code
    postcode_area_codes
    postcode_area_description
    postcode_area_details
    postcode_area_flags
    postcode_area_geojson
    postcode_area_geometry
    postcode_area_slug
    postcode_area_streets
    postcode_area_tags
    postcode_area_title
    postcode_average_property_price_cents
    postcode_average_property_price_currency
    postcode_geo_clusters
    primary_care_trust
    quality
    real_sold_transactions
    realty_assets
    realty_assets_count
    region
    relevance
    rural_urban
    sold_transactions
    sold_transactions_count
    suitable_for_ppu_analysis
    synthetic_sold_transactions
    uk_grid_reference
    uk_ward
    uk_ward_code
    uuid
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    admin_county
    admin_district
    agency_tenant
    agency_tenant_uuid
    altitude
    area_households
    area_population
    average_household_income
    bbox_max_latitude
    bbox_max_longitude
    bbox_min_latitude
    bbox_min_longitude
    boundary_coordinates
    built_up_area
    center_latitude
    center_longitude
    child_postal_codes
    city
    country
    date_of_introduction
    date_of_termination
    discarded_at
    doogal_details
    eastings
    european_electoral_region
    feature_type
    generic_properties
    geo_clusters
    geometry_type
    in_use
    incode
    index_of_multiple_deprivation
    is_incomplete_postcode
    is_outcode_only
    kml_data
    lsoa
    mapit_id
    median_generic_property
    median_generic_property_uuid
    msoa
    nhs_ha
    northings
    number_of_sales
    outcode
    parent_postal_code
    parent_uuid
    parish
    parliamentary_constituency
    percent_diff_outcode_price
    place_type
    post_town
    postal_code
    postcode_area_codes
    postcode_area_description
    postcode_area_details
    postcode_area_flags
    postcode_area_geojson
    postcode_area_geometry
    postcode_area_slug
    postcode_area_streets
    postcode_area_tags
    postcode_area_title
    postcode_average_property_price_cents
    postcode_average_property_price_currency
    postcode_geo_clusters
    predominant_property_type
    primary_care_trust
    quality
    real_sold_transactions
    realty_assets
    realty_assets_count
    region
    relevance
    rural_urban
    sold_transactions
    sold_transactions_count
    suitable_for_ppu_analysis
    synthetic_sold_transactions
    uk_grid_reference
    uk_ward
    uk_ward_code
    uuid
    what_three_words
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how postcode areas are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(postcode_area)
  #   "PostcodeArea ##{postcode_area.id}"
  # end
end
