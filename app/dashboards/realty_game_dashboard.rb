require "administrate/base_dashboard"

class RealtyGameDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    available_game_listings_count: Field::Number,
    discarded_at: Field::DateTime,
    game_area_details: Field::String.with_options(searchable: false),
    game_bg_image_url: Field::String,
    game_default_country: Field::String,
    game_default_currency: Field::String,
    game_default_locale: Field::String,
    game_description: Field::String,
    game_end_at: Field::DateTime,
    game_global_slug: Field::String,
    game_jots_count: Field::Number,
    game_listings_count: Field::Number,
    game_notes: Field::String.with_options(searchable: false),
    game_primary_user_uuid: Field::String,
    game_rules: Field::String.with_options(searchable: false),
    game_sessions: Field::HasMany,
    game_sessions_count: Field::Number,
    game_settings_flags: Field::Number,
    game_source_portal: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    game_start_at: Field::DateTime,
    game_starting_url: Field::String,
    game_title: Field::String,
    game_type_flags: Field::Number,
    guessed_prices: Field::HasMany,
    guessed_prices_count: Field::Number,
    is_one_off_game: Field::Boolean,
    is_paid_game: Field::Boolean,
    is_public_listed_game: Field::Boolean,
    one_off_mgmt_code: Field::String,
    ordered_listing_ids: Field::String,
    realty_game_aasm_state: Field::String,
    realty_game_details: Field::String.with_options(searchable: false),
    realty_game_flags: Field::Number,
    realty_game_listings: Field::HasMany,
    realty_game_slug: Field::String,
    scoot: Field::BelongsTo,
    scoot_uuid: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    video_url_for_game: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    is_one_off_game
    game_global_slug
    game_title
    game_sessions_count
    game_listings_count
    scoot
    game_default_locale
    created_at
    updated_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    available_game_listings_count
    discarded_at
    game_area_details
    game_bg_image_url
    game_default_country
    game_default_currency
    game_default_locale
    game_description
    game_end_at
    game_global_slug
    game_jots_count
    game_listings_count
    game_notes
    game_primary_user_uuid
    game_rules
    game_sessions
    game_sessions_count
    game_settings_flags
    game_source_portal
    game_start_at
    game_starting_url
    game_title
    game_type_flags
    guessed_prices
    guessed_prices_count
    is_one_off_game
    is_paid_game
    is_public_listed_game
    one_off_mgmt_code
    ordered_listing_ids
    realty_game_aasm_state
    realty_game_details
    realty_game_flags
    realty_game_listings
    realty_game_slug
    scoot
    scoot_uuid
    translations
    uuid
    video_url_for_game
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    game_area_details
    game_bg_image_url
    game_default_country
    game_default_currency
    game_default_locale
    game_description
    game_starting_url
    game_title
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how realty games are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(realty_game)
  #   "RealtyGame ##{realty_game.id}"
  # end
end
