require 'administrate/base_dashboard'

class LlmInteractionDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    aasm_state: Field::String,
    agency_tenant_uuid: Field::String,
    ai_model: Field::String,
    chosen_response: Field::String.with_options(searchable: false),
    chosen_response_finish_reason: Field::String,
    cloned_from_uuid: Field::String,
    discarded_at: Field::DateTime,
    endpoint: Field::String,
    extra_llm_interaction_details: Field::String.with_options(searchable: false),
    extra_params: Field::String.with_options(searchable: false),
    flags: Field::Number,
    full_prompt: Field::Text,
    full_response: Field::String.with_options(searchable: false),
    has_errored: Field::Boolean,
    llm_error_message: Field::String,
    llm_interaction_slug: Field::String,
    max_tokens: Field::Number,
    related_llm_interactions: Field::String.with_options(searchable: false),
    response_choices: Field::String.with_options(searchable: false),
    response_completion_tokens: Field::Number,
    response_id: Field::String,
    response_model: Field::String,
    response_object: Field::String,
    response_prompt_tokens: Field::Number,
    response_total_tokens: Field::Number,
    result_confidence: Field::Number,
    temperature: Field::Number.with_options(decimals: 2),
    translations: Field::String.with_options(searchable: false),
    user_uuid: Field::String,
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
    realty_asset: Field::HasOne,
    realty_asset_photo: Field::HasOne,
    sale_listing: Field::HasOne
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    sale_listing
    realty_asset_photo
    response_total_tokens
    has_errored
    ai_model
    response_model
    llm_interaction_slug
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    has_errored
    llm_error_message
    temperature
    created_at
    updated_at
    sale_listing
    realty_asset
    realty_asset_photo
    aasm_state
    agency_tenant_uuid
    ai_model
    response_model
    chosen_response
    chosen_response_finish_reason
    cloned_from_uuid
    discarded_at
    endpoint
    extra_llm_interaction_details
    extra_params
    flags
    full_prompt
    full_response
    llm_interaction_slug
    max_tokens
    related_llm_interactions
    response_choices
    response_completion_tokens
    response_id
    response_object
    response_prompt_tokens
    response_total_tokens
    result_confidence
    translations
    user_uuid
    uuid
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    aasm_state
    agency_tenant_uuid
    ai_model
    chosen_response
    chosen_response_finish_reason
    cloned_from_uuid
    discarded_at
    endpoint
    extra_llm_interaction_details
    extra_params
    flags
    full_prompt
    full_response
    has_errored
    llm_error_message
    llm_interaction_slug
    max_tokens
    related_llm_interactions
    response_choices
    response_completion_tokens
    response_id
    response_model
    response_object
    response_prompt_tokens
    response_total_tokens
    result_confidence
    temperature
    translations
    user_uuid
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how llm interactions are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(llm_interaction)
  #   "LlmInteraction ##{llm_interaction.id}"
  # end
end
