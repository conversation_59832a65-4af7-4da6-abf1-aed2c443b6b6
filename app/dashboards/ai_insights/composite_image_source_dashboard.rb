require 'administrate/base_dashboard'

module ::AiInsights
  class CompositeImageSourceDashboard < Administrate::BaseDashboard
    ATTRIBUTE_TYPES = {
      id: Field::Number,
      uuid: Field::String,
      agency_tenant_uuid: Field::String,
      dossier_asset: Field::BelongsTo,
      dossier_asset_uuid: Field::String,
      llm_interaction: Field::BelongsTo,
      llm_interaction_uuid: Field::String,
      realty_asset_photo: Field::BelongsTo,
      realty_dossier_uuid: Field::String,
      associated_listing_uuid: Field::String,
      composite_image_type: Field::String,
      composite_photo_file_path: Field::String,
      screenshot_url: Field::String,
      batch_offset: Field::Number,
      batch_limit: Field::Number,
      llm_model: Field::String,
      prompt_template_version: Field::String,
      prompt_payload: Field::Text.with_options(searchable: false),
      response_status: Field::String,
      error_message: Field::Text.with_options(searchable: false),
      duration_ms: Field::Number,
      screenshot_dimensions: Field::String.with_options(searchable: false),
      file_size_bytes: Field::Number,
      sub_images_count: Field::Number,
      composite_checksum: Field::String,
      description_medium: Field::Text.with_options(searchable: false),
      main_section_or_room_details: Field::String.with_options(searchable: false),
      other_section_or_room_details: Field::String.with_options(searchable: false),
      sub_images: Field::String.with_options(searchable: false),
      unique_rooms_or_sections: Field::String.with_options(searchable: false),
      flags: Field::Number,
      property_is_for_rent: Field::Boolean,
      property_is_for_sale: Field::Boolean,
      discarded_at: Field::DateTime,
      created_at: Field::DateTime,
      updated_at: Field::DateTime
    }.freeze

    COLLECTION_ATTRIBUTES = %i[
      id
      sub_images_count
      batch_offset
      composite_photo_file_path
      response_status
      dossier_asset
      llm_interaction
      created_at
    ].freeze

    SHOW_PAGE_ATTRIBUTES = %i[
      id
      uuid
      agency_tenant_uuid
      dossier_asset
      dossier_asset_uuid
      llm_interaction
      llm_interaction_uuid
      realty_asset_photo
      realty_dossier_uuid
      associated_listing_uuid
      composite_image_type
      composite_photo_file_path
      screenshot_url
      batch_offset
      batch_limit
      llm_model
      prompt_template_version
      prompt_payload
      response_status
      error_message
      duration_ms
      screenshot_dimensions
      file_size_bytes
      sub_images_count
      composite_checksum
      description_medium
      main_section_or_room_details
      other_section_or_room_details
      sub_images
      unique_rooms_or_sections
      flags
      property_is_for_rent
      property_is_for_sale
      discarded_at
      created_at
      updated_at
    ].freeze

    FORM_ATTRIBUTES = %i[
      agency_tenant_uuid
      dossier_asset
      llm_interaction
      realty_asset_photo
      associated_listing_uuid
      composite_image_type
      composite_photo_file_path
      screenshot_url
      batch_offset
      batch_limit
      llm_model
      prompt_template_version
      prompt_payload
      response_status
      error_message
      duration_ms
      screenshot_dimensions
      file_size_bytes
      sub_images_count
      composite_checksum
      description_medium
      main_section_or_room_details
      other_section_or_room_details
      sub_images
      unique_rooms_or_sections
      flags
      property_is_for_rent
      property_is_for_sale
      discarded_at
    ].freeze

    COLLECTION_FILTERS = {}.freeze

    # Overwrite this method to customize how composite image sources are displayed
    # across all pages of the admin dashboard.
    #
    # def display_resource(composite_image_source)
    #   "CompositeImageSource ##{composite_image_source.id} - #{composite_image_source.uuid}"
    # end
  end
end
