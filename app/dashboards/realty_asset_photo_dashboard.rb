require "administrate/base_dashboard"

class RealtyAssetPhotoDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    agency_uuid: Field::String,
    cloned_from_uuid: Field::String,
    content_type: Field::String,
    photo_title: Field::String,
    photo_description: Field::String,
    photo_gen_prompt: Field::String,
    details: Field::String.with_options(searchable: false),
    external_img_details: Field::String.with_options(searchable: false),
    discarded_at: Field::DateTime,
    file_size: Field::Number,
    photo_flags: Field::Number,
    folder: Field::String,
    height: Field::String,
    image: Field::String,
    new_build_listing_uuid: Field::String,
    photo_slug: Field::String,
    process_options: Field::String.with_options(searchable: false),
    psq_visit_id: Field::Number,
    realty_asset: Field::BelongsTo,
    realty_asset_uuid: Field::String,
    realty_image_attachment: Field::String, # Field::HasOne,
    # 1 july 2025 - had to remove administrate-field-active_storage gem
    # to upgrade to ruby 3.4.3
    # realty_image: Field::ActiveStorage,
    # image_attachment: Field::HasOne,
    zac_active_storage_attachment: Field::HasOne,
    realty_image_blob: Field::String, # Field::HasOne,
    zac_active_storage_blob: Field::HasOne,
    remote_photo_url: Field::String,
    rental_listing: Field::BelongsTo,
    rental_listing_uuid: Field::String,
    sale_listing: Field::BelongsTo,
    sale_listing_uuid: Field::String,
    site_visitor_token: Field::String,
    sort_order: Field::Number,
    sort_order_new_build: Field::Number,
    sort_order_rental: Field::Number,
    sort_order_sale: Field::Number,
    translations: Field::String.with_options(searchable: false),
    user_uuid: Field::String,
    uuid: Field::String,
    visible_for_new_build_listing: Field::Boolean,
    visible_for_rental_listing: Field::Boolean,
    visible_for_sale_listing: Field::Boolean,
    width: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  # url: Field::String
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    photo_slug
    created_at
    file_size
    sale_listing_uuid
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    photo_slug
    photo_title
    photo_description
    photo_gen_prompt
    agency_tenant
    agency_tenant_uuid
    width
    height
    file_size
    realty_image
    zac_active_storage_attachment
    zac_active_storage_blob
    agency_uuid
    cloned_from_uuid
    content_type
    details
    external_img_details
    discarded_at
    photo_flags
    folder
    image
    new_build_listing_uuid
    process_options
    psq_visit_id
    realty_asset
    realty_asset_uuid
    realty_image_attachment
    realty_image_blob
    remote_photo_url
    rental_listing
    rental_listing_uuid
    sale_listing
    sale_listing_uuid
    site_visitor_token
    sort_order
    sort_order_new_build
    sort_order_rental
    sort_order_sale
    translations
    user_uuid
    uuid
    visible_for_new_build_listing
    visible_for_rental_listing
    visible_for_sale_listing
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    agency_uuid
    cloned_from_uuid
    content_type
    photo_description
    external_img_details
    discarded_at
    file_size
    photo_flags
    folder
    height
    image
    new_build_listing_uuid
    photo_slug
    process_options
    psq_visit_id
    realty_asset
    realty_asset_uuid
    realty_image_attachment
    realty_image_blob
    remote_photo_url
    rental_listing
    rental_listing_uuid
    sale_listing
    sale_listing_uuid
    site_visitor_token
    sort_order
    sort_order_new_build
    sort_order_rental
    sort_order_sale
    translations
    user_uuid
    uuid
    visible_for_new_build_listing
    visible_for_rental_listing
    visible_for_sale_listing
    width
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how realty asset photos are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(realty_asset_photo)
  #   "RealtyAssetPhoto ##{realty_asset_photo.id}"
  # end
end
