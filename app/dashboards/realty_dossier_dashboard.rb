require 'administrate/base_dashboard'

class RealtyDossierDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    postal_code: Field::String,
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    discarded_at: Field::DateTime,
    dossier_checklists: Field::String.with_options(searchable: false),
    dossier_description: Field::String,
    dossier_notes: Field::String.with_options(searchable: false),
    dossier_places_of_interest: Field::String.with_options(searchable: false),
    dossier_related_urls: Field::String.with_options(searchable: false),
    dossier_significant_dates: Field::String.with_options(searchable: false),
    dossier_source_portal: Field::Number,
    dossier_starting_url: Field::String,
    dossier_title: Field::String,
    dossier_user_uuid: Field::String,
    listing_history: Field::String.with_options(searchable: false),
    mortgage_projections: Field::String.with_options(searchable: false),
    params_for_similar_properties: Field::String.with_options(searchable: false),
    primary_realty_asset: Field::String, # BelongsTo,
    primary_sale_listing: Field::String, # BelongsTo,
    realty_dossier_aasm_state: Field::String,
    realty_dossier_details: Field::String.with_options(searchable: false),
    realty_dossier_flags: Field::Number,
    site_visitor_token: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
    dossier_assets_count: Field::Number
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    postal_code
    created_at
    updated_at
    primary_realty_asset
    primary_sale_listing
    dossier_assets_count
    discarded_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    uuid
    dossier_assets_count
    created_at
    updated_at
    primary_realty_asset
    primary_sale_listing
    agency_tenant
    agency_tenant_uuid
    discarded_at
    dossier_checklists
    dossier_description
    dossier_notes
    dossier_places_of_interest
    dossier_related_urls
    dossier_significant_dates
    dossier_source_portal
    dossier_starting_url
    dossier_title
    dossier_user_uuid
    listing_history
    mortgage_projections
    params_for_similar_properties
    realty_dossier_aasm_state
    realty_dossier_details
    realty_dossier_flags
    site_visitor_token
    translations
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    discarded_at
    dossier_checklists
    dossier_description
    dossier_notes
    dossier_places_of_interest
    dossier_related_urls
    dossier_significant_dates
    dossier_source_portal
    dossier_starting_url
    dossier_title
    dossier_user_uuid
    listing_history
    mortgage_projections
    params_for_similar_properties
    primary_realty_asset
    primary_sale_listing
    realty_dossier_aasm_state
    realty_dossier_details
    realty_dossier_flags
    site_visitor_token
    translations
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how realty dossiers are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(realty_dossier)
  #   "RealtyDossier ##{realty_dossier.id}"
  # end
end
