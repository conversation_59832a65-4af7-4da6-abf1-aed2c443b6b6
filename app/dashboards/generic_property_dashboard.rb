require 'administrate/base_dashboard'

class GenericPropertyDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    archived: Field::Boolean,
    area_unit: Field::Number,
    buyers_guide: Field::String.with_options(searchable: false),
    city: Field::String,
    cloned_from_uuid: Field::String,
    constructed_area: Field::Number.with_options(decimals: 2),
    constructed_area_sq_ft: Field::String.with_options(searchable: false),
    constructed_area_sq_mt: Field::String.with_options(searchable: false),
    country: Field::String,
    design_style: Field::String,
    details_of_rooms: Field::String.with_options(searchable: false),
    discarded_at: Field::DateTime,
    energy_performance: Field::Number.with_options(decimals: 2),
    energy_rating: Field::Number,
    extra_generic_property_details: Field::String.with_options(searchable: false),
    floor_of_flat: Field::String,
    floors_in_building: Field::Number,
    furnished: Field::<PERSON>olean,
    generic_property_currency: Field::String,
    generic_property_description: Field::String,
    generic_property_features: Field::String.with_options(searchable: false),
    generic_property_flags: Field::Number,
    generic_property_gen_prompt: Field::Text,
    generic_property_photos: Field::String, # Field::HasMany,
    generic_property_photos_count: Field::Number,
    generic_property_reference: Field::String,
    generic_property_sections: Field::HasMany,
    generic_property_sections_count: Field::Number,
    generic_property_slug: Field::String,
    generic_property_tags: Field::String,
    generic_property_title: Field::String,
    generic_property_type: Field::Number,
    generic_property_type_europe: Field::Number,
    generic_property_type_uk: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    generic_property_type_us: Field::Number,
    gp_position_in_list: Field::Number,
    high_sale_price_cents: Field::Number,
    high_sale_price_currency: Field::String,
    highest_sale_price_cents: Field::Number,
    highest_sale_price_currency: Field::String,
    is_ai_generated_gp: Field::Boolean,
    latitude: Field::Number.with_options(decimals: 2),
    leasehold_or_freehold: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    leasehold_years_remaining: Field::Number,
    likely_service_charge_yearly_cents: Field::Number,
    likely_service_charge_yearly_currency: Field::String,
    llm_interaction: Field::BelongsTo,
    llm_interaction_uuid: Field::String,
    longitude: Field::Number.with_options(decimals: 2),
    low_sale_price_cents: Field::Number,
    low_sale_price_currency: Field::String,
    lowest_sale_price_cents: Field::Number,
    lowest_sale_price_currency: Field::String,
    map_url: Field::String,
    no_of_bathrooms: Field::Number.with_options(decimals: 2),
    no_of_bedrooms: Field::Number,
    no_of_garages: Field::Number,
    no_of_toilets: Field::Number,
    # ordered_visible_generic_property_photos: Field::HasMany,
    plot_area: Field::Number.with_options(decimals: 2),
    plot_area_sq_ft: Field::String.with_options(searchable: false),
    plot_area_sq_mt: Field::String.with_options(searchable: false),
    postal_code: Field::String,
    potential_rental_daily_cents: Field::Number,
    potential_rental_daily_currency: Field::String,
    potential_rental_monthly_cents: Field::Number,
    potential_rental_monthly_currency: Field::String,
    primary_neighborhood: Field::String,
    prop_origin_key: Field::String,
    prop_state_key: Field::String,
    prop_type_key: Field::String,
    province: Field::String,
    published: Field::Boolean,
    reasonable_sale_price_cents: Field::Number,
    reasonable_sale_price_currency: Field::String,
    region: Field::String,
    related_urls: Field::String.with_options(searchable: false),
    sellers_guide: Field::String.with_options(searchable: false),
    similar_neighborhoods: Field::String,
    site_visitor_token: Field::String,
    street_name: Field::String,
    street_number: Field::String,
    translations: Field::String.with_options(searchable: false),
    usefulness_rating: Field::Number,
    user_uuid: Field::String,
    uuid: Field::String,
    versions_count: Field::Number,
    visible: Field::Boolean,
    year_construction: Field::Number,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    reasonable_sale_price_cents
    generic_property_sections_count
    generic_property_reference
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    generic_property_reference
    created_at
    updated_at
    reasonable_sale_price_cents
    reasonable_sale_price_currency
    generic_property_sections_count
    generic_property_slug
    generic_property_tags
    generic_property_title
    archived
    area_unit
    agency_tenant
    agency_tenant_uuid
    buyers_guide
    city
    cloned_from_uuid
    constructed_area
    constructed_area_sq_ft
    constructed_area_sq_mt
    country
    design_style
    details_of_rooms
    discarded_at
    energy_performance
    energy_rating
    extra_generic_property_details
    floor_of_flat
    floors_in_building
    furnished
    generic_property_currency
    generic_property_description
    generic_property_features
    generic_property_flags
    generic_property_gen_prompt
    generic_property_photos
    generic_property_photos_count
    generic_property_sections
    generic_property_type
    generic_property_type_europe
    generic_property_type_uk
    generic_property_type_us
    gp_position_in_list
    high_sale_price_cents
    high_sale_price_currency
    highest_sale_price_cents
    highest_sale_price_currency
    is_ai_generated_gp
    latitude
    leasehold_or_freehold
    leasehold_years_remaining
    likely_service_charge_yearly_cents
    likely_service_charge_yearly_currency
    llm_interaction
    llm_interaction_uuid
    longitude
    low_sale_price_cents
    low_sale_price_currency
    lowest_sale_price_cents
    lowest_sale_price_currency
    map_url
    no_of_bathrooms
    no_of_bedrooms
    no_of_garages
    no_of_toilets
    plot_area
    plot_area_sq_ft
    plot_area_sq_mt
    postal_code
    potential_rental_daily_cents
    potential_rental_daily_currency
    potential_rental_monthly_cents
    potential_rental_monthly_currency
    primary_neighborhood
    prop_origin_key
    prop_state_key
    prop_type_key
    province
    published
    region
    related_urls
    sellers_guide
    similar_neighborhoods
    site_visitor_token
    street_name
    street_number
    translations
    usefulness_rating
    user_uuid
    uuid
    versions_count
    visible
    year_construction
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    archived
    area_unit
    buyers_guide
    city
    cloned_from_uuid
    constructed_area
    constructed_area_sq_ft
    constructed_area_sq_mt
    country
    design_style
    details_of_rooms
    discarded_at
    energy_performance
    energy_rating
    extra_generic_property_details
    floor_of_flat
    floors_in_building
    furnished
    generic_property_currency
    generic_property_description
    generic_property_features
    generic_property_flags
    generic_property_gen_prompt
    generic_property_photos
    generic_property_photos_count
    generic_property_reference
    generic_property_sections
    generic_property_sections_count
    generic_property_slug
    generic_property_tags
    generic_property_title
    generic_property_type
    generic_property_type_europe
    generic_property_type_uk
    generic_property_type_us
    gp_position_in_list
    high_sale_price_cents
    high_sale_price_currency
    highest_sale_price_cents
    highest_sale_price_currency
    is_ai_generated_gp
    latitude
    leasehold_or_freehold
    leasehold_years_remaining
    likely_service_charge_yearly_cents
    likely_service_charge_yearly_currency
    llm_interaction
    llm_interaction_uuid
    longitude
    low_sale_price_cents
    low_sale_price_currency
    lowest_sale_price_cents
    lowest_sale_price_currency
    map_url
    no_of_bathrooms
    no_of_bedrooms
    no_of_garages
    no_of_toilets
    plot_area
    plot_area_sq_ft
    plot_area_sq_mt
    postal_code
    potential_rental_daily_cents
    potential_rental_daily_currency
    potential_rental_monthly_cents
    potential_rental_monthly_currency
    primary_neighborhood
    prop_origin_key
    prop_state_key
    prop_type_key
    province
    published
    reasonable_sale_price_cents
    reasonable_sale_price_currency
    region
    related_urls
    sellers_guide
    similar_neighborhoods
    site_visitor_token
    street_name
    street_number
    translations
    usefulness_rating
    user_uuid
    uuid
    versions_count
    visible
    year_construction
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how generic properties are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(generic_property)
  #   "GenericProperty ##{generic_property.id}"
  # end
end
