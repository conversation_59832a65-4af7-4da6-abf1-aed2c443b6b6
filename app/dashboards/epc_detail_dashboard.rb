require 'administrate/base_dashboard'

class EpcDetailDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    address: Field::String,
    address1: Field::String,
    address2: Field::String,
    address3: Field::String,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    building_reference_number: Field::String,
    built_form: Field::String,
    city: Field::String,
    co2_emiss_curr_per_floor_area: Field::String.with_options(searchable: false),
    co2_emissions_current: Field::String.with_options(searchable: false),
    co2_emissions_potential: Field::String.with_options(searchable: false),
    constituency: Field::String,
    constituency_label: Field::String,
    construction_age_band_full: Field::String,
    construction_age_band: Field::String,
    county: Field::String,
    current_energy_efficiency: Field::Number,
    current_energy_rating: Field::String,
    discarded_at: Field::DateTime,
    energy_consumption_current: Field::Number.with_options(decimals: 2),
    energy_consumption_potential: Field::Number.with_options(decimals: 2),
    energy_tariff: Field::String,
    environmental_impact_current: Field::Number,
    environmental_impact_potential: Field::Number,
    epc_extra_details: Field::String.with_options(searchable: false),
    epc_flags: Field::Number,
    epc_latitude: Field::Number.with_options(decimals: 2),
    epc_longitude: Field::Number.with_options(decimals: 2),
    epc_outcode: Field::String,
    epc_tags: Field::String,
    extension_count: Field::Number,
    fixed_lighting_outlets_count: Field::Number.with_options(decimals: 2),
    flat_storey_count: Field::String.with_options(searchable: false),
    flat_top_storey: Field::String,
    floor_description: Field::String,
    floor_energy_eff: Field::String,
    floor_env_eff: Field::String,
    floor_height: Field::String.with_options(searchable: false),
    floor_level: Field::String,
    glazed_area: Field::String,
    glazed_type: Field::String,
    heat_loss_corridor: Field::String,
    heating_cost_current: Field::Number.with_options(decimals: 2),
    heating_cost_potential: Field::Number.with_options(decimals: 2),
    hot_water_cost_current: Field::Number.with_options(decimals: 2),
    hot_water_cost_potential: Field::Number.with_options(decimals: 2),
    hot_water_energy_eff: Field::String,
    hot_water_env_eff: Field::String,
    hotwater_description: Field::String,
    inspection_date: Field::Date,
    last_sale_listing_uuid: Field::String,
    last_sold_transaction_uuid: Field::String,
    lighting_cost_current: Field::Number.with_options(decimals: 2),
    lighting_cost_potential: Field::Number.with_options(decimals: 2),
    lighting_description: Field::String,
    lighting_energy_eff: Field::String,
    lighting_env_eff: Field::String,
    lmk_key: Field::String,
    local_authority: Field::String,
    local_authority_label: Field::String,
    lodgement_date: Field::Date,
    lodgement_datetime: Field::DateTime,
    low_energy_fixed_light_count: Field::Number.with_options(decimals: 2),
    low_energy_lighting: Field::Number,
    main_fuel: Field::String,
    main_heating_controls: Field::String,
    mainheat_description: Field::String,
    mainheat_energy_eff: Field::String,
    mainheat_env_eff: Field::String,
    mainheatc_energy_eff: Field::String,
    mainheatc_env_eff: Field::String,
    mainheatcont_description: Field::String,
    mains_gas_flag: Field::String,
    mechanical_ventilation: Field::String,
    multi_glaze_proportion: Field::Number.with_options(decimals: 2),
    number_habitable_rooms: Field::Number.with_options(decimals: 2),
    number_heated_rooms: Field::Number.with_options(decimals: 2),
    number_open_fireplaces: Field::Number,
    photo_supply: Field::Number.with_options(decimals: 2),
    epc_postal_code: Field::String,
    postcode_area: Field::BelongsTo,
    postcode_area_uuid: Field::String,
    posttown: Field::String,
    potential_energy_efficiency: Field::Number,
    potential_energy_rating: Field::String,
    property_type: Field::String,
    province: Field::String,
    realty_asset_uuid: Field::String,
    roof_description: Field::String,
    roof_energy_eff: Field::String,
    roof_env_eff: Field::String,
    secondheat_description: Field::String,
    sheating_energy_eff: Field::String,
    sheating_env_eff: Field::String,
    solar_water_heating_flag: Field::String,
    tenure: Field::String,
    total_floor_area: Field::String.with_options(searchable: false),
    transaction_type: Field::String,
    translations: Field::String.with_options(searchable: false),
    unheated_corridor_length: Field::String.with_options(searchable: false),
    uprn: Field::Number,
    uprn_source: Field::String,
    uuid: Field::String,
    walls_description: Field::String,
    walls_energy_eff: Field::String,
    walls_env_eff: Field::String,
    wind_turbine_count: Field::Number.with_options(decimals: 2),
    windows_description: Field::String,
    windows_energy_eff: Field::String,
    windows_env_eff: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    address
    address1
    epc_postal_code
    property_type
    tenure
    current_energy_rating
    uprn_source
    total_floor_area
    inspection_date
    construction_age_band_full
    construction_age_band
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    construction_age_band_full
    address
    address1
    address2
    address3
    agency_tenant
    agency_tenant_uuid
    building_reference_number
    built_form
    city
    co2_emiss_curr_per_floor_area
    co2_emissions_current
    co2_emissions_potential
    constituency
    constituency_label
    construction_age_band
    county
    current_energy_efficiency
    current_energy_rating
    discarded_at
    energy_consumption_current
    energy_consumption_potential
    energy_tariff
    environmental_impact_current
    environmental_impact_potential
    epc_extra_details
    epc_flags
    epc_latitude
    epc_longitude
    epc_outcode
    epc_tags
    extension_count
    fixed_lighting_outlets_count
    flat_storey_count
    flat_top_storey
    floor_description
    floor_energy_eff
    floor_env_eff
    floor_height
    floor_level
    glazed_area
    glazed_type
    heat_loss_corridor
    heating_cost_current
    heating_cost_potential
    hot_water_cost_current
    hot_water_cost_potential
    hot_water_energy_eff
    hot_water_env_eff
    hotwater_description
    inspection_date
    last_sale_listing_uuid
    last_sold_transaction_uuid
    lighting_cost_current
    lighting_cost_potential
    lighting_description
    lighting_energy_eff
    lighting_env_eff
    lmk_key
    local_authority
    local_authority_label
    lodgement_date
    lodgement_datetime
    low_energy_fixed_light_count
    low_energy_lighting
    main_fuel
    main_heating_controls
    mainheat_description
    mainheat_energy_eff
    mainheat_env_eff
    mainheatc_energy_eff
    mainheatc_env_eff
    mainheatcont_description
    mains_gas_flag
    mechanical_ventilation
    multi_glaze_proportion
    number_habitable_rooms
    number_heated_rooms
    number_open_fireplaces
    photo_supply
    epc_postal_code
    postcode_area
    postcode_area_uuid
    posttown
    potential_energy_efficiency
    potential_energy_rating
    property_type
    province
    realty_asset_uuid
    roof_description
    roof_energy_eff
    roof_env_eff
    secondheat_description
    sheating_energy_eff
    sheating_env_eff
    solar_water_heating_flag
    tenure
    total_floor_area
    transaction_type
    translations
    unheated_corridor_length
    uprn
    uprn_source
    uuid
    walls_description
    walls_energy_eff
    walls_env_eff
    wind_turbine_count
    windows_description
    windows_energy_eff
    windows_env_eff
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    address
    address1
    address2
    address3
    agency_tenant
    agency_tenant_uuid
    building_reference_number
    built_form
    city
    co2_emiss_curr_per_floor_area
    co2_emissions_current
    co2_emissions_potential
    constituency
    constituency_label
    construction_age_band
    county
    current_energy_efficiency
    current_energy_rating
    discarded_at
    energy_consumption_current
    energy_consumption_potential
    energy_tariff
    environmental_impact_current
    environmental_impact_potential
    epc_extra_details
    epc_flags
    epc_latitude
    epc_longitude
    epc_outcode
    epc_tags
    extension_count
    fixed_lighting_outlets_count
    flat_storey_count
    flat_top_storey
    floor_description
    floor_energy_eff
    floor_env_eff
    floor_height
    floor_level
    glazed_area
    glazed_type
    heat_loss_corridor
    heating_cost_current
    heating_cost_potential
    hot_water_cost_current
    hot_water_cost_potential
    hot_water_energy_eff
    hot_water_env_eff
    hotwater_description
    inspection_date
    last_sale_listing_uuid
    last_sold_transaction_uuid
    lighting_cost_current
    lighting_cost_potential
    lighting_description
    lighting_energy_eff
    lighting_env_eff
    lmk_key
    local_authority
    local_authority_label
    lodgement_date
    lodgement_datetime
    low_energy_fixed_light_count
    low_energy_lighting
    main_fuel
    main_heating_controls
    mainheat_description
    mainheat_energy_eff
    mainheat_env_eff
    mainheatc_energy_eff
    mainheatc_env_eff
    mainheatcont_description
    mains_gas_flag
    mechanical_ventilation
    multi_glaze_proportion
    number_habitable_rooms
    number_heated_rooms
    number_open_fireplaces
    photo_supply
    epc_postal_code
    postcode_area
    postcode_area_uuid
    posttown
    potential_energy_efficiency
    potential_energy_rating
    property_type
    province
    realty_asset_uuid
    roof_description
    roof_energy_eff
    roof_env_eff
    secondheat_description
    sheating_energy_eff
    sheating_env_eff
    solar_water_heating_flag
    tenure
    total_floor_area
    transaction_type
    translations
    unheated_corridor_length
    uprn
    uprn_source
    uuid
    walls_description
    walls_energy_eff
    walls_env_eff
    wind_turbine_count
    windows_description
    windows_energy_eff
    windows_env_eff
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how epc details are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(epc_detail)
  #   "EpcDetail ##{epc_detail.id}"
  # end
end
