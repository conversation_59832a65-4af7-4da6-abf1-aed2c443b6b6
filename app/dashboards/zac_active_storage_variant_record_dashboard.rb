require 'administrate/base_dashboard'

class ZacActiveStorageVariantRecordDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    blob_id: Field::Number,
    # blob: Field::BelongsTo,
    zac_active_storage_blob: Field::BelongsTo,
    # image_attachment: Field::HasOne,
    zac_active_storage_attachment: Field::HasOne,
    # image_blob: Field::HasOne,
    # below is result of:
    #   delegate :created_at, to: :zac_active_storage_blob
    created_at: Field::String,
    variation_digest: Field::String
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    blob_id
    created_at
    zac_active_storage_blob
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    created_at
    zac_active_storage_attachment
    zac_active_storage_blob
    variation_digest
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    zac_active_storage_blob
    variation_digest
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how zac active storage variant records are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(zac_active_storage_variant_record)
  #   "ZacActiveStorageVariantRecord ##{zac_active_storage_variant_record.id}"
  # end
end
