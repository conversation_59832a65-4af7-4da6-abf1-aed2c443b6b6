require 'administrate/base_dashboard'

class DossierJotDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    archived: Field::Boolean,
    discarded_at: Field::DateTime,
    dossier_asset_uuid: Field::String,
    dossier_assets_comparison_uuid: Field::String,
    dossier_jot_aasm_state: Field::String,
    dossier_jot_flags: Field::Number,
    dossier_task_uuid: Field::String,
    is_photo_specific: Field::Boolean,
    is_primary_listing_jot: Field::Boolean,
    is_query: Field::Boolean,
    jot_creator_name: Field::String,
    jot_creator_token: Field::String,
    jot_details: Field::String.with_options(searchable: false),
    jot_text: Field::Text,
    jot_title: Field::String,
    photo_uuids: Field::String,
    primary_photo: Field::BelongsTo,
    primary_photo_uuid: Field::String,
    realty_dossier: Field::BelongsTo,
    realty_dossier_uuid: Field::String,
    uuid: Field::String,
    visible_to_all: Field::Boolean,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    jot_text
    created_at
    primary_photo
    archived
    is_primary_listing_jot
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    jot_text
    agency_tenant
    agency_tenant_uuid
    archived
    discarded_at
    dossier_asset_uuid
    dossier_assets_comparison_uuid
    dossier_jot_aasm_state
    dossier_jot_flags
    dossier_task_uuid
    is_photo_specific
    is_primary_listing_jot
    is_query
    jot_creator_name
    jot_creator_token
    jot_details
    jot_title
    photo_uuids
    primary_photo
    primary_photo_uuid
    realty_dossier
    realty_dossier_uuid
    uuid
    visible_to_all
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    jot_text
    agency_tenant
    agency_tenant_uuid
    archived
    discarded_at
    dossier_asset_uuid
    dossier_assets_comparison_uuid
    dossier_jot_aasm_state
    dossier_jot_flags
    dossier_task_uuid
    is_photo_specific
    is_primary_listing_jot
    is_query
    jot_creator_name
    jot_creator_token
    jot_details
    jot_title
    photo_uuids
    primary_photo
    primary_photo_uuid
    realty_dossier
    realty_dossier_uuid
    uuid
    visible_to_all
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how dossier jots are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(dossier_jot)
  #   "DossierJot ##{dossier_jot.id}"
  # end
end
