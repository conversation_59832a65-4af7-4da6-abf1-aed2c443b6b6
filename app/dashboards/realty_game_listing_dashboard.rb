require 'administrate/base_dashboard'

class RealtyGameListingDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    average_guess_cents: Field::Number,
    discarded_at: Field::DateTime,
    game_sessions: Field::HasMany,
    game_sessions_count: Field::Number,
    guessed_prices: Field::HasMany,
    guessed_prices_count: Field::Number,
    highest_guess_cents: Field::Number,
    is_rental_listing: Field::Boolean,
    is_sale_listing: Field::Boolean,
    listing_uuid: Field::String,
    lowest_guess_cents: Field::Number,
    ordered_photo_uuids: Field::Text,
    photo_in_game_comments: Field::String.with_options(searchable: false),
    position_in_game: Field::Number,
    realty_asset: Field::BelongsTo,
    realty_asset_uuid: Field::String,
    realty_game: Field::BelongsTo,
    realty_game_listing_details: Field::String.with_options(searchable: false),
    realty_game_listing_flags: Field::Number,
    realty_game_uuid: Field::String,
    rental_listing: Field::BelongsTo,
    sale_listing: Field::BelongsTo,
    scoot: Field::BelongsTo,
    scoot_uuid: Field::String,
    statistics_updated_at: Field::DateTime,
    total_guesses_count: Field::Number,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    visible_in_game: Field::Boolean,
    visible_photo_uuids: Field::Text,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    average_guess_cents
    scoot
    created_at
    statistics_updated_at
    game_sessions_count
    guessed_prices_count
    guessed_prices
    discarded_at
    game_sessions
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    average_guess_cents
    discarded_at
    game_sessions
    game_sessions_count
    guessed_prices
    guessed_prices_count
    highest_guess_cents
    is_rental_listing
    is_sale_listing
    listing_uuid
    lowest_guess_cents
    ordered_photo_uuids
    photo_in_game_comments
    position_in_game
    realty_asset
    realty_asset_uuid
    realty_game
    realty_game_listing_details
    realty_game_listing_flags
    realty_game_uuid
    rental_listing
    sale_listing
    scoot
    scoot_uuid
    statistics_updated_at
    total_guesses_count
    translations
    uuid
    visible_in_game
    visible_photo_uuids
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    average_guess_cents
    discarded_at
    game_sessions
    game_sessions_count
    guessed_prices
    guessed_prices_count
    highest_guess_cents
    is_rental_listing
    is_sale_listing
    listing_uuid
    lowest_guess_cents
    ordered_photo_uuids
    photo_in_game_comments
    position_in_game
    realty_asset
    realty_asset_uuid
    realty_game
    realty_game_listing_details
    realty_game_listing_flags
    realty_game_uuid
    rental_listing
    sale_listing
    scoot
    scoot_uuid
    statistics_updated_at
    total_guesses_count
    translations
    uuid
    visible_in_game
    visible_photo_uuids
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how realty game listings are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(realty_game_listing)
  #   "RealtyGameListing ##{realty_game_listing.id}"
  # end
end
