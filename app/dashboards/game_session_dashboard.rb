require 'administrate/base_dashboard'

class GameSessionDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    discarded_at: Field::DateTime,
    game_session_details: Field::String.with_options(searchable: false),
    game_session_flags: Field::Number,
    guessed_prices: Field::HasMany,
    guessed_prices_count: Field::Number,
    is_protected: Field::Boolean,
    main_realty_game_uuid: Field::String,
    main_scoot_uuid: Field::String,
    max_possible_score: Field::Number,
    performance_percentage: Field::String.with_options(searchable: false),
    performance_rating_color: Field::String,
    performance_rating_icon: Field::String,
    performance_rating_text: Field::String,
    realty_game: Field::BelongsTo,
    results_calculated_at: Field::DateTime,
    scoot: Field::BelongsTo,
    session_guest_name: Field::String,
    session_guest_title: Field::String,
    session_preferred_currency: Field::String,
    site_visitor_token: Field::String,
    total_score: Field::Number,
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
    ahoy_visit: Field::BelongsTo
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    ahoy_visit
    session_guest_title
    session_guest_name
    session_preferred_currency
    guessed_prices_count
    realty_game
    scoot
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    discarded_at
    game_session_details
    game_session_flags
    guessed_prices
    guessed_prices_count
    is_protected
    main_realty_game_uuid
    main_scoot_uuid
    max_possible_score
    performance_percentage
    performance_rating_color
    performance_rating_icon
    performance_rating_text
    realty_game
    results_calculated_at
    scoot
    session_guest_name
    session_guest_title
    session_preferred_currency
    site_visitor_token
    total_score
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    discarded_at
    game_session_details
    game_session_flags
    guessed_prices
    guessed_prices_count
    is_protected
    main_realty_game_uuid
    main_scoot_uuid
    max_possible_score
    performance_percentage
    performance_rating_color
    performance_rating_icon
    performance_rating_text
    realty_game
    results_calculated_at
    scoot
    session_guest_name
    session_guest_title
    session_preferred_currency
    site_visitor_token
    total_score
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how game sessions are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(game_session)
  #   "GameSession ##{game_session.id}"
  # end
end
