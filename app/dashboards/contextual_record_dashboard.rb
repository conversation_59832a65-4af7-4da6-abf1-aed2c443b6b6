require 'administrate/base_dashboard'

class ContextualRecordDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    aasm_state: Field::String,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    contxt_flags: Field::Number,
    contxt_outcode: Field::String,
    contxt_postcode: Field::String,
    contxt_type: Field::Number,
    discarded_at: Field::DateTime,
    dossier_asset_contextual_records: Field::String,
    dossier_assets: Field::HasMany,
    extra_contxt_details: Field::String.with_options(searchable: false),
    latest_scrape_item_uuid: Field::String,
    raw_contxt: Field::Text,
    record_source_url: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    aasm_state
    contxt_postcode
    created_at
    updated_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    aasm_state
    agency_tenant
    agency_tenant_uuid
    contxt_flags
    contxt_outcode
    contxt_postcode
    contxt_type
    discarded_at
    dossier_asset_contextual_records
    dossier_assets
    extra_contxt_details
    latest_scrape_item_uuid
    raw_contxt
    record_source_url
    translations
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    aasm_state
    agency_tenant
    agency_tenant_uuid
    contxt_flags
    contxt_outcode
    contxt_postcode
    contxt_type
    discarded_at
    dossier_asset_contextual_records
    dossier_assets
    extra_contxt_details
    latest_scrape_item_uuid
    raw_contxt
    record_source_url
    translations
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how contextual records are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(contextual_record)
  #   "ContextualRecord ##{contextual_record.id}"
  # end
end
