require 'administrate/base_dashboard'

class GuessedPriceDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    discarded_at: Field::DateTime,
    estimate_title: Field::String,
    estimator_name: Field::String,
    extra_uuid: Field::String,
    game_session: Field::BelongsTo,
    game_session_string: Field::String,
    game_session_uuid: Field::String,
    guessed_price_amount_cents: Field::Number,
    guessed_price_currency: Field::String,
    guessed_price_details: Field::String.with_options(searchable: false),
    guessed_price_flags: Field::Number,
    guessed_price_in_ui_currency_cents: Field::Number,
    is_ai_estimate: Field::Boolean,
    is_protected: Field::Boolean,
    listing_uuid: Field::String,
    notes_on_guess: Field::Text,
    percentage_above_or_below: Field::Number,
    price_at_time_of_estimate_cents: Field::Number,
    realty_game: Field::BelongsTo,
    realty_game_listing: Field::BelongsTo,
    realty_game_listing_uuid: Field::String,
    realty_game_uuid: Field::String,
    score_for_guess: Field::Number,
    source_currency: Field::String,
    ui_currency: Field::String,
    user: Field::BelongsTo,
    user_uuid: Field::String,
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    estimate_title
    ui_currency
    percentage_above_or_below
    estimator_name
    game_session
    realty_game_listing
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    discarded_at
    estimate_title
    estimator_name
    extra_uuid
    game_session
    game_session_string
    game_session_uuid
    guessed_price_amount_cents
    guessed_price_currency
    guessed_price_details
    guessed_price_flags
    guessed_price_in_ui_currency_cents
    is_ai_estimate
    is_protected
    listing_uuid
    notes_on_guess
    percentage_above_or_below
    price_at_time_of_estimate_cents
    realty_game
    realty_game_listing
    realty_game_listing_uuid
    realty_game_uuid
    score_for_guess
    source_currency
    ui_currency
    user
    user_uuid
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    discarded_at
    estimate_title
    estimator_name
    extra_uuid
    game_session
    game_session_string
    game_session_uuid
    guessed_price_amount_cents
    guessed_price_currency
    guessed_price_details
    guessed_price_flags
    guessed_price_in_ui_currency_cents
    is_ai_estimate
    is_protected
    listing_uuid
    notes_on_guess
    percentage_above_or_below
    price_at_time_of_estimate_cents
    realty_game
    realty_game_listing
    realty_game_listing_uuid
    realty_game_uuid
    score_for_guess
    source_currency
    ui_currency
    user
    user_uuid
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how guessed prices are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(guessed_price)
  #   "GuessedPrice ##{guessed_price.id}"
  # end
end
