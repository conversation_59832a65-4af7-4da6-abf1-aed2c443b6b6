require 'administrate/base_dashboard'

class ParticipantDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    visitor_token: Field::String,
    first_visit_at: Field::DateTime,
    last_visit_at: Field::DateTime,
    total_visits: Field::Number,
    total_events: Field::Number,
    total_page_views: Field::Number,
    average_session_duration: Field::Number.with_options(decimals: 2),
    unique_pages_visited: Field::Number,
    returning_visitor: Field::Boolean,
    first_referrer: Field::Text,
    first_landing_page: Field::Text,
    first_utm_source: Field::String,
    first_utm_medium: Field::String,
    first_utm_campaign: Field::String,
    first_country: Field::String,
    first_city: Field::String,
    first_device_type: Field::String,
    first_browser: Field::String,
    first_os: Field::String,
    engagement_metrics: Field::String.with_options(searchable: false),
    behavior_patterns: Field::String.with_options(searchable: false),
    ahoy_visits: Field::HasMany.with_options(class_name: 'Ahoy::Visit'),
    ahoy_events: Field::HasMany.with_options(class_name: 'Ahoy::Event'),
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    visitor_token
    total_visits
    total_events
    returning_visitor
    first_visit_at
    last_visit_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    visitor_token
    first_visit_at
    last_visit_at
    total_visits
    total_events
    total_page_views
    average_session_duration
    unique_pages_visited
    returning_visitor
    first_referrer
    first_landing_page
    first_utm_source
    first_utm_medium
    first_utm_campaign
    first_country
    first_city
    first_device_type
    first_browser
    first_os
    engagement_metrics
    behavior_patterns
    ahoy_visits
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    visitor_token
    first_visit_at
    last_visit_at
    total_visits
    total_events
    total_page_views
    average_session_duration
    unique_pages_visited
    returning_visitor
    first_referrer
    first_landing_page
    first_utm_source
    first_utm_medium
    first_utm_campaign
    first_country
    first_city
    first_device_type
    first_browser
    first_os
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {
    returning: ->(resources) { resources.returning },
    new_visitors: ->(resources) { resources.new_visitors }
    # high_engagement: ->(resources) { resources.high_engagement },
    # recent: ->(resources) { resources.recent },
    # explorer: ->(resources) { resources.joins(:engagement_metrics).where("engagement_metrics->>'behavior_category' = ?", 'explorer') },
    # engaged: ->(resources) { resources.joins(:engagement_metrics).where("engagement_metrics->>'behavior_category' = ?", 'engaged') },
    # regular: ->(resources) { resources.joins(:engagement_metrics).where("engagement_metrics->>'behavior_category' = ?", 'regular') },
    # casual: ->(resources) { resources.joins(:engagement_metrics).where("engagement_metrics->>'behavior_category' = ?", 'casual') }
  }.freeze

  # Overwrite this method to customize how participants are displayed
  # across all pages of the admin dashboard.
  def display_resource(participant)
    "Participant #{participant.visitor_token[0..8]}..."
  end

  # Custom methods for additional fields
  def engagement_score(participant)
    participant.engagement_score
  end

  # def behavior_category(participant)
  #   participant.behavior_category.capitalize
  # end

  # Override the default search to include custom logic
  def self.search_attributes
    %w[visitor_token first_country first_city first_device_type first_browser first_os]
  end
end
