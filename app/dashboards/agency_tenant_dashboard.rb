require 'administrate/base_dashboard'

class AgencyTenantDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    aasm_state: Field::String,
    agency_tenant_details: Field::String.with_options(searchable: false),
    agency_tenant_flags: Field::Number,
    agency_tenant_url: Field::String,
    analytics_id: Field::String,
    analytics_id_type: Field::Number,
    available_currencies: Field::Text,
    available_locales: Field::Text,
    client_forms_count: Field::Number,
    company_name: Field::String,
    contacts_count: Field::Number,
    default_admin_locale: Field::String,
    default_client_locale: Field::String,
    default_currency: Field::String,
    discarded_at: Field::DateTime,
    display_name: Field::String,
    domain: Field::String,
    email_for_general_contact_form: Field::String,
    email_for_property_contact_form: Field::String,
    email_primary: Field::String,
    event_store_events_count: Field::Number,
    guests_count: Field::Number,
    is_claimed: Field::Boolean,
    is_provisioned: Field::Boolean,
    messages_count: Field::Number,
    pages_count: Field::Number,
    payment_plan_id: Field::Number,
    phone_number_mobile: Field::String,
    phone_number_other: Field::String,
    phone_number_primary: Field::String,
    primary_address_id: Field::Number,
    primary_api_key: Field::String,
    primary_pwb_pro_user_uuid: Field::String,
    primary_pwb_user_uuid: Field::String,
    raw_css: Field::Text,
    realty_assets_count: Field::Number,
    rental_listings_count: Field::Number,
    sale_listings_count: Field::Number,
    secondary_address_id: Field::Number,
    site_configuration: Field::String.with_options(searchable: false),
    site_template_id: Field::Number,
    site_visitors_count: Field::Number,
    social_media: Field::String.with_options(searchable: false),
    subdomain: Field::String,
    supported_currencies: Field::Text,
    supported_locales: Field::Text,
    theme_name: Field::String,
    total_photos_count: Field::Number,
    translations: Field::String.with_options(searchable: false),
    users_count: Field::Number,
    uuid: Field::String,
    versions_count: Field::Number,
    websites_count: Field::Number,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
    # ActionView::Template::Error (undefined method `superwiser_user_path' for an instance of #<Class:0x00007cc086465320>)
    # Added below to try to fix the above error
    primary_pwb_user: Field::BelongsTo.with_options(class_name: 'Pwb::User'),
    pu_email: Field::String
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    domain
    subdomain
    created_at
    is_claimed
    is_provisioned
    realty_assets_count
    primary_pwb_user
    pu_email
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    primary_pwb_user
    pu_email
    aasm_state
    agency_tenant_details
    agency_tenant_flags
    agency_tenant_url
    analytics_id
    analytics_id_type
    available_currencies
    available_locales
    client_forms_count
    company_name
    contacts_count
    default_admin_locale
    default_client_locale
    default_currency
    discarded_at
    display_name
    domain
    email_for_general_contact_form
    email_for_property_contact_form
    email_primary
    event_store_events_count
    guests_count
    is_claimed
    is_provisioned
    messages_count
    pages_count
    payment_plan_id
    phone_number_mobile
    phone_number_other
    phone_number_primary
    primary_address_id
    primary_api_key
    primary_pwb_pro_user_uuid
    primary_pwb_user
    primary_pwb_user_uuid
    raw_css
    realty_assets_count
    rental_listings_count
    sale_listings_count
    secondary_address_id
    site_configuration
    site_template_id
    site_visitors_count
    social_media
    subdomain
    supported_currencies
    supported_locales
    theme_name
    total_photos_count
    translations
    users_count
    uuid
    versions_count
    websites_count
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    aasm_state
    agency_tenant_details
    agency_tenant_flags
    agency_tenant_url
    analytics_id
    analytics_id_type
    available_currencies
    available_locales
    client_forms_count
    company_name
    contacts_count
    default_admin_locale
    default_client_locale
    default_currency
    discarded_at
    display_name
    domain
    email_for_general_contact_form
    email_for_property_contact_form
    email_primary
    event_store_events_count
    guests_count
    is_claimed
    is_provisioned
    messages_count
    pages_count
    payment_plan_id
    phone_number_mobile
    phone_number_other
    phone_number_primary
    primary_address_id
    primary_api_key
    primary_pwb_pro_user_uuid
    primary_pwb_user
    primary_pwb_user_uuid
    raw_css
    realty_assets_count
    rental_listings_count
    sale_listings_count
    secondary_address_id
    site_configuration
    site_template_id
    site_visitors_count
    social_media
    subdomain
    supported_currencies
    supported_locales
    theme_name
    total_photos_count
    translations
    users_count
    uuid
    versions_count
    websites_count
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how agency tenants are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(agency_tenant)
  #   "AgencyTenant ##{agency_tenant.id}"
  # end
end
