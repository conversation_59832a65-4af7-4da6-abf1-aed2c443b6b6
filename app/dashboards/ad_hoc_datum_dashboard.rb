require "administrate/base_dashboard"

class AdHocDatumDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    ad_hoc_class_slug: Field::String,
    ad_hoc_data_enum: Field::Number,
    ad_hoc_data_flags: Field::Number,
    ad_hoc_data_item_slug: Field::String,
    ad_hoc_data_tags: Field::String,
    ad_hoc_foreign_class_slug: Field::String,
    ad_hoc_foreign_item_slug: Field::String,
    ad_hoc_meta: Field::String.with_options(searchable: false),
    ad_hoc_sub_class_slug: Field::String,
    agency_tenant_uuid: Field::String,
    agency_uuid: Field::String,
    discarded_at: Field::DateTime,
    json_representation: Field::String.with_options(searchable: false),
    site_visitor_token: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    ad_hoc_class_slug
    ad_hoc_data_enum
    ad_hoc_data_flags
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    ad_hoc_class_slug
    ad_hoc_data_enum
    ad_hoc_data_flags
    ad_hoc_data_item_slug
    ad_hoc_data_tags
    ad_hoc_foreign_class_slug
    ad_hoc_foreign_item_slug
    ad_hoc_meta
    ad_hoc_sub_class_slug
    agency_tenant_uuid
    agency_uuid
    discarded_at
    json_representation
    site_visitor_token
    translations
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    ad_hoc_class_slug
    ad_hoc_data_enum
    ad_hoc_data_flags
    ad_hoc_data_item_slug
    ad_hoc_data_tags
    ad_hoc_foreign_class_slug
    ad_hoc_foreign_item_slug
    ad_hoc_meta
    ad_hoc_sub_class_slug
    agency_tenant_uuid
    agency_uuid
    discarded_at
    json_representation
    site_visitor_token
    translations
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how ad hoc data are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(ad_hoc_datum)
  #   "AdHocDatum ##{ad_hoc_datum.id}"
  # end
end
