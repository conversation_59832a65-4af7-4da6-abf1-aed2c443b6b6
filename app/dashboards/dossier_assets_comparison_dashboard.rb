require 'administrate/base_dashboard'

class DossierAssetsComparisonDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    assets_parts_comparison: Field::String.with_options(searchable: false),
    buyer_guidance: Field::String.with_options(searchable: false),
    comparison_extra_details: Field::String.with_options(searchable: false),
    comparison_slug: Field::String,
    comparison_title: Field::String,
    detailed_comparison: Field::String.with_options(searchable: false),
    discarded_at: Field::DateTime,
    first_dossier_asset: Field::BelongsTo,
    first_dossier_asset_uuid: Field::String,
    market_analysis: Field::String.with_options(searchable: false),
    property_insights: Field::String.with_options(searchable: false),
    realty_dossier: Field::BelongsTo,
    realty_dossier_uuid: Field::String,
    second_dossier_asset: Field::BelongsTo,
    second_dossier_asset_uuid: Field::String,
    translations: Field::String.with_options(searchable: false),
    user_notes_on_comparison: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    uuid
    created_at
    updated_at
    first_dossier_asset
    realty_dossier
    second_dossier_asset
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    uuid
    created_at
    updated_at
    comparison_slug
    comparison_title
    detailed_comparison
    market_analysis
    property_insights
    buyer_guidance
    comparison_extra_details
    discarded_at
    first_dossier_asset
    first_dossier_asset_uuid
    realty_dossier
    realty_dossier_uuid
    second_dossier_asset
    second_dossier_asset_uuid
    agency_tenant
    agency_tenant_uuid
    assets_parts_comparison
    translations
    user_notes_on_comparison
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    assets_parts_comparison
    comparison_slug
    comparison_title
    discarded_at
    first_dossier_asset
    first_dossier_asset_uuid
    realty_dossier
    realty_dossier_uuid
    second_dossier_asset
    second_dossier_asset_uuid
    translations
    user_notes_on_comparison
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how dossier assets comparisons are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(dossier_assets_comparison)
  #   "DossierAssetsComparison ##{dossier_assets_comparison.id}"
  # end
end
