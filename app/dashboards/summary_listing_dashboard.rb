require 'administrate/base_dashboard'

class SummaryListingDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    discarded_at: Field::DateTime,
    full_listing_url: Field::String,
    full_listing_uuid: Field::String,
    realty_agent_details: Field::String.with_options(searchable: false),
    realty_asset_uuid: Field::String,
    realty_search_query_uuid: Field::String,
    scrape_item_uuid: Field::String,
    summary_listing_aasm_state: Field::String,
    summary_listing_bathrooms_count: Field::Number,
    summary_listing_bedrooms_count: Field::Number,
    summary_listing_details: Field::String.with_options(searchable: false),
    summary_listing_flags: Field::Number,
    summary_listing_import_url: Field::String,
    summary_listing_latitude: Field::Number.with_options(decimals: 2),
    summary_listing_long_address: Field::String,
    summary_listing_longitude: Field::Number.with_options(decimals: 2),
    summary_listing_postcode: Field::String,
    summary_listing_price: Field::String,
    summary_listing_source_site: Field::Number,
    summary_listing_uprn: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    summary_listing_postcode
    summary_listing_price
    summary_listing_source_site
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    summary_listing_price
    summary_listing_postcode
    summary_listing_source_site
    agency_tenant
    agency_tenant_uuid
    discarded_at
    full_listing_url
    full_listing_uuid
    realty_agent_details
    realty_asset_uuid
    realty_search_query_uuid
    scrape_item_uuid
    summary_listing_aasm_state
    summary_listing_bathrooms_count
    summary_listing_bedrooms_count
    summary_listing_details
    summary_listing_flags
    summary_listing_import_url
    summary_listing_latitude
    summary_listing_long_address
    summary_listing_longitude
    summary_listing_postcode
    summary_listing_uprn
    translations
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    discarded_at
    full_listing_url
    full_listing_uuid
    realty_agent_details
    realty_asset_uuid
    realty_search_query_uuid
    scrape_item_uuid
    summary_listing_aasm_state
    summary_listing_bathrooms_count
    summary_listing_bedrooms_count
    summary_listing_details
    summary_listing_flags
    summary_listing_import_url
    summary_listing_latitude
    summary_listing_long_address
    summary_listing_longitude
    summary_listing_postcode
    summary_listing_price
    summary_listing_source_site
    summary_listing_uprn
    translations
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how summary listings are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(summary_listing)
  #   "SummaryListing ##{summary_listing.id}"
  # end
end
