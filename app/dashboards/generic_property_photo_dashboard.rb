require "administrate/base_dashboard"

class GenericPropertyPhotoDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    cloned_from_uuid: Field::String,
    content_type: Field::String,
    details: Field::String.with_options(searchable: false),
    discarded_at: Field::DateTime,
    external_img_details: Field::String.with_options(searchable: false),
    file_size: Field::Number,
    folder: Field::String,
    generic_property: Field::BelongsTo,
    generic_property_image_attachment: Field::String, # HasOne,
    # 1 july 2025 - had to remove administrate-field-active_storage gem
    # to upgrade to ruby 3.4.3
    # generic_property_image: Field::ActiveStorage,
    generic_property_image_blob: Field::String, # HasOne,
    generic_property_section: Field::BelongsTo,
    generic_property_section_uuid: Field::String,
    generic_property_uuid: Field::String,
    gp_photo_description: Field::String,
    gp_photo_flags: Field::Number,
    gp_photo_gen_prompt: Field::Text,
    gp_photo_position_in_list: Field::Number,
    gp_photo_reference: Field::String,
    gp_photo_slug: Field::String,
    gp_photo_tags: Field::String,
    gp_photo_title: Field::String,
    height: Field::String,
    is_ai_generated_photo: Field::Boolean,
    is_external_photo: Field::Boolean,
    llm_interaction: Field::BelongsTo,
    llm_interaction_uuid: Field::String,
    process_options: Field::String.with_options(searchable: false),
    remote_photo_url: Field::String,
    site_visitor_token: Field::String,
    translations: Field::String.with_options(searchable: false),
    user_uuid: Field::String,
    uuid: Field::String,
    width: Field::String,
    zac_active_storage_attachment: Field::HasOne,
    zac_active_storage_blob: Field::HasOne,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    created_at
    gp_photo_slug
    gp_photo_tags
    gp_photo_title
    file_size
    height
    generic_property
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    gp_photo_reference
    gp_photo_slug
    gp_photo_tags
    gp_photo_title
    height
    file_size
    folder
    agency_tenant
    agency_tenant_uuid
    cloned_from_uuid
    content_type
    details
    discarded_at
    external_img_details
    generic_property_image
    generic_property
    generic_property_image_attachment
    generic_property_image_blob
    generic_property_section
    generic_property_section_uuid
    generic_property_uuid
    gp_photo_description
    gp_photo_flags
    gp_photo_gen_prompt
    gp_photo_position_in_list
    is_ai_generated_photo
    is_external_photo
    llm_interaction
    llm_interaction_uuid
    process_options
    remote_photo_url
    site_visitor_token
    translations
    user_uuid
    uuid
    width
    zac_active_storage_attachment
    zac_active_storage_blob
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    cloned_from_uuid
    content_type
    details
    discarded_at
    external_img_details
    file_size
    folder
    generic_property
    generic_property_image_attachment
    generic_property_image_blob
    generic_property_section
    generic_property_section_uuid
    generic_property_uuid
    gp_photo_description
    gp_photo_flags
    gp_photo_gen_prompt
    gp_photo_position_in_list
    gp_photo_reference
    gp_photo_slug
    gp_photo_tags
    gp_photo_title
    height
    is_ai_generated_photo
    is_external_photo
    llm_interaction
    llm_interaction_uuid
    process_options
    remote_photo_url
    site_visitor_token
    translations
    user_uuid
    uuid
    width
    zac_active_storage_attachment
    zac_active_storage_blob
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how generic property photos are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(generic_property_photo)
  #   "GenericPropertyPhoto ##{generic_property_photo.id}"
  # end
end
