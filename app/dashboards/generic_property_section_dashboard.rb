require 'administrate/base_dashboard'

class GenericPropertySectionDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    discarded_at: Field::DateTime,
    generic_property: Field::BelongsTo,
    generic_property_photos: Field::String, #  Field::HasMany,
    generic_property_uuid: Field::String,
    gp_section_area_sq_ft: Field::String.with_options(searchable: false),
    gp_section_area_sq_mt: Field::String.with_options(searchable: false),
    gp_section_description: Field::String,
    gp_section_details: Field::String.with_options(searchable: false),
    gp_section_dimensions: Field::String,
    gp_section_flags: Field::Number,
    gp_section_photos_count: Field::Number,
    gp_section_position_in_list: Field::Number,
    gp_section_reference: Field::String,
    gp_section_slug: Field::String,
    gp_section_tags: Field::String,
    gp_section_title: Field::String,
    gp_section_type: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    is_room: Field::Boolean,
    llm_interaction_uuid: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    gp_section_slug
    generic_property
    gp_section_area_sq_mt
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    gp_section_slug
    generic_property_uuid
    gp_section_reference
    gp_section_tags
    gp_section_title
    gp_section_type
    is_room
    llm_interaction_uuid
    gp_section_area_sq_ft
    gp_section_area_sq_mt
    gp_section_description
    gp_section_details
    gp_section_dimensions
    agency_tenant
    agency_tenant_uuid
    created_at
    updated_at
    discarded_at
    generic_property
    generic_property_photos
    gp_section_flags
    gp_section_photos_count
    gp_section_position_in_list
    translations
    uuid
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    discarded_at
    generic_property
    generic_property_photos
    generic_property_uuid
    gp_section_area_sq_ft
    gp_section_area_sq_mt
    gp_section_description
    gp_section_details
    gp_section_dimensions
    gp_section_flags
    gp_section_photos_count
    gp_section_position_in_list
    gp_section_reference
    gp_section_slug
    gp_section_tags
    gp_section_title
    gp_section_type
    is_room
    llm_interaction_uuid
    translations
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how generic property sections are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(generic_property_section)
  #   "GenericPropertySection ##{generic_property_section.id}"
  # end
end
