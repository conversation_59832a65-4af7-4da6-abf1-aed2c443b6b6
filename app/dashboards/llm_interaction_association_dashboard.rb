require 'administrate/base_dashboard'

class LlmInteractionAssociationDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    associable: Field::Polymorphic,
    association_metadata: Field::String.with_options(searchable: false),
    association_type: Field::String,
    discarded_at: Field::DateTime,
    llm_interaction: Field::BelongsTo,
    llm_interaction_assoc_aasm_state: Field::String,
    llm_interaction_assoc_flags: Field::Number,
    llm_interaction_uuid: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    association_type
    created_at
    associable
    llm_interaction
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    associable
    association_metadata
    association_type
    discarded_at
    llm_interaction
    llm_interaction_assoc_aasm_state
    llm_interaction_assoc_flags
    llm_interaction_uuid
    translations
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    associable
    association_metadata
    association_type
    discarded_at
    llm_interaction
    llm_interaction_assoc_aasm_state
    llm_interaction_assoc_flags
    llm_interaction_uuid
    translations
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how llm interaction associations are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(llm_interaction_association)
  #   "LlmInteractionAssociation ##{llm_interaction_association.id}"
  # end
end
