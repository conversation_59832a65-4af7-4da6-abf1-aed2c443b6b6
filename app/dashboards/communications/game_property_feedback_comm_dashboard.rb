require 'administrate/base_dashboard'

class Communications::GamePropertyFeedbackCommDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    ahoy_visit: Field::BelongsTo,
    guessed_price: Field::BelongsTo,
    session_guest_name: Field::String,
    id: Field::Number,
    aasm_state: Field::String,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    comm_flags: Field::Number,
    comm_form_name: Field::String,
    comm_form_params: Field::String.with_options(searchable: false),
    game_session_id: Field::String.with_options(searchable: true, accessor: ->(resource) { resource.comm_form_params['game_session_id'] }),
    general_feedback: Field::String.with_options(searchable: true, accessor: ->(resource) { resource.comm_form_params['general_feedback'] }),
    property_title: Field::String,
    # .with_options(accessor: ->(resource) { resource.comm_form_params['property_title'] }),
    email: Field::String.with_options(searchable: true, accessor: ->(resource) { resource.comm_form_params['email'] }),
    subdomain: Field::String.with_options(searchable: true, accessor: ->(resource) { resource.comm_form_params['subdomain'] }),
    property_uuid: Field::String.with_options(searchable: true, accessor: ->(resource) { resource.comm_form_params['property_uuid'] }),
    submitted_at: Field::DateTime.with_options(accessor: ->(resource) { resource.comm_form_params['submitted_at'] }),
    user_agent: Field::String.with_options(searchable: false, accessor: ->(resource) { resource.comm_form_params['user_agent'] }),
    page_url: Field::String.with_options(searchable: false, accessor: ->(resource) { resource.comm_form_params['page_url'] }),
    comm_text: Field::Text,
    comm_type: Field::Number,
    discarded_at: Field::DateTime,
    extra_comm_details: Field::String.with_options(searchable: false),
    origin_user_uuid: Field::String,
    package_code: Field::String,
    primary_assoc_type: Field::String,
    primary_assoc_uuid: Field::String,
    request_referrer: Field::String,
    secondary_assoc_id: Field::Number,
    secondary_assoc_type: Field::String,
    subdomain_uuid: Field::String,
    target_user_uuid: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    viewed_at: Field::DateTime,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    guessed_price
    session_guest_name
    ahoy_visit
    subdomain
    game_session_id
    created_at
    email
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    aasm_state
    agency_tenant
    agency_tenant_uuid
    comm_flags
    comm_form_name
    comm_form_params
    game_session_id
    general_feedback
    property_title
    email
    subdomain
    property_uuid
    submitted_at
    user_agent
    page_url
    comm_text
    comm_type
    discarded_at
    extra_comm_details
    guessed_price
    origin_user_uuid
    package_code
    primary_assoc_type
    primary_assoc_uuid
    request_referrer
    secondary_assoc_id
    secondary_assoc_type
    subdomain_uuid
    target_user_uuid
    translations
    uuid
    viewed_at
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    aasm_state
    agency_tenant
    agency_tenant_uuid
    comm_flags
    comm_form_name
    game_session_id
    general_feedback
    property_title
    email
    subdomain
    property_uuid
    submitted_at
    user_agent
    page_url
    comm_text
    comm_type
    discarded_at
    extra_comm_details
    guessed_price
    origin_user_uuid
    package_code
    primary_assoc_type
    primary_assoc_uuid
    request_referrer
    secondary_assoc_id
    secondary_assoc_type
    subdomain_uuid
    target_user_uuid
    translations
    uuid
    viewed_at
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how game feedback comms are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(game_feedback_comm)
  #   "Communications::GameFeedbackComm ##{game_feedback_comm.id}"
  # end
end
