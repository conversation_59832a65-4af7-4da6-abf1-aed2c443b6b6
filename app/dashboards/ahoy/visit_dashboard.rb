require 'administrate/base_dashboard'

class Ahoy::VisitDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    app_version: Field::String,
    browser: Field::String,
    city: Field::String,
    country: Field::String,
    device_type: Field::String,
    events: Field::HasMany,
    ip: Field::String,
    landing_page: Field::Text,
    latitude: Field::Number.with_options(decimals: 2),
    longitude: Field::Number.with_options(decimals: 2),
    os: Field::String,
    os_version: Field::String,
    platform: Field::String,
    referrer: Field::Text,
    referring_domain: Field::String,
    region: Field::String,
    started_at: Field::DateTime,
    # user: Field::BelongsTo,
    user_agent: Field::Text,
    utm_campaign: Field::String,
    utm_content: Field::String,
    utm_medium: Field::String,
    utm_source: Field::String,
    utm_term: Field::String,
    visit_token: Field::String,
    visitor_token: Field::String
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    landing_page
    os
    ip
    device_type
    started_at
    browser
    visitor_token
    city
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    app_version
    browser
    city
    country
    device_type
    events
    ip
    landing_page
    latitude
    longitude
    os
    os_version
    platform
    referrer
    referring_domain
    region
    started_at
    user_agent
    utm_campaign
    utm_content
    utm_medium
    utm_source
    utm_term
    visit_token
    visitor_token
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    app_version
    browser
    city
    country
    device_type
    events
    ip
    landing_page
    latitude
    longitude
    os
    os_version
    platform
    referrer
    referring_domain
    region
    started_at
    user_agent
    utm_campaign
    utm_content
    utm_medium
    utm_source
    utm_term
    visit_token
    visitor_token
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how visits are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(visit)
  #   "Ahoy::Visit ##{visit.id}"
  # end
end
