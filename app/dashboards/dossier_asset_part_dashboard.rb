require 'administrate/base_dashboard'

class DossierAssetPartDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    asset_part_condition: Field::String,
    asset_part_description: Field::String,
    asset_part_details: Field::String.with_options(searchable: false),
    asset_part_flags: Field::Number,
    asset_part_main_color: Field::String,
    asset_part_secondary_color: Field::String,
    asset_part_significant_items: Field::Text,
    asset_part_slug: Field::String,
    asset_part_style: Field::String,
    asset_part_title: Field::String,
    asset_part_type: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    asset_part_unique_features: Field::String.with_options(searchable: false),
    dap_area_sq_feet: Field::String,
    dap_area_sq_meters: Field::String,
    discarded_at: Field::DateTime,
    dossier_asset: Field::BelongsTo,
    dossier_asset_uuid: Field::String,
    is_heated: Field::Boolean,
    is_outside: Field::Boolean,
    realty_asset: Field::BelongsTo,
    realty_asset_photo_uuids: Field::Text,
    realty_asset_uuid: Field::String,
    realty_dossier: Field::BelongsTo,
    realty_dossier_uuid: Field::String,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    asset_part_slug
    realty_dossier
    dossier_asset
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    asset_part_condition
    asset_part_description
    asset_part_details
    asset_part_flags
    asset_part_main_color
    asset_part_secondary_color
    asset_part_significant_items
    asset_part_slug
    asset_part_style
    asset_part_title
    asset_part_type
    asset_part_unique_features
    dap_area_sq_feet
    dap_area_sq_meters
    discarded_at
    dossier_asset
    dossier_asset_uuid
    is_heated
    is_outside
    realty_asset
    realty_asset_photo_uuids
    realty_asset_uuid
    realty_dossier
    realty_dossier_uuid
    translations
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    asset_part_condition
    asset_part_description
    asset_part_details
    asset_part_flags
    asset_part_main_color
    asset_part_secondary_color
    asset_part_significant_items
    asset_part_slug
    asset_part_style
    asset_part_title
    asset_part_type
    asset_part_unique_features
    dap_area_sq_feet
    dap_area_sq_meters
    discarded_at
    dossier_asset
    dossier_asset_uuid
    is_heated
    is_outside
    realty_asset
    realty_asset_photo_uuids
    realty_asset_uuid
    realty_dossier
    realty_dossier_uuid
    translations
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how dossier asset parts are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(dossier_asset_part)
  #   "DossierAssetPart ##{dossier_asset_part.id}"
  # end
end
