require 'administrate/base_dashboard'

class Pwb::PropDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    active_from: Field::DateTime,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    archived: Field::Boolean,
    area_unit: Field::Select.with_options(searchable: false, collection: lambda { |field|
                                                                           field.resource.class.send(field.attribute.to_s.pluralize).keys
                                                                         }),
    available_to_rent_from: Field::DateTime,
    available_to_rent_till: Field::DateTime,
    city: Field::String,
    commission_cents: Field::Number,
    commission_currency: Field::String,
    constructed_area: Field::Number.with_options(decimals: 2),
    count_bathrooms: Field::Number.with_options(decimals: 2),
    count_bedrooms: Field::Number,
    count_garages: Field::Number,
    count_toilets: Field::Number,
    country: Field::String,
    currency: Field::String,
    deleted_at: Field::DateTime,
    energy_performance: Field::Number.with_options(decimals: 2),
    energy_rating: Field::Number,
    # features: Field::HasMany,
    flags: Field::Number,
    for_rent_long_term: Field::Boolean,
    for_rent_short_term: Field::Boolean,
    for_sale: Field::Boolean,
    furnished: Field::Boolean,
    hide_map: Field::Boolean,
    highlighted: Field::Boolean,
    latitude: Field::Number.with_options(decimals: 2),
    longitude: Field::Number.with_options(decimals: 2),
    obscure_map: Field::Boolean,
    plot_area: Field::Number.with_options(decimals: 2),
    portals_enabled: Field::Boolean,
    postal_code: Field::String,
    price_rental_monthly_current_cents: Field::Number,
    price_rental_monthly_current_currency: Field::String,
    price_rental_monthly_for_search_cents: Field::Number,
    price_rental_monthly_for_search_currency: Field::String,
    price_rental_monthly_high_season_cents: Field::Number,
    price_rental_monthly_high_season_currency: Field::String,
    price_rental_monthly_low_season_cents: Field::Number,
    price_rental_monthly_low_season_currency: Field::String,
    price_rental_monthly_original_cents: Field::Number,
    price_rental_monthly_original_currency: Field::String,
    price_rental_monthly_standard_season_cents: Field::Number,
    price_rental_monthly_standard_season_currency: Field::String,
    price_sale_current_cents: Field::Number,
    price_sale_current_currency: Field::String,
    price_sale_original_cents: Field::Number,
    price_sale_original_currency: Field::String,
    prop_origin_key: Field::String,
    # prop_photos: Field::HasMany,
    prop_state_key: Field::String,
    prop_type_key: Field::String,
    province: Field::String,
    reference: Field::String,
    region: Field::String,
    reserved: Field::Boolean,
    service_charge_yearly_cents: Field::Number,
    service_charge_yearly_currency: Field::String,
    sold: Field::Boolean,
    street_address: Field::String,
    street_name: Field::String,
    street_number: Field::String,
    # translations: Field::HasMany,
    visible: Field::Boolean,
    year_construction: Field::Number,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    reference
    agency_tenant
    created_at
    price_sale_current_cents
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    active_from
    agency_tenant
    agency_tenant_uuid
    archived
    area_unit
    available_to_rent_from
    available_to_rent_till
    city
    commission_cents
    commission_currency
    constructed_area
    count_bathrooms
    count_bedrooms
    count_garages
    count_toilets
    country
    currency
    deleted_at
    energy_performance
    energy_rating
    flags
    for_rent_long_term
    for_rent_short_term
    for_sale
    furnished
    hide_map
    highlighted
    latitude
    longitude
    obscure_map
    plot_area
    portals_enabled
    postal_code
    price_rental_monthly_current_cents
    price_rental_monthly_current_currency
    price_rental_monthly_for_search_cents
    price_rental_monthly_for_search_currency
    price_rental_monthly_high_season_cents
    price_rental_monthly_high_season_currency
    price_rental_monthly_low_season_cents
    price_rental_monthly_low_season_currency
    price_rental_monthly_original_cents
    price_rental_monthly_original_currency
    price_rental_monthly_standard_season_cents
    price_rental_monthly_standard_season_currency
    price_sale_current_cents
    price_sale_current_currency
    price_sale_original_cents
    price_sale_original_currency
    prop_origin_key
    prop_state_key
    prop_type_key
    province
    reference
    region
    reserved
    service_charge_yearly_cents
    service_charge_yearly_currency
    sold
    street_address
    street_name
    street_number
    visible
    year_construction
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    active_from
    agency_tenant
    agency_tenant_uuid
    archived
    area_unit
    available_to_rent_from
    available_to_rent_till
    city
    commission_cents
    commission_currency
    constructed_area
    count_bathrooms
    count_bedrooms
    count_garages
    count_toilets
    country
    currency
    deleted_at
    energy_performance
    energy_rating
    flags
    for_rent_long_term
    for_rent_short_term
    for_sale
    furnished
    hide_map
    highlighted
    latitude
    longitude
    obscure_map
    plot_area
    portals_enabled
    postal_code
    price_rental_monthly_current_cents
    price_rental_monthly_current_currency
    price_rental_monthly_for_search_cents
    price_rental_monthly_for_search_currency
    price_rental_monthly_high_season_cents
    price_rental_monthly_high_season_currency
    price_rental_monthly_low_season_cents
    price_rental_monthly_low_season_currency
    price_rental_monthly_original_cents
    price_rental_monthly_original_currency
    price_rental_monthly_standard_season_cents
    price_rental_monthly_standard_season_currency
    price_sale_current_cents
    price_sale_current_currency
    price_sale_original_cents
    price_sale_original_currency
    prop_origin_key
    prop_state_key
    prop_type_key
    province
    reference
    region
    reserved
    service_charge_yearly_cents
    service_charge_yearly_currency
    sold
    street_address
    street_name
    street_number
    visible
    year_construction
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how props are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(prop)
  #   "Pwb::Prop ##{prop.id}"
  # end
end
