require 'administrate/base_dashboard'

class Pwb::UserDashboard < Administrate::BaseDashboard
  # Set default sort order
  ORDER_AT = :updated_at # Attribute to sort by
  ORDER_DIRECTION = :asc # Sort direction, either :asc or :desc
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    admin: Field::Boolean,
    agency_tenant_uuid: Field::String,
    authentication_token: Field::String,
    # authorizations: Field::HasMany,
    confirmation_sent_at: Field::DateTime,
    confirmation_token: Field::String,
    confirmed_at: Field::DateTime,
    current_sign_in_at: Field::DateTime,
    current_sign_in_ip: Field::String,
    default_admin_locale: Field::String,
    default_client_locale: Field::String,
    default_currency: Field::String,
    email: Field::String,
    encrypted_password: Field::String,
    failed_attempts: Field::Number,
    first_names: Field::String,
    last_names: Field::String,
    last_sign_in_at: Field::DateTime,
    last_sign_in_ip: Field::String,
    locked_at: Field::DateTime,
    phone_number_primary: Field::String,
    remember_created_at: Field::DateTime,
    reset_password_sent_at: Field::DateTime,
    reset_password_token: Field::String,
    sign_in_count: Field::Number,
    skype: Field::String,
    unconfirmed_email: Field::String,
    unlock_token: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
    primary_agency_tenant: Field::HasOne.with_options(class_name: 'AgencyTenant'),
    #  Field::BelongsTo.with_options(class_name: 'AgencyTenant')
    pat_name: Field::String
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    admin
    email
    skype
    created_at
    updated_at
    primary_agency_tenant
    pat_name
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    pat_name
    admin
    agency_tenant_uuid
    authentication_token
    confirmation_sent_at
    confirmation_token
    confirmed_at
    current_sign_in_at
    current_sign_in_ip
    default_admin_locale
    default_client_locale
    default_currency
    email
    encrypted_password
    failed_attempts
    first_names
    last_names
    last_sign_in_at
    last_sign_in_ip
    locked_at
    phone_number_primary
    remember_created_at
    reset_password_sent_at
    reset_password_token
    sign_in_count
    skype
    unconfirmed_email
    unlock_token
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    admin
    agency_tenant_uuid
    authentication_token
    confirmation_sent_at
    confirmation_token
    confirmed_at
    current_sign_in_at
    current_sign_in_ip
    default_admin_locale
    default_client_locale
    default_currency
    email
    encrypted_password
    failed_attempts
    first_names
    last_names
    last_sign_in_at
    last_sign_in_ip
    locked_at
    phone_number_primary
    remember_created_at
    reset_password_sent_at
    reset_password_token
    sign_in_count
    skype
    unconfirmed_email
    unlock_token
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how users are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(user)
  #   "Pwb::User ##{user.id}"
  # end
end
