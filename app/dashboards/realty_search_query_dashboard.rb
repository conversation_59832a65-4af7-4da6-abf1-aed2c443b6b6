require 'administrate/base_dashboard'

class RealtySearchQueryDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    aasm_state: Field::String,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    agency_uuid: Field::String,
    average_results_count: Field::Number,
    summary_listings_count: Field::Number,
    default_sort_criteria: Field::Number,
    discarded_at: Field::DateTime,
    indoor_area_max: Field::Number,
    indoor_area_min: Field::Number,
    locale: Field::String,
    parent_query_uuid: Field::String,
    plot_area_max: Field::Number,
    plot_area_min: Field::Number,
    property_type: Field::Number,
    property_type_string: Field::String,
    query_rating: Field::Number,
    query_traits: Field::String,
    realty_search_details: Field::String.with_options(searchable: false),
    realty_search_flags: Field::Number,
    scrape_items: Field::HasMany,
    search_area_unit: Field::Number,
    search_bathrooms_max: Field::Number,
    search_bathrooms_min: Field::Number,
    search_bedrooms_max: Field::Number,
    search_bedrooms_min: Field::Number,
    search_cities: Field::Text,
    search_city: Field::String,
    search_currency: Field::String,
    search_lat_lng_bounds: Field::String.with_options(searchable: false),
    search_latitude_center: Field::Number.with_options(decimals: 2),
    search_longitude_center: Field::Number.with_options(decimals: 2),
    search_postcode: Field::String,
    search_price_max: Field::Number,
    search_price_min: Field::Number,
    search_query_slug: Field::String,
    search_query_url: Field::String,
    search_query_url_template: Field::String,
    search_source: Field::Number,
    summary_listings: Field::HasMany,
    transaction_type: Field::Number,
    translations: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    search_postcode
    search_price_max
    search_price_min
    search_query_slug
    search_query_url
    summary_listings_count
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    search_postcode
    search_price_max
    search_price_min
    search_query_slug
    search_query_url
    search_query_url_template
    aasm_state
    agency_tenant
    agency_tenant_uuid
    agency_uuid
    average_results_count
    summary_listings_count
    default_sort_criteria
    discarded_at
    indoor_area_max
    indoor_area_min
    locale
    parent_query_uuid
    plot_area_max
    plot_area_min
    property_type
    property_type_string
    query_rating
    query_traits
    realty_search_details
    realty_search_flags
    scrape_items
    search_area_unit
    search_bathrooms_max
    search_bathrooms_min
    search_bedrooms_max
    search_bedrooms_min
    search_cities
    search_city
    search_currency
    search_lat_lng_bounds
    search_latitude_center
    search_longitude_center
    search_source
    summary_listings
    transaction_type
    translations
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    aasm_state
    agency_tenant
    agency_tenant_uuid
    agency_uuid
    average_results_count
    summary_listings_count
    default_sort_criteria
    discarded_at
    indoor_area_max
    indoor_area_min
    locale
    parent_query_uuid
    plot_area_max
    plot_area_min
    property_type
    property_type_string
    query_rating
    query_traits
    realty_search_details
    realty_search_flags
    scrape_items
    search_area_unit
    search_bathrooms_max
    search_bathrooms_min
    search_bedrooms_max
    search_bedrooms_min
    search_cities
    search_city
    search_currency
    search_lat_lng_bounds
    search_latitude_center
    search_longitude_center
    search_postcode
    search_price_max
    search_price_min
    search_query_slug
    search_query_url
    search_query_url_template
    search_source
    summary_listings
    transaction_type
    translations
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how realty search queries are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(realty_search_query)
  #   "RealtySearchQuery ##{realty_search_query.id}"
  # end
end
