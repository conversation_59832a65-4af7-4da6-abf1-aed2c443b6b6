require 'administrate/base_dashboard'

class GeoClusterDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    bbox_max_latitude: Field::String.with_options(searchable: false),
    bbox_max_longitude: Field::String.with_options(searchable: false),
    bbox_min_latitude: Field::String.with_options(searchable: false),
    bbox_min_longitude: Field::String.with_options(searchable: false),
    benchmark_property: Field::BelongsTo,
    benchmark_property_uuid: Field::String,
    center_latitude: Field::String.with_options(searchable: false),
    center_longitude: Field::String.with_options(searchable: false),
    child_clusters_count: Field::Number,
    cluster_average_property_price_cents: Field::Number,
    cluster_average_property_price_currency: Field::String,
    cluster_city: Field::String,
    cluster_country: Field::String,
    cluster_description: Field::Text,
    cluster_flags: Field::Number,
    cluster_geojsons: Field::String.with_options(searchable: false),
    cluster_group_name: Field::String,
    cluster_name: Field::String,
    cluster_outcode: Field::String,
    cluster_postcode_district: Field::String,
    cluster_region: Field::String,
    cluster_slug: Field::String,
    default_zoom_level: Field::String,
    discarded_at: Field::DateTime,
    extra_cluster_details: Field::String.with_options(searchable: false),
    g_places: Field::String.with_options(searchable: false),
    geo_cluster_places: Field::String, # HasMany,
    is_postcode_cluster: Field::Boolean,
    number_of_postcode_areas: Field::Number,
    parent_cluster: Field::BelongsTo,
    parent_cluster_uuid: Field::String,
    postcode_areas: Field::String, # HasMany,
    postcode_cluster_tags: Field::String,
    postcode_geo_clusters: Field::String, # HasMany,
    real_sold_transactions: Field::String, # HasMany,
    relevant_places: Field::String, # HasMany,
    sold_transactions: Field::HasMany,
    sold_transactions_count: Field::Number,
    synthetic_sold_transactions: Field::HasMany,
    total_population: Field::Number,
    translations: Field::String.with_options(searchable: false),
    travel_to_work_area: Field::String,
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    cluster_city
    cluster_country
    cluster_description
    cluster_flags
    cluster_geojsons
    cluster_group_name
    cluster_name
    cluster_outcode
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    created_at
    updated_at
    cluster_city
    cluster_country
    cluster_description
    cluster_flags
    cluster_geojsons
    cluster_group_name
    cluster_name
    cluster_outcode
    cluster_postcode_district
    cluster_region
    cluster_slug
    agency_tenant
    agency_tenant_uuid
    bbox_max_latitude
    bbox_max_longitude
    bbox_min_latitude
    bbox_min_longitude
    benchmark_property
    benchmark_property_uuid
    center_latitude
    center_longitude
    child_clusters_count
    cluster_average_property_price_cents
    cluster_average_property_price_currency
    default_zoom_level
    discarded_at
    extra_cluster_details
    g_places
    geo_cluster_places
    is_postcode_cluster
    number_of_postcode_areas
    parent_cluster
    parent_cluster_uuid
    postcode_areas
    postcode_cluster_tags
    postcode_geo_clusters
    real_sold_transactions
    relevant_places
    sold_transactions
    sold_transactions_count
    synthetic_sold_transactions
    total_population
    translations
    travel_to_work_area
    uuid
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    bbox_max_latitude
    bbox_max_longitude
    bbox_min_latitude
    bbox_min_longitude
    benchmark_property
    benchmark_property_uuid
    center_latitude
    center_longitude
    child_clusters_count
    cluster_average_property_price_cents
    cluster_average_property_price_currency
    cluster_city
    cluster_country
    cluster_description
    cluster_flags
    cluster_geojsons
    cluster_group_name
    cluster_name
    cluster_outcode
    cluster_postcode_district
    cluster_region
    cluster_slug
    default_zoom_level
    discarded_at
    extra_cluster_details
    g_places
    geo_cluster_places
    is_postcode_cluster
    number_of_postcode_areas
    parent_cluster
    parent_cluster_uuid
    postcode_areas
    postcode_cluster_tags
    postcode_geo_clusters
    real_sold_transactions
    relevant_places
    sold_transactions
    sold_transactions_count
    synthetic_sold_transactions
    total_population
    translations
    travel_to_work_area
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how geo clusters are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(geo_cluster)
  #   "GeoCluster ##{geo_cluster.id}"
  # end
end
