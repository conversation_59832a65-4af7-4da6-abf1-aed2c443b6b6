require 'administrate/base_dashboard'

class SoldTransactionEpcDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    association_certainty: Field::Number,
    best_estimated_price: Field::Number,
    best_estimated_price_off_by: Field::Number,
    days_difference_epc_and_sale: Field::Number,
    discarded_at: Field::DateTime,
    epc_address: Field::String,
    epc_detail: Field::BelongsTo,
    epc_detail_uuid: Field::String,
    epc_inspection_date: Field::Date,
    is_best_combo: Field::Boolean,
    is_outlier: Field::Boolean,
    outlier_reason: Field::String,
    outlier_score: Field::Number,
    postcode_area: Field::HasOne,
    postcode_area_uuid: Field::String,
    price_per_floor_area_meter: Field::Number,
    price_per_habitable_room: Field::Number,
    realty_asset: Field::BelongsTo,
    realty_asset_uuid: Field::String,
    sold_transaction: Field::BelongsTo,
    sold_transaction_date: Field::Date,
    sold_transaction_uuid: Field::String,
    st_address: Field::String,
    st_epc_extra_details: Field::String.with_options(searchable: false),
    st_epc_flags: Field::Number,
    st_epc_latitude: Field::Number.with_options(decimals: 2),
    st_epc_longitude: Field::Number.with_options(decimals: 2),
    st_epc_outcode: Field::String,
    st_epc_postcode: Field::String,
    st_epc_price_predictions: Field::String.with_options(searchable: false),
    st_epc_uprn: Field::String,
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    price_per_habitable_room
    price_per_floor_area_meter
    is_outlier
    outlier_score
    days_difference_epc_and_sale
    epc_address
    best_estimated_price
    best_estimated_price_off_by
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    association_certainty
    best_estimated_price
    best_estimated_price_off_by
    days_difference_epc_and_sale
    discarded_at
    epc_address
    epc_detail
    epc_detail_uuid
    epc_inspection_date
    is_best_combo
    is_outlier
    outlier_reason
    outlier_score
    postcode_area
    postcode_area_uuid
    price_per_floor_area_meter
    price_per_habitable_room
    realty_asset
    realty_asset_uuid
    sold_transaction
    sold_transaction_date
    sold_transaction_uuid
    st_address
    st_epc_extra_details
    st_epc_flags
    st_epc_latitude
    st_epc_longitude
    st_epc_outcode
    st_epc_postcode
    st_epc_price_predictions
    st_epc_uprn
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    association_certainty
    best_estimated_price
    best_estimated_price_off_by
    days_difference_epc_and_sale
    discarded_at
    epc_address
    epc_detail
    epc_detail_uuid
    epc_inspection_date
    is_best_combo
    is_outlier
    outlier_reason
    outlier_score
    postcode_area
    postcode_area_uuid
    price_per_floor_area_meter
    price_per_habitable_room
    realty_asset
    realty_asset_uuid
    sold_transaction
    sold_transaction_date
    sold_transaction_uuid
    st_address
    st_epc_extra_details
    st_epc_flags
    st_epc_latitude
    st_epc_longitude
    st_epc_outcode
    st_epc_postcode
    st_epc_price_predictions
    st_epc_uprn
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how sold transaction epcs are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(sold_transaction_epc)
  #   "SoldTransactionEpc ##{sold_transaction_epc.id}"
  # end
end
