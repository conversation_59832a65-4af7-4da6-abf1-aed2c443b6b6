require 'administrate/base_dashboard'

class SaleListingDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    agency_uuid: Field::String,
    archived: Field::Boolean,
    cloned_from_uuid: Field::String,
    commission_cents: Field::Number,
    commission_currency: Field::String,
    currency: Field::String,
    extra_sale_details: Field::String.with_options(searchable: false),
    discarded_at: Field::DateTime,
    sale_listing_flags: Field::Number,
    furnished: Field::Boolean,
    hide_map: Field::Boolean,
    highlighted: Field::Boolean,
    host_on_create: Field::String,
    import_url: Field::String,
    listing_pages_count: Field::Number,
    listing_photos: Field::Has<PERSON>any,
    main_video_url: Field::String,
    obscure_map: Field::<PERSON>olean,
    ordered_visible_listing_photos: Field::HasMany,
    page_section_listings_count: Field::Number,
    sl_photos_count: Field::Number,
    price_sale_current_cents: Field::Number,
    price_sale_current_currency: Field::String,
    price_sale_original_cents: Field::Number,
    price_sale_original_currency: Field::String,
    property_board_items_count: Field::Number,
    psq_visit_id: Field::Number,
    publish_from: Field::DateTime,
    publish_till: Field::DateTime,
    realty_asset: Field::BelongsTo,
    realty_asset_uuid: Field::String,
    reference: Field::String,
    related_urls: Field::String.with_options(searchable: false),
    reserved: Field::Boolean,
    service_charge_yearly_cents: Field::Number,
    service_charge_yearly_currency: Field::String,
    site_visitor_token: Field::String,
    translations: Field::String.with_options(searchable: false),
    user_uuid: Field::String,
    uuid: Field::String,
    versions: Field::String, # Field::HasMany,
    versions_count: Field::Number,
    visible: Field::Boolean,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    agency_tenant
    reference
    host_on_create
    sl_photos_count
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    agency_uuid
    archived
    cloned_from_uuid
    commission_cents
    commission_currency
    currency
    extra_sale_details
    discarded_at
    sale_listing_flags
    furnished
    hide_map
    highlighted
    host_on_create
    import_url
    listing_pages_count
    listing_photos
    main_video_url
    obscure_map
    ordered_visible_listing_photos
    page_section_listings_count
    sl_photos_count
    price_sale_current_cents
    price_sale_current_currency
    price_sale_original_cents
    price_sale_original_currency
    property_board_items_count
    psq_visit_id
    publish_from
    publish_till
    realty_asset
    realty_asset_uuid
    reference
    related_urls
    reserved
    service_charge_yearly_cents
    service_charge_yearly_currency
    site_visitor_token
    translations
    user_uuid
    uuid
    versions
    versions_count
    visible
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    price_sale_current_cents
    visible
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how sale listings are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(sale_listing)
  #   "SaleListing ##{sale_listing.id}"
  # end
end
