require 'administrate/base_dashboard'

class DossierInfoSourceDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    associated_listing_uuid: Field::String,
    discarded_at: Field::DateTime,
    dossier_asset: Field::BelongsTo,
    dossier_asset_uuid: Field::String,
    dossier_info_source_aasm_state: Field::String,
    dossier_info_source_details: Field::String.with_options(searchable: false),
    dossier_info_source_flags: Field::Number,
    dossier_info_source_rating: Field::Number,
    is_for_most_comparable_property_to_primary: Field::Boolean,
    is_source_for_primary_property: Field::Boolean,
    llm_interaction: Field::BelongsTo,
    llm_interaction_uuid: Field::String,
    property_is_for_rent: Field::Boolean,
    property_is_for_sale: Field::Boolean,
    realty_asset_photo_uuid: Field::String,
    realty_dossier: Field::BelongsTo,
    realty_dossier_uuid: Field::String,
    realty_search_query: Field::BelongsTo,
    realty_search_query_uuid: Field::String,
    sale_listing: Field::BelongsTo,
    source_is_epc: Field::Boolean,
    source_is_listing: Field::Boolean,
    source_is_photo: Field::Boolean,
    source_is_sold_transaction: Field::Boolean,
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    realty_dossier
    created_at
    updated_at
    is_source_for_primary_property
    associated_listing_uuid
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    realty_dossier
    created_at
    updated_at
    is_source_for_primary_property
    associated_listing_uuid
    agency_tenant
    agency_tenant_uuid
    associated_listing_uuid
    discarded_at
    dossier_asset
    dossier_asset_uuid
    dossier_info_source_aasm_state
    dossier_info_source_details
    dossier_info_source_flags
    dossier_info_source_rating
    is_for_most_comparable_property_to_primary
    is_source_for_primary_property
    llm_interaction
    llm_interaction_uuid
    property_is_for_rent
    property_is_for_sale
    realty_asset_photo_uuid
    realty_dossier
    realty_dossier_uuid
    realty_search_query
    realty_search_query_uuid
    sale_listing
    source_is_epc
    source_is_listing
    source_is_photo
    source_is_sold_transaction
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    associated_listing_uuid
    discarded_at
    dossier_asset
    dossier_asset_uuid
    dossier_info_source_aasm_state
    dossier_info_source_details
    dossier_info_source_flags
    dossier_info_source_rating
    is_for_most_comparable_property_to_primary
    is_source_for_primary_property
    llm_interaction
    llm_interaction_uuid
    property_is_for_rent
    property_is_for_sale
    realty_asset_photo_uuid
    realty_dossier
    realty_dossier_uuid
    realty_search_query
    realty_search_query_uuid
    sale_listing
    source_is_epc
    source_is_listing
    source_is_photo
    source_is_sold_transaction
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how dossier info sources are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(dossier_info_source)
  #   "DossierInfoSource ##{dossier_info_source.id}"
  # end
end
