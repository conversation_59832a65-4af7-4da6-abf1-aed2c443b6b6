require 'administrate/base_dashboard'

class DossierAssetDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    ai_comparability_score: Field::Number,
    asset_condition: Field::String,
    asset_description: Field::String,
    asset_details: Field::String.with_options(searchable: false),
    asset_flags: Field::Number,
    asset_main_color: Field::String,
    asset_secondary_color: Field::String,
    asset_significant_items: Field::Text,
    asset_slug: Field::String,
    asset_style: Field::String,
    asset_title: Field::String,
    asset_unique_features: Field::String.with_options(searchable: false),
    discarded_at: Field::DateTime,
    dossier_asset_parts: Field::HasMany,
    info_sources: Field::HasMany,
    is_good_comparable_to_primary: Field::Boolean,
    is_most_comparable_to_primary: Field::Boolean,
    is_primary_dossier_asset: Field::Boolean,
    property_is_for_rent: Field::Boolean,
    property_is_for_sale: Field::Boolean,
    realty_asset: Field::BelongsTo,
    realty_asset_uuid: Field::String,
    realty_dossier: Field::BelongsTo,
    realty_dossier_uuid: Field::String,
    origin_sale_listing: Field::BelongsTo,
    origin_sale_listing_uuid: Field::String,
    # sale_listings: Field::HasMany,
    translations: Field::String.with_options(searchable: false),
    user_comparability_score: Field::Number,
    user_notes_on_asset: Field::String.with_options(searchable: false),
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    realty_asset
    realty_dossier
    origin_sale_listing
    ai_comparability_score
    is_primary_dossier_asset
    created_at
    updated_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    realty_asset
    realty_asset_uuid
    realty_dossier
    realty_dossier_uuid
    created_at
    updated_at
    is_good_comparable_to_primary
    is_most_comparable_to_primary
    is_primary_dossier_asset
    property_is_for_rent
    property_is_for_sale
    agency_tenant
    agency_tenant_uuid
    ai_comparability_score
    asset_condition
    asset_description
    asset_details
    asset_flags
    asset_main_color
    asset_secondary_color
    asset_significant_items
    asset_slug
    asset_style
    asset_title
    asset_unique_features
    discarded_at
    dossier_asset_parts
    info_sources
    origin_sale_listing
    translations
    user_comparability_score
    user_notes_on_asset
    uuid
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    ai_comparability_score
    asset_condition
    asset_description
    asset_details
    asset_flags
    asset_main_color
    asset_secondary_color
    asset_significant_items
    asset_slug
    asset_style
    asset_title
    asset_unique_features
    discarded_at
    dossier_asset_parts
    info_sources
    is_good_comparable_to_primary
    is_most_comparable_to_primary
    is_primary_dossier_asset
    property_is_for_rent
    property_is_for_sale
    realty_asset
    realty_asset_uuid
    realty_dossier
    realty_dossier_uuid
    origin_sale_listing
    translations
    user_comparability_score
    user_notes_on_asset
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how dossier assets are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(dossier_asset)
  #   "DossierAsset ##{dossier_asset.id}"
  # end
end
