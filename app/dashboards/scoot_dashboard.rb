require 'administrate/base_dashboard'

class ScootDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    realty_game_ids: Field::Text,
    aasm_state: Field::String,
    access_code_details: Field::String.with_options(searchable: false),
    access_token: Field::String,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    bathrooms_max_absolute: Field::Number,
    bathrooms_max_preferred: Field::Number,
    bathrooms_min_absolute: Field::Number,
    bathrooms_min_preferred: Field::Number,
    bedrooms_max_absolute: Field::Number,
    bedrooms_max_preferred: Field::Number,
    bedrooms_min_absolute: Field::Number,
    bedrooms_min_preferred: Field::Number,
    cloned_from_uuid: Field::String,
    country_code: Field::String,
    discarded_at: Field::DateTime,
    dossier_assets_count: Field::Number,
    dossier_ids: Field::String,
    dossiers_count: Field::Number,
    feature_preferences: Field::String.with_options(searchable: false),
    flags: Field::Number,
    guest_uuid: Field::String,
    indoor_area_max_absolute: Field::Number,
    indoor_area_min_absolute: Field::Number,
    integer: Field::Number,
    is_approved_for_public: Field::Boolean,
    is_closed: Field::Boolean,
    is_public_scoot: Field::Boolean,
    is_showcase_scoot: Field::Boolean,
    last_accessed_at: Field::DateTime,
    lat_lng_bounds: Field::String.with_options(searchable: false),
    latitude_center: Field::Number.with_options(decimals: 2),
    longitude_center: Field::Number.with_options(decimals: 2),
    max_distance_from_vicinity_absolute: Field::Number,
    max_distance_from_vicinity_preferred: Field::Number,
    plot_area_max_absolute: Field::Number,
    plot_area_min_absolute: Field::Number,
    preferred_area_unit: Field::Number,
    preferred_currency: Field::String,
    preferred_distance_unit: Field::Number,
    preferred_locale: Field::String,
    preferred_property_type: Field::String,
    preferred_vicinity_name: Field::String,
    price_max_absolute_cents: Field::Number,
    price_max_preferred_cents: Field::Number,
    price_min_absolute_cents: Field::Number,
    price_min_preferred_cents: Field::Number,
    price_plan: Field::Number,
    scoot_access_count: Field::Number,
    scoot_access_flags: Field::Number,
    scoot_checklists: Field::String.with_options(searchable: false),
    scoot_description: Field::String,
    scoot_host_domain: Field::String,
    scoot_image_url: Field::String,
    scoot_notes: Field::String.with_options(searchable: false),
    scoot_places_of_interest: Field::String.with_options(searchable: false),
    scoot_related_urls: Field::String.with_options(searchable: false),
    scoot_share_links: Field::String.with_options(searchable: false),
    scoot_significant_dates: Field::String.with_options(searchable: false),
    scoot_subdomain: Field::String,
    scoot_title: Field::String,
    scoot_type: Field::Number,
    supported_currencies: Field::Text,
    translations: Field::String.with_options(searchable: false),
    user_uuid: Field::String,
    uuid: Field::String,
    viewport: Field::String.with_options(searchable: false),
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    realty_game_ids
    access_code_details
    flags
    access_token
    scoot_subdomain
    dossier_ids
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    scoot_subdomain
    access_token
    dossier_ids
    access_code_details
    aasm_state
    agency_tenant
    agency_tenant_uuid
    bathrooms_max_absolute
    bathrooms_max_preferred
    bathrooms_min_absolute
    bathrooms_min_preferred
    bedrooms_max_absolute
    bedrooms_max_preferred
    bedrooms_min_absolute
    bedrooms_min_preferred
    cloned_from_uuid
    country_code
    discarded_at
    dossier_assets_count
    dossiers_count
    feature_preferences
    flags
    guest_uuid
    indoor_area_max_absolute
    indoor_area_min_absolute
    integer
    is_approved_for_public
    is_closed
    is_public_scoot
    is_showcase_scoot
    last_accessed_at
    lat_lng_bounds
    latitude_center
    longitude_center
    max_distance_from_vicinity_absolute
    max_distance_from_vicinity_preferred
    plot_area_max_absolute
    plot_area_min_absolute
    preferred_area_unit
    preferred_currency
    preferred_distance_unit
    preferred_locale
    preferred_property_type
    preferred_vicinity_name
    price_max_absolute_cents
    price_max_preferred_cents
    price_min_absolute_cents
    price_min_preferred_cents
    price_plan
    scoot_access_count
    scoot_access_flags
    scoot_checklists
    scoot_description
    scoot_host_domain
    scoot_image_url
    scoot_notes
    scoot_places_of_interest
    scoot_related_urls
    scoot_share_links
    scoot_significant_dates
    scoot_title
    scoot_type
    supported_currencies
    translations
    user_uuid
    uuid
    viewport
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    flags
    scoot_subdomain
    realty_game_ids
    access_token
    dossier_ids
    access_code_details
  ].freeze
  # access_code_details
  # access_token
  # agency_tenant
  # agency_tenant_uuid
  # bathrooms_max_absolute
  # bathrooms_max_preferred
  # bathrooms_min_absolute
  # bathrooms_min_preferred
  # bedrooms_max_absolute
  # bedrooms_max_preferred
  # bedrooms_min_absolute
  # bedrooms_min_preferred
  # cloned_from_uuid
  # country_code
  # discarded_at
  # dossier_assets_count
  # dossier_ids
  # dossiers_count
  # feature_preferences
  # flags
  # guest_uuid
  # indoor_area_max_absolute
  # indoor_area_min_absolute
  # integer
  # is_approved_for_public
  # is_closed
  # is_public_scoot
  # is_showcase_scoot
  # last_accessed_at
  # lat_lng_bounds
  # latitude_center
  # longitude_center
  # max_distance_from_vicinity_absolute
  # max_distance_from_vicinity_preferred
  # plot_area_max_absolute
  # plot_area_min_absolute
  # preferred_area_unit
  # preferred_currency
  # preferred_distance_unit
  # preferred_locale
  # preferred_property_type
  # preferred_vicinity_name
  # price_max_absolute_cents
  # price_max_preferred_cents
  # price_min_absolute_cents
  # price_min_preferred_cents
  # price_plan
  # scoot_access_count
  # scoot_access_flags
  # scoot_checklists
  # scoot_description
  # scoot_host_domain
  # scoot_image_url
  # scoot_notes
  # scoot_places_of_interest
  # scoot_related_urls
  # scoot_share_links
  # scoot_significant_dates
  # scoot_subdomain
  # scoot_title
  # scoot_type
  # supported_currencies
  # translations
  # user_uuid
  # uuid
  # viewport
  # ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how scoots are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(scoot)
  #   "Scoot ##{scoot.id}"
  # end
end
