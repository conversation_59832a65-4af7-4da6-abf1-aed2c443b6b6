require 'administrate/base_dashboard'

class PriceEstimateDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    count_sold_transactions_shown: Field::Number,
    discarded_at: Field::DateTime,
    estimate_currency: Field::String,
    estimate_details: Field::String.with_options(searchable: false),
    estimate_flags: Field::Number,
    estimate_latitude_center: Field::Number.with_options(decimals: 2),
    estimate_longitude_center: Field::Number.with_options(decimals: 2),
    estimate_postal_code: Field::String,
    estimate_text: Field::Text,
    estimate_title: Field::String,
    estimate_vicinity: Field::String,
    estimated_price_cents: Field::Number,
    estimator_name: Field::String,
    extra_uuid: Field::String,
    is_ai_estimate: Field::Boolean,
    is_for_rental_listing: Field::Boolean,
    is_for_sale_listing: Field::Boolean,
    is_protected: Field::Boolean,
    listing_uuid: Field::String,
    percentage_above_or_below: Field::Number,
    price_at_time_of_estimate_cents: Field::Number,
    realty_dossier: Field::BelongsTo,
    realty_dossier_uuid: Field::String,
    scoot: Field::BelongsTo,
    scoot_uuid: Field::String,
    user: Field::BelongsTo,
    user_uuid: Field::String,
    uuid: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    estimate_postal_code
    estimate_vicinity
    estimated_price_cents
    price_at_time_of_estimate_cents
    percentage_above_or_below
    estimator_name
    created_at
    is_protected
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    agency_tenant
    agency_tenant_uuid
    count_sold_transactions_shown
    discarded_at
    estimate_currency
    estimate_details
    estimate_flags
    estimate_latitude_center
    estimate_longitude_center
    estimate_postal_code
    estimate_text
    estimate_title
    estimate_vicinity
    estimated_price_cents
    estimator_name
    extra_uuid
    is_ai_estimate
    is_for_rental_listing
    is_for_sale_listing
    is_protected
    listing_uuid
    percentage_above_or_below
    price_at_time_of_estimate_cents
    realty_dossier
    realty_dossier_uuid
    scoot
    scoot_uuid
    user
    user_uuid
    uuid
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    count_sold_transactions_shown
    discarded_at
    estimate_currency
    estimate_details
    estimate_flags
    estimate_latitude_center
    estimate_longitude_center
    estimate_postal_code
    estimate_text
    estimate_title
    estimate_vicinity
    estimated_price_cents
    estimator_name
    extra_uuid
    is_ai_estimate
    is_for_rental_listing
    is_for_sale_listing
    is_protected
    listing_uuid
    percentage_above_or_below
    price_at_time_of_estimate_cents
    realty_dossier
    realty_dossier_uuid
    scoot
    scoot_uuid
    user
    user_uuid
    uuid
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how price estimates are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(price_estimate)
  #   "PriceEstimate ##{price_estimate.id}"
  # end
end
