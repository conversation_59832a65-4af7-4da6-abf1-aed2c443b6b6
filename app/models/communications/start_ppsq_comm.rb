# == Schema Information
#
# Table name: communications
#
#  id                   :bigint           not null, primary key
#  aasm_state           :string
#  agency_tenant_uuid   :uuid
#  comm_flags           :integer          default(0), not null
#  comm_form_name       :string
#  comm_form_params     :jsonb
#  comm_text            :text
#  comm_type            :integer          default(0), not null
#  discarded_at         :datetime
#  extra_comm_details   :jsonb
#  origin_user_uuid     :uuid
#  package_code         :string
#  primary_assoc_type   :string
#  primary_assoc_uuid   :uuid
#  request_referrer     :string
#  secondary_assoc_type :string
#  subdomain_uuid       :uuid
#  target_user_uuid     :uuid
#  translations         :jsonb
#  uuid                 :uuid
#  viewed_at            :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  secondary_assoc_id   :bigint
#
# Indexes
#
#  index_communications_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_communications_on_comm_flags          (comm_flags)
#  index_communications_on_comm_type           (comm_type)
#  index_communications_on_discarded_at        (discarded_at)
#  index_communications_on_uuid                (uuid)
#
module Communications
  class StartPpsqComm < Communication
    default_scope { where(comm_form_name: 'start_ppsq_a') }
    # belongs_to :quest, foreign_key: 'quest_uuid', primary_key: 'uuid'
    store_attribute :comm_form_params, :game_session_id, :string, default: ''

    def self.create_from_form(start_params)
      # comm = super(params)
      comm = Communications::StartPpsqComm.create!
      comm.update!(comm_form_params: start_params)
      comm
    end
  end
end
