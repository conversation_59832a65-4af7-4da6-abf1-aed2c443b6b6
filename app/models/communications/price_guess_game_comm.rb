# == Schema Information
#
# Table name: communications
#
#  id                   :bigint           not null, primary key
#  aasm_state           :string
#  agency_tenant_uuid   :uuid
#  comm_flags           :integer          default(0), not null
#  comm_form_name       :string
#  comm_form_params     :jsonb
#  comm_text            :text
#  comm_type            :integer          default(0), not null
#  discarded_at         :datetime
#  extra_comm_details   :jsonb
#  origin_user_uuid     :uuid
#  package_code         :string
#  primary_assoc_type   :string
#  primary_assoc_uuid   :uuid
#  request_referrer     :string
#  secondary_assoc_type :string
#  subdomain_uuid       :uuid
#  target_user_uuid     :uuid
#  translations         :jsonb
#  uuid                 :uuid
#  viewed_at            :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  secondary_assoc_id   :bigint
#
# Indexes
#
#  index_communications_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_communications_on_comm_flags          (comm_flags)
#  index_communications_on_comm_type           (comm_type)
#  index_communications_on_discarded_at        (discarded_at)
#  index_communications_on_uuid                (uuid)
#
module Communications
  class PriceGuessGameComm < Communication
    default_scope { where(comm_form_name: 'price_guess_game_creation') }

    store_attribute :comm_form_params, :title, :string
    store_attribute :comm_form_params, :description, :string
    store_attribute :comm_form_params, :start_time, :datetime
    store_attribute :comm_form_params, :end_time, :datetime
    store_attribute :comm_form_params, :is_active, :boolean
    store_attribute :comm_form_params, :allow_anonymous, :boolean
    store_attribute :comm_form_params, :max_players, :integer
    store_attribute :comm_form_params, :creator_email, :string
    store_attribute :comm_form_params, :initial_property_url, :string

    def self.create_from_form(game_params)
      price_guess_game_data = game_params[:price_guess_game] || game_params

      comm = Communications::PriceGuessGameComm.create!(
        comm_form_name: 'price_guess_game_creation',
        comm_type: 'game_creation_request',
        comm_form_params: {
          title: price_guess_game_data[:title],
          description: price_guess_game_data[:description],
          start_time: price_guess_game_data[:start_time] ? DateTime.parse(price_guess_game_data[:start_time]) : nil,
          end_time: price_guess_game_data[:end_time] ? DateTime.parse(price_guess_game_data[:end_time]) : nil,
          is_active: price_guess_game_data[:is_active],
          allow_anonymous: price_guess_game_data[:allow_anonymous],
          max_players: price_guess_game_data[:max_players],
          creator_email: price_guess_game_data[:creator_email],
          initial_property_url: price_guess_game_data[:initial_property_url]
        },
        comm_text: price_guess_game_data[:description] || ''
      )
      comm
    end
  end
end
