# == Schema Information
#
# Table name: communications
#
#  id                   :bigint           not null, primary key
#  aasm_state           :string
#  agency_tenant_uuid   :uuid
#  comm_flags           :integer          default(0), not null
#  comm_form_name       :string
#  comm_form_params     :jsonb
#  comm_text            :text
#  comm_type            :integer          default(0), not null
#  discarded_at         :datetime
#  extra_comm_details   :jsonb
#  origin_user_uuid     :uuid
#  package_code         :string
#  primary_assoc_type   :string
#  primary_assoc_uuid   :uuid
#  request_referrer     :string
#  secondary_assoc_type :string
#  subdomain_uuid       :uuid
#  target_user_uuid     :uuid
#  translations         :jsonb
#  uuid                 :uuid
#  viewed_at            :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  secondary_assoc_id   :bigint
#
# Indexes
#
#  index_communications_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_communications_on_comm_flags          (comm_flags)
#  index_communications_on_comm_type           (comm_type)
#  index_communications_on_discarded_at        (discarded_at)
#  index_communications_on_uuid                (uuid)
#
module Communications
  class GamePropertyFeedbackComm < Communication
    default_scope { where(comm_form_name: 'game_property_feedback_comm') }
    # belongs_to :game_session, foreign_key: 'primary_assoc_uuid',
    #                           primary_key: 'uuid', optional: true
    belongs_to :guessed_price, foreign_key: 'primary_assoc_uuid',
                               primary_key: 'uuid', optional: true
    belongs_to :ahoy_visit, primary_key: 'visitor_token', foreign_key: 'secondary_assoc_type',
                            class_name: 'Ahoy::Visit', optional: true
    
    # Convenience method to access the game session through guessed price
    has_one :game_session, through: :guessed_price

    store_attribute :comm_form_params, :game_session_id, :string
    store_attribute :comm_form_params, :general_feedback, :string
    store_attribute :comm_form_params, :wants_to_create_game, :boolean
    store_attribute :comm_form_params, :email, :string
    store_attribute :comm_form_params, :subdomain, :string
    store_attribute :comm_form_params, :game_description, :string
    store_attribute :comm_form_params, :submitted_at, :datetime
    store_attribute :comm_form_params, :user_agent, :string
    store_attribute :comm_form_params, :page_url, :string
    store_attribute :comm_form_params, :session_guest_name, :string
    store_attribute :comm_form_params, :property_uuid, :string
    store_attribute :comm_form_params, :property_title, :string
    store_attribute :comm_form_params, :player_name, :string
    store_attribute :comm_form_params, :feedback_text, :string
    store_attribute :comm_form_params, :user_guess, :integer
    store_attribute :comm_form_params, :actual_price, :integer
    store_attribute :comm_form_params, :score, :integer

    def self.create_from_form(start_params)
      comm = Communications::GamePropertyFeedbackComm.create!(
        comm_form_name: 'game_property_feedback_comm',
        comm_type: 'game_feedback',
        comm_form_params: {
          game_session_id: start_params[:game_session_id],
          general_feedback: start_params[:feedback_text],
          wants_to_create_game: start_params[:wants_to_create_game],
          email: start_params[:email],
          subdomain: start_params[:subdomain],
          game_description: start_params[:game_description],
          submitted_at: start_params[:submitted_at] ? DateTime.parse(start_params[:submitted_at]) : nil,
          user_agent: start_params[:user_agent],
          page_url: start_params[:page_url],
          session_guest_name: start_params[:player_name],
          property_uuid: start_params[:property_uuid],
          property_title: start_params[:property_title],
          player_name: start_params[:player_name],
          feedback_text: start_params[:feedback_text],
          user_guess: start_params[:user_guess],
          actual_price: start_params[:actual_price],
          score: start_params[:score]
        },
        comm_text: start_params[:feedback_text] || ''
      )
      comm
    end
  end
end
