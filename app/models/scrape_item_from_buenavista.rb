# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#

# Assuming ScrapeItem is defined elsewhere and provides full_content_before_js
# class ScrapeItem
#   attr_accessor :full_content_before_js, :scrape_is_buenavista, :content_is_json, :id
#   def self.find_or_create_for_h2c(retrieval_end_point, is_search_url:); end
#   def update!(attrs); end
#   def self.find(id); end
#   def self.scrape_is_buenavista; end # Mocking default_scope behavior
# end

class ScrapeItemFromBuenavista < ScrapeItem
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  default_scope { scrape_is_buenavista } # Assuming this works or is handled by parent

  def self.find_or_create_for_h2c_buenavista(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: false
    )
    # Ensure we are updating the correct object type if ScrapeItem.find_or_create_for_h2c
    # returns a base ScrapeItem instance.
    # If it can return ScrapeItemFromBuenavista directly, this might not be strictly necessary
    # but ensures the type is correct before calling .find
    scrape_item.update!(
      scrape_is_buenavista: true,
      content_is_json: true
    )
    # This ensures we are working with an instance of ScrapeItemFromBuenavista
    # which has the specific mapping methods.
    found_item = find(scrape_item.id) # Use self.find to ensure it's ScrapeItemFromBuenavista
    # If ScrapeItem uses STI, then ScrapeItem.find(id) might already return the correct subclass
    # if scrape_is_buenavista is used as a type column or similar.
    # If not, you might need to cast or ensure `found_item` is of the correct type,
    # though `ScrapeItemFromBuenavista.find(scrape_item.id)` should handle this if STI is set up.
    # For simplicity, I'm assuming `ScrapeItemFromBuenavista.find` works as intended.
    found_item
  end

  def property_hash_from_scrape_item
    return nil unless full_content_before_js && !full_content_before_js.empty?

    begin
      buenavista_json = JSON.parse(full_content_before_js)
    rescue JSON::ParserError => e
      Rails.logger.error "Failed to parse Buenavista JSON: #{e.message}"
      return nil
    end

    # Ensure 'property' key exists
    return nil unless buenavista_json['property']

    property_data_hash = {}

    listing_data = map_property_to_listing_schema(buenavista_json)
    asset_data = map_property_to_asset_schema(buenavista_json)

    property_data_hash[:listing_data] = listing_data
    property_data_hash[:asset_data] = asset_data

    pictures = buenavista_json.dig('property', 'Pictures', 'Picture')
    property_data_hash[:listing_image_urls] = if pictures.is_a?(Array)
                                                pictures.map { |img| img['PictureURL'] }.compact
                                              else
                                                []
                                              end

    property_data_hash.stringify_keys!
    property_data_hash
  end

  private

  def clean_string_to_float(value)
    value.to_s.gsub(/[^0-9.]/, '').to_f
  end

  def clean_string_to_int(value)
    value.to_s.gsub(/[^0-9]/, '').to_i
  end

  def extract_all_features(property_features_array)
    return [] unless property_features_array.is_a?(Array)

    property_features_array.flat_map { |feature_group| feature_group['Value'] }.compact.uniq
  end

  def map_property_to_asset_schema(buenavista_json)
    prop = buenavista_json['property'] # Main property data
    auto_title = buenavista_json['auto_title']
    auto_desc = buenavista_json.dig('auto_desc', 'description')

    # Corrected: Pass the array of feature categories to extract_all_features
    all_property_features = extract_all_features(prop.dig('PropertyFeatures', 'Category'))

    constructed_area_val = prop['Built'].is_a?(Numeric) ? prop['Built'].to_f : clean_string_to_float(prop['Built'])
    plot_area_val = prop['GardenPlot'].is_a?(Numeric) ? prop['GardenPlot'].to_f : clean_string_to_float(prop['GardenPlot'])

    year_built = 0
    if prop['BuiltYear'].to_s.match?(/\A\d{4}\z/)
      year_built = prop['BuiltYear'].to_i
    elsif prop['CompletionDate']
      begin
        # Ensure CompletionDate is not nil or empty before parsing
        year_built = Date.parse(prop['CompletionDate']).year if prop['CompletionDate'].present?
      rescue ArgumentError, TypeError
        # Keep 0 if parsing fails
      end
    end

    garage_count = if prop['Parking'].to_i > 0
                     prop['Parking'].to_i
                   else
                     ((auto_desc || prop['Description']).to_s.downcase.include?('garage') ? 1 : 0)
                   end

    {
      'title' => auto_title || "#{prop.dig('PropertyType', 'NameType')} in #{prop['Location']}",
      'categories' => all_property_features.map { |f| { 'id' => f.to_s.downcase.gsub(/\s+/, '-'), 'name' => f.to_s } },
      'city' => prop['Location'],
      'city_search_key' => prop['Location']&.downcase&.gsub(/\s+/, '-') || '',
      'constructed_area' => constructed_area_val,
      'count_bathrooms' => prop['Bathrooms'].to_f || 0.0,
      'count_bedrooms' => prop['Bedrooms'].to_i || 0,
      'count_garages' => garage_count,
      'count_toilets' => 0, # New schema doesn't specify toilets separately from bathrooms
      'country' => prop['Country'],
      'description' => auto_desc || prop['Description'],
      'details' => {}, # New schema doesn't have structured room data like the old one might have
      'discarded_at' => nil,
      'energy_performance' => nil, # Not directly available, could parse from EnergyRating.Image if needed
      'energy_rating' => prop.dig('EnergyRating', 'EnergyRated').present? && prop.dig('EnergyRating', 'EnergyRated') != '' ? 'Available' : nil,
      'floor' => nil, # Not a structured field in new schema (might be in description)
      'has_rental_listings' => false, # Assuming properties from this source are for sale
      'has_sale_listings' => true,
      'has_sold_transactions' => false,
      'host_on_create' => 'unknown_host', # Default
      'latitude' => nil, # Not available in the new example JSON
      'longitude' => nil, # Not available in the new example JSON
      'neighborhood' => prop['SubLocation'].presence, # SubLocation seems like the best fit
      'neighborhood_search_key' => prop['SubLocation']&.downcase&.gsub(/\s+/, '-') || '',
      'plot_area' => plot_area_val,
      'postal_code' => nil, # Not available in the new example JSON
      'prop_origin_key' => '', # Default
      'prop_state_key' => 'new', # Default or map from prop['Status']['system'] if applicable
      'prop_type_key' => prop.dig('PropertyType', 'NameType')&.downcase&.gsub(/\s+/, '-') || prop.dig('PropertyType', 'Type')&.downcase&.gsub(/\s+/, '-') || '',
      'province' => prop['Province'],
      'ra_photos_count' => prop.dig('Pictures', 'Picture')&.is_a?(Array) ? prop.dig('Pictures', 'Picture').size : 0,
      'realty_asset_flags' => 0, # Default
      'realty_asset_tags' => all_property_features, # Using all features as tags
      'reference' => prop['Reference'],
      'region' => prop['Area'], # e.g., "Costa del Sol"
      'rental_listings_count' => 0,
      'sale_listings_count' => 1, # Assuming one listing per property item
      'site_visitor_token' => nil,
      'sold_transactions_count' => 0,
      'street_address' => prop['Location'], # Best guess, new schema lacks full street address
      'street_number' => nil,
      'year_construction' => year_built
    }.transform_values { |v| v.is_a?(String) ? v.strip : v } # Optional: strip whitespace from string values
  end

  def map_property_to_listing_schema(buenavista_json)
    prop = buenavista_json['property']
    auto_title = buenavista_json['auto_title']
    auto_desc = buenavista_json.dig('auto_desc', 'description')

    # Corrected: Pass the array of feature categories to extract_all_features
    property_feature_categories = prop.dig('PropertyFeatures', 'Category')
    all_property_features = extract_all_features(property_feature_categories)

    price_raw = prop['Price'].is_a?(Numeric) ? prop['Price'].to_f : clean_string_to_float(prop['Price'])
    original_price_raw = prop['OriginalPrice'].is_a?(Numeric) ? prop['OriginalPrice'].to_f : clean_string_to_float(prop['OriginalPrice'])

    service_charge_yearly_val = prop['Community_Fees_Year'] # e.g., "1,560"
    service_charge_cents = service_charge_yearly_val ? (clean_string_to_float(service_charge_yearly_val) * 100).to_i : 0

    currency = prop['Currency'] || 'EUR' # Default to EUR if not specified, though it should be

    is_furnished = false
    # Corrected: Search within the 'Category' array for 'Furniture' type
    if property_feature_categories.is_a?(Array)
      furniture_feature_group = property_feature_categories.find { |f_group| f_group['Type'] == 'Furniture' }
      is_furnished = furniture_feature_group['Value'].any? { |v| v.to_s.downcase.include?('furnished') && !v.to_s.downcase.include?('unfurnished') } if furniture_feature_group && furniture_feature_group['Value'].is_a?(Array)
    end

    {
      'title' => auto_title || "#{prop.dig('PropertyType', 'NameType')} in #{prop['Location']}",
      'description' => auto_desc || prop['Description'],
      'archived' => prop.dig('Status', 'system')&.downcase != 'available', # Assuming 'Available' means not archived
      'commission_cents' => 0, # Default
      'commission_currency' => currency,
      'currency' => currency,
      'design_style' => nil,
      'details_of_rooms' => {}, # New schema doesn't have structured room data
      'discarded_at' => nil,
      'extra_sale_details' => {},
      'furnished' => is_furnished,
      'hide_map' => false, # Default
      'highlighted' => false, # No direct equivalent in new schema
      'host_on_create' => 'unknown_host', # Default
      'is_ai_generated_listing' => false, # Default (auto_desc might be AI, but this flag is for internal marking)
      'listing_pages_count' => 0,
      'listing_slug' => prop['Reference'], # Use the property reference as slug
      'listing_tags' => all_property_features,
      'main_video_url' => nil, # No direct equivalent for video URL in example
      'obscure_map' => false, # Default
      'page_section_listings_count' => 0,
      'position_in_list' => nil,
      'price_sale_current_cents' => (price_raw * 100).to_i,
      'price_sale_current_currency' => currency,
      'price_sale_original_cents' => (original_price_raw * 100).to_i,
      'price_sale_original_currency' => currency,
      'property_board_items_count' => 0,
      'publish_from' => nil,
      'publish_till' => nil,
      'reference' => prop['Reference'],
      'related_urls' => {},
      'reserved' => false, # No direct equivalent, assume false
      'sale_listing_features' => all_property_features.map { |feature| [feature.to_s.downcase.gsub(/\s+/, '-'), feature.to_s] }.to_h,
      'sale_listing_flags' => 0,
      'sale_listing_gen_prompt' => nil,
      'service_charge_yearly_cents' => service_charge_cents,
      'service_charge_yearly_currency' => currency,
      'site_visitor_token' => nil,
      'sl_photos_count' => prop.dig('Pictures', 'Picture')&.is_a?(Array) ? prop.dig('Pictures', 'Picture').size : 0,
      'visible' => prop.dig('Status', 'system')&.downcase == 'available' # Assuming 'Available' means visible
    }.transform_values { |v| v.is_a?(String) ? v.strip : v } # Optional: strip whitespace from string values
  end
end
