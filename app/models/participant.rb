# == Schema Information
#
# Table name: participants
#
#  id                       :bigint           not null, primary key
#  average_session_duration :decimal(10, 2)
#  behavior_patterns        :jsonb
#  engagement_metrics       :jsonb
#  first_browser            :string
#  first_city               :string
#  first_country            :string
#  first_device_type        :string
#  first_landing_page       :text
#  first_os                 :string
#  first_referrer           :text
#  first_utm_campaign       :string
#  first_utm_medium         :string
#  first_utm_source         :string
#  first_visit_at           :datetime
#  last_visit_at            :datetime
#  returning_visitor        :boolean          default(FALSE)
#  total_events             :integer          default(0)
#  total_page_views         :integer          default(0)
#  total_visits             :integer          default(0)
#  unique_pages_visited     :integer          default(0)
#  visitor_token            :string           not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
# Indexes
#
#  index_participants_on_behavior_patterns   (behavior_patterns) USING gin
#  index_participants_on_engagement_metrics  (engagement_metrics) USING gin
#  index_participants_on_first_visit_at      (first_visit_at)
#  index_participants_on_last_visit_at       (last_visit_at)
#  index_participants_on_returning_visitor   (returning_visitor)
#  index_participants_on_total_visits        (total_visits)
#  index_participants_on_visitor_token       (visitor_token) UNIQUE
#
class Participant < ApplicationRecord
  validates :visitor_token, presence: true, uniqueness: true

  # Associations
  has_many :ahoy_visits, primary_key: :visitor_token, foreign_key: :visitor_token, 
           class_name: 'Ahoy::Visit', dependent: :nullify
  has_many :ahoy_events, through: :ahoy_visits, source: :events

  # Scopes
  scope :returning, -> { where(returning_visitor: true) }
  scope :new_visitors, -> { where(returning_visitor: false) }
  scope :active_in_period, ->(start_date, end_date) { 
    where(last_visit_at: start_date..end_date) 
  }
  scope :first_visit_in_period, ->(start_date, end_date) { 
    where(first_visit_at: start_date..end_date) 
  }
  scope :high_engagement, -> { where('total_visits > ?', 5) }
  scope :recent, -> { where('last_visit_at > ?', 30.days.ago) }

  # Callbacks
  before_save :calculate_returning_visitor_status
  before_save :update_engagement_metrics

  # Class methods
  def self.sync_from_ahoy_visits
    Ahoy::Visit.includes(:events).find_each do |visit|
      next unless visit.visitor_token.present?
      
      participant = find_or_initialize_by(visitor_token: visit.visitor_token)
      participant.sync_with_visit(visit)
      participant.save!
    end
  end

  def self.analytics_summary(period = 30.days)
    start_date = period.ago
    {
      total_participants: count,
      new_participants: first_visit_in_period(start_date, Time.current).count,
      returning_participants: returning.active_in_period(start_date, Time.current).count,
      average_visits_per_participant: average(:total_visits).to_f.round(2),
      average_events_per_participant: average(:total_events).to_f.round(2),
      high_engagement_participants: high_engagement.count
    }
  end

  # Instance methods
  def sync_with_visit(visit)
    update_first_visit_data(visit) if first_visit_at.nil? || visit.started_at < first_visit_at
    update_last_visit_data(visit) if last_visit_at.nil? || visit.started_at > last_visit_at
    recalculate_totals
  end

  def engagement_score
    return 0 if total_visits.zero?
    
    base_score = [total_visits * 10, 100].min
    event_bonus = [total_events * 2, 50].min
    page_bonus = [unique_pages_visited * 5, 30].min
    returning_bonus = returning_visitor? ? 20 : 0
    
    base_score + event_bonus + page_bonus + returning_bonus
  end

  def behavior_category
    return 'inactive' if total_visits.zero?
    return 'explorer' if unique_pages_visited > 10
    return 'engaged' if total_events > 20
    return 'regular' if returning_visitor? && total_visits > 3
    return 'casual' if total_visits <= 3
    'visitor'
  end

  def days_since_first_visit
    return 0 unless first_visit_at
    (Time.current - first_visit_at) / 1.day
  end

  def days_since_last_visit
    return 0 unless last_visit_at
    (Time.current - last_visit_at) / 1.day
  end

  private

  def calculate_returning_visitor_status
    self.returning_visitor = total_visits > 1
  end

  def update_engagement_metrics
    self.engagement_metrics = {
      engagement_score: engagement_score,
      behavior_category: behavior_category,
      days_since_first_visit: days_since_first_visit,
      days_since_last_visit: days_since_last_visit,
      visit_frequency: calculate_visit_frequency
    }
  end

  def calculate_visit_frequency
    return 0 if total_visits <= 1 || first_visit_at.nil? || last_visit_at.nil?
    
    days_span = (last_visit_at - first_visit_at) / 1.day
    return 0 if days_span.zero?
    
    (total_visits.to_f / days_span).round(3)
  end

  def update_first_visit_data(visit)
    self.first_visit_at = visit.started_at
    self.first_referrer = visit.referrer
    self.first_landing_page = visit.landing_page
    self.first_utm_source = visit.utm_source
    self.first_utm_medium = visit.utm_medium
    self.first_utm_campaign = visit.utm_campaign
    self.first_country = visit.country
    self.first_city = visit.city
    self.first_device_type = visit.device_type
    self.first_browser = visit.browser
    self.first_os = visit.os
  end

  def update_last_visit_data(visit)
    self.last_visit_at = visit.started_at
  end

  def recalculate_totals
    visits = ahoy_visits.reload
    self.total_visits = visits.count
    self.total_events = ahoy_events.count
    self.total_page_views = visits.sum { |v| v.events.where(name: '$view').count }
    self.unique_pages_visited = visits.joins(:events)
                                     .where(ahoy_events: { name: '$view' })
                                     .distinct
                                     .count('ahoy_events.properties->>\'url\'')
    
    # Calculate average session duration (simplified)
    if visits.any?
      durations = visits.map { |v| calculate_session_duration(v) }.compact
      self.average_session_duration = durations.any? ? durations.sum / durations.size : 0
    end
  end

  def calculate_session_duration(visit)
    events = visit.events.order(:time)
    return nil if events.count < 2
    
    (events.last.time - events.first.time) / 60.0 # in minutes
  end
end
