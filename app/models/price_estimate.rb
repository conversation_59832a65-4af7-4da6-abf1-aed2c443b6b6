# frozen_string_literal: true

# == Schema Information
#
# Table name: price_estimates
#
#  id                              :bigint           not null, primary key
#  agency_tenant_uuid              :uuid
#  count_sold_transactions_shown   :integer          default(0), not null
#  discarded_at                    :datetime
#  estimate_currency               :string           default("GBP"), not null
#  estimate_details                :jsonb
#  estimate_flags                  :integer          default(0), not null
#  estimate_latitude_center        :float
#  estimate_longitude_center       :float
#  estimate_postal_code            :string
#  estimate_text                   :text
#  estimate_title                  :string
#  estimate_vicinity               :string
#  estimated_price_cents           :bigint           default(0), not null
#  estimator_name                  :string
#  extra_uuid                      :uuid
#  is_ai_estimate                  :boolean          default(FALSE), not null
#  is_for_rental_listing           :boolean          default(FALSE), not null
#  is_for_sale_listing             :boolean          default(TRUE), not null
#  is_protected                    :boolean          default(FALSE), not null
#  listing_uuid                    :uuid
#  percentage_above_or_below       :integer          default(0), not null
#  price_at_time_of_estimate_cents :bigint           default(0), not null
#  realty_dossier_uuid             :uuid
#  scoot_uuid                      :uuid
#  user_uuid                       :uuid
#  uuid                            :uuid             not null
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  game_session_id                 :string
#
# Indexes
#
#  index_price_estimates_on_agency_tenant_uuid   (agency_tenant_uuid)
#  index_price_estimates_on_discarded_at         (discarded_at)
#  index_price_estimates_on_estimate_flags       (estimate_flags)
#  index_price_estimates_on_listing_uuid         (listing_uuid)
#  index_price_estimates_on_realty_dossier_uuid  (realty_dossier_uuid)
#  index_price_estimates_on_scoot_uuid           (scoot_uuid)
#  index_price_estimates_on_user_uuid            (user_uuid)
#  index_price_estimates_on_uuid                 (uuid) UNIQUE
#
class PriceEstimate < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # Concerns
  include Discard::Model
  # has_paper_trail

  # Relationships
  belongs_to :agency_tenant, primary_key: 'uuid', foreign_key: 'agency_tenant_uuid'
  belongs_to :realty_dossier, primary_key: 'uuid', foreign_key: 'realty_dossier_uuid', optional: true
  belongs_to :user, primary_key: 'uuid', foreign_key: 'user_uuid', optional: true, class_name: 'Pwb::User'
  belongs_to :scoot, primary_key: 'uuid', foreign_key: 'scoot_uuid', optional: true

  # Game session relationship - can be linked by either game_session_id (string) or proper GameSession
  def game_session
    return nil if game_session_id.blank?

    # First try to find by UUID (proper GameSession)
    session = GameSession.find_by(uuid: game_session_id)
    return session if session

    # Fallback: try to find by game_session_id stored in estimate_details
    session_id_from_details = estimate_details&.dig('game_session_id')
    return nil if session_id_from_details.blank?

    GameSession.find_by(uuid: session_id_from_details)
  end

  # Polymorphic association for listings - can be either SaleListing or RentalListing
  def listing
    return nil if listing_uuid.blank?

    if is_for_sale_listing?
      SaleListing.find_by(uuid: listing_uuid)
    elsif is_for_rental_listing?
      RentalListing.find_by(uuid: listing_uuid)
    end
  end

  # Monetize price fields
  monetize :estimated_price_cents, with_model_currency: :estimate_currency
  monetize :price_at_time_of_estimate_cents, with_model_currency: :estimate_currency

  # Validations
  validates :uuid, presence: true, uniqueness: true
  validates :agency_tenant_uuid, presence: true
  validates :estimated_price_cents, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :estimate_currency, presence: true

  validates :listing_uuid, uniqueness: { scope: :game_session_id, message: 'can only have one price estimate per game session' }, unless: -> { listing_uuid.blank? || game_session_id.blank? }
  # Scopes
  scope :for_sale, -> { where(is_for_sale_listing: true) }
  scope :for_rental, -> { where(is_for_rental_listing: true) }
  scope :ai_estimates, -> { where(is_ai_estimate: true) }
  # will use is_protected below to signify estimates
  # autogen by rake task
  scope :gen_by_rake_task, -> { where(is_protected: true) }
  scope :by_estimator, ->(name) { where(estimator_name: name) }

  # Flags using FlagShihTzu
  include FlagShihTzu
  has_flags 1 => :is_featured,
            2 => :is_verified,
            3 => :is_archived,
            4 => :needs_review,
            column: 'estimate_flags'

  # Callbacks
  before_validation :ensure_uuid
  before_validation :set_default_currency

  # Instance methods
  def formatted_estimated_price
    return 'N/A' if estimated_price_cents.zero?

    estimated_price.format
  end

  def formatted_price_at_time_of_estimate
    return 'N/A' if price_at_time_of_estimate_cents.zero?

    price_at_time_of_estimate.format
  end

  def estimate_type
    return 'AI Estimate' if is_ai_estimate?
    return 'Professional Estimate' if estimator_name.present?

    'User Estimate'
  end

  def listing_type
    return 'Sale' if is_for_sale_listing?
    return 'Rental' if is_for_rental_listing?

    'Unknown'
  end

  def accuracy_indicator
    return 'N/A' if percentage_above_or_below.zero?

    if percentage_above_or_below.positive?
      "+#{percentage_above_or_below}%"
    else
      "#{percentage_above_or_below}%"
    end
  end

  private

  def ensure_uuid
    self.uuid ||= SecureRandom.uuid
  end

  def set_default_currency
    self.estimate_currency ||= 'GBP'
  end
end
