# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromCapeverdepropertySearch < ScrapeItem
  default_scope { scrape_is_capeverdeproperty.where(is_realty_search_scrape: true) }

  def self.find_or_create_for_capeverdeproperty_search(retrieval_end_point)
    # I might soon need to figure out some logic for deciding
    # when and if to create a new scrape item
    # Perhaps if the existing one is more than a few days old...
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: true
    )
    scrape_item.update!(scrape_is_capeverdeproperty: true, is_realty_search_scrape: true)
    ScrapeItemFromCapeverdepropertySearch.find(scrape_item.id)
  end

  def raw_contents_as_html_doc
    return nil unless full_content_before_js&.length&.> 1000

    Nokogiri::HTML(full_content_before_js)
  end

  def summary_sale_listings_from_scrape_item
    doc = raw_contents_as_html_doc
    return [] unless doc

    # Extract property listings from search results page
    doc.css('.property-item, .property-listing, .listing-item').each do |property_element|
      capeverdeproperty_hash = extract_property_from_search_element(property_element)
      next unless capeverdeproperty_hash

      standardised_hash = get_property_hash_from_summary_html(capeverdeproperty_hash)
      standardised_hash[:summary_listing_import_url] = scrapable_url
      
      # Find or create the SummaryListing based on full_listing_url
      summary_sale_listing = SummaryListing.find_or_initialize_by(
        full_listing_url: standardised_hash[:full_listing_url],
        scrape_item_uuid: uuid
      )

      # Update the attributes of the found or newly created SummaryListing
      summary_sale_listing.assign_attributes(standardised_hash)
      summary_sale_listing.realty_search_query_uuid = realty_search_query_uuid

      if summary_sale_listing.save!
        puts "summary_sale_listing: #{summary_sale_listing.inspect}"
      else
        puts "Failed to save/update SummaryListing: #{summary_sale_listing.errors.full_messages}"
      end
    end
  end

  private

  def extract_property_from_search_element(property_element)
    property = {}
    
    # Extract title
    title_element = property_element.at('h3, h2, .property-title, .listing-title')
    property['title'] = title_element&.text&.strip if title_element
    
    # Extract price
    price_element = property_element.at('.price, .property-price')
    if price_element
      price_text = price_element.text.strip
      property['price'] = price_text.gsub(/[^\d]/, '').to_i
      property['price_display'] = price_text
    end
    
    # Extract URL
    link_element = property_element.at('a[href*="/property/"]')
    if link_element
      href = link_element['href']
      property['url'] = href.start_with?('http') ? href : "https://www.capeverdeproperty.co.uk#{href}"
    end
    
    # Extract image
    img_element = property_element.at('img')
    if img_element
      src = img_element['src'] || img_element['data-src']
      property['image'] = src if src&.include?('wdcdn.co')
    end
    
    # Extract bedrooms/bathrooms if available
    beds_element = property_element.css('.beds, .bedrooms').first
    property['bedrooms'] = beds_element&.text&.match(/\d+/)&.[](0)&.to_i || 0
    
    baths_element = property_element.css('.baths, .bathrooms').first  
    property['bathrooms'] = baths_element&.text&.match(/\d+/)&.[](0)&.to_i || 0
    
    # Extract location
    location_element = property_element.at('.location, .address')
    property['location'] = location_element&.text&.strip if location_element
    
    property.present? ? property : nil
  end

  # Convert Cape Verde Property search result to standardized format
  def get_property_hash_from_summary_html(capeverdeproperty_hash)
    return {} unless capeverdeproperty_hash

    # Extract reference from URL
    reference = if capeverdeproperty_hash['url']
                  url_parts = capeverdeproperty_hash['url'].split('/')
                  url_parts.find { |part| part.match?(/^[a-z0-9]+$/) && part.length > 5 }
                else
                  "cvp_#{SecureRandom.hex(4)}"
                end

    property_hash = {
      agent_details: {
        company_name: 'Cape Verde Property',
        email: '<EMAIL>',
        phone: '00238 242 2041'
      },
      realty_agent_details: {
        company_name: 'Cape Verde Property',
        email: '<EMAIL>',
        phone: '00238 242 2041'
      },
      summary_listing_reference: reference,
      summary_listing_postcode: nil, # Not available for Cape Verde
      summary_listing_long_address: capeverdeproperty_hash['location'] || capeverdeproperty_hash['title'],
      summary_listing_price: capeverdeproperty_hash['price'] || 0,
      summary_listing_bedrooms_count: capeverdeproperty_hash['bedrooms'] || 0,
      summary_listing_bathrooms_count: capeverdeproperty_hash['bathrooms'] || 0,
      summary_listing_latitude: nil, # Would need to extract from individual pages
      summary_listing_longitude: nil, # Would need to extract from individual pages
      full_listing_url: capeverdeproperty_hash['url'],
      summary_listing_details: {
        title: capeverdeproperty_hash['title'],
        reference: reference,
        images: capeverdeproperty_hash['image'] ? [capeverdeproperty_hash['image']] : []
      },
      summary_listing_source_site: 14 # Assuming Cape Verde Property is source site 14
    }
    
    property_hash
  end
end
