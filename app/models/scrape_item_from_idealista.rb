# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromIdealista < ScrapeItem
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  default_scope { scrape_is_idealista }

  def self.find_or_create_for_h2c_idealista(retrieval_end_point)
    # I might soon need to figure out some logic for deciding
    # when and if to create a new scrape item
    # Perhaps if the existing one is more than a few days old...
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: false
    )
    scrape_item.update!(scrape_is_idealista: true, is_realty_search_scrape: false)
    ScrapeItemFromIdealista.find(scrape_item.id)
  end

  def raw_contents_as_json
    JSON.parse(full_content_before_js)
  rescue JSON::ParserError => e
    # the full error message is too long to be useful
    puts 'Failed to parse JSON..' # #{e.message}"
    nil
  end

  # 1 mar 2025: below def is to do with scraping a single item
  # from a single listing page!
  def property_hash_from_scrape_item
    unless full_content_before_js && full_content_before_js.length > 500
      puts "full_content_before_js: #{full_content_before_js&.length || 0} characters"
      puts "Content preview: #{full_content_before_js&.first(200)}"
      return nil # Return nil instead of raising error to allow graceful handling
    end
    idealista_html = full_content_before_js

    puts top_level_url
    doc = Nokogiri::HTML(idealista_html)
    property_data = {}
    
    # Try to find JSON data in script tags (similar to other portals)
    script_tags = doc.css('script[type="application/json"]')
    json_content = nil
    
    script_tags.each do |script|
      begin
        json_content = JSON.parse(script.text)
        break if json_content && json_content.is_a?(Hash)
      rescue JSON::ParserError
        next
      end
    end
    
    # If no JSON found in script tags, try to extract from window object or other sources
    if json_content.nil?
      script_tags = doc.css('script')
      script_tags.each do |script|
        script_text = script.text
        # Look for common patterns in Idealista pages
        if script_text.include?('window.__INITIAL_STATE__') || 
           script_text.include?('window.dataLayer') ||
           script_text.include?('window.utag_data')
          # Extract JSON from these patterns
          # This would need to be customized based on actual Idealista page structure
          begin
            # Simple regex to extract JSON-like content
            json_match = script_text.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/)
            if json_match
              json_content = JSON.parse(json_match[1])
              break
            end
          rescue JSON::ParserError
            next
          end
        end
      end
    end

    # Transform the extracted data to our property schema
    if json_content
      property_data = transform_idealista_data(json_content, doc)
    else
      # Fallback to HTML parsing if no JSON found
      property_data = extract_from_html(doc)
    end

    property_data.stringify_keys!
  end

  private

  def transform_idealista_data(json_data, doc)
    # This method would transform Idealista's JSON structure to our schema
    # The exact implementation would depend on Idealista's actual data structure
    {
      'title' => extract_title(json_data, doc),
      'price' => extract_price(json_data, doc),
      'description' => extract_description(json_data, doc),
      'bedrooms' => extract_bedrooms(json_data, doc),
      'bathrooms' => extract_bathrooms(json_data, doc),
      'area' => extract_area(json_data, doc),
      'location' => extract_location(json_data, doc),
      'images' => extract_images(json_data, doc),
      'features' => extract_features(json_data, doc)
    }
  end

  def extract_from_html(doc)
    # Fallback HTML extraction method
    {
      'title' => doc.css('h1').first&.text&.strip,
      'price' => extract_price_from_html(doc),
      'description' => extract_description_from_html(doc),
      'bedrooms' => extract_bedrooms_from_html(doc),
      'bathrooms' => extract_bathrooms_from_html(doc),
      'area' => extract_area_from_html(doc),
      'location' => extract_location_from_html(doc),
      'images' => extract_images_from_html(doc),
      'features' => extract_features_from_html(doc)
    }
  end

  def extract_title(json_data, doc)
    # Extract title from JSON or fallback to HTML
    json_data.dig('property', 'title') || 
    json_data.dig('listing', 'title') ||
    doc.css('h1').first&.text&.strip
  end

  def extract_price(json_data, doc)
    # Extract price from JSON or fallback to HTML
    json_data.dig('property', 'price') || 
    json_data.dig('listing', 'price') ||
    extract_price_from_html(doc)
  end

  def extract_description(json_data, doc)
    # Extract description from JSON or fallback to HTML
    json_data.dig('property', 'description') || 
    json_data.dig('listing', 'description') ||
    extract_description_from_html(doc)
  end

  def extract_bedrooms(json_data, doc)
    # Extract bedroom count from JSON or fallback to HTML
    json_data.dig('property', 'bedrooms') || 
    json_data.dig('listing', 'bedrooms') ||
    extract_bedrooms_from_html(doc)
  end

  def extract_bathrooms(json_data, doc)
    # Extract bathroom count from JSON or fallback to HTML
    json_data.dig('property', 'bathrooms') || 
    json_data.dig('listing', 'bathrooms') ||
    extract_bathrooms_from_html(doc)
  end

  def extract_area(json_data, doc)
    # Extract area from JSON or fallback to HTML
    json_data.dig('property', 'area') || 
    json_data.dig('listing', 'area') ||
    extract_area_from_html(doc)
  end

  def extract_location(json_data, doc)
    # Extract location from JSON or fallback to HTML
    json_data.dig('property', 'location') || 
    json_data.dig('listing', 'location') ||
    extract_location_from_html(doc)
  end

  def extract_images(json_data, doc)
    # Extract images from JSON or fallback to HTML
    json_data.dig('property', 'images') || 
    json_data.dig('listing', 'images') ||
    extract_images_from_html(doc)
  end

  def extract_features(json_data, doc)
    # Extract features from JSON or fallback to HTML
    json_data.dig('property', 'features') || 
    json_data.dig('listing', 'features') ||
    extract_features_from_html(doc)
  end

  # HTML extraction fallback methods
  def extract_price_from_html(doc)
    price_element = doc.css('.price, .precio, [class*="price"], [class*="precio"]').first
    price_element&.text&.strip
  end

  def extract_description_from_html(doc)
    desc_element = doc.css('.description, .descripcion, [class*="description"], [class*="descripcion"]').first
    desc_element&.text&.strip
  end

  def extract_bedrooms_from_html(doc)
    # Look for bedroom indicators in various formats
    bedroom_text = doc.text
    bedroom_match = bedroom_text.match(/(\d+)\s*(habitacion|dormitor|bedroom)/i)
    bedroom_match ? bedroom_match[1].to_i : 0
  end

  def extract_bathrooms_from_html(doc)
    # Look for bathroom indicators in various formats
    bathroom_text = doc.text
    bathroom_match = bathroom_text.match(/(\d+)\s*(baño|bathroom)/i)
    bathroom_match ? bathroom_match[1].to_i : 0
  end

  def extract_area_from_html(doc)
    # Look for area indicators in various formats
    area_text = doc.text
    area_match = area_text.match(/(\d+)\s*m[²2]/i)
    area_match ? area_match[1].to_i : 0
  end

  def extract_location_from_html(doc)
    location_element = doc.css('.location, .ubicacion, [class*="location"], [class*="ubicacion"]').first
    location_element&.text&.strip
  end

  def extract_images_from_html(doc)
    images = []
    doc.css('img').each do |img|
      src = img['src'] || img['data-src']
      images << src if src && src.include?('idealista')
    end
    images
  end

  def extract_features_from_html(doc)
    features = []
    # Look for common feature indicators
    feature_elements = doc.css('.features, .caracteristicas, [class*="feature"], [class*="caracteristica"]')
    feature_elements.each do |element|
      features << element.text.strip if element.text.present?
    end
    features
  end
end
