# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromZillow < ScrapeItem
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  default_scope { scrape_is_zillow }

  def self.find_or_create_for_h2c_zillow(retrieval_end_point)
    # I might soon need to figure out some logic for deciding
    # when and if to create a new scrape item
    # Perhaps if the existing one is more than a few days old...
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point, is_search_url: false
    )
    scrape_item.update!(scrape_is_zillow: true, is_realty_search_scrape: false)
    ScrapeItemFromZillow.find(scrape_item.id)
  end

  def raw_contents_as_json
    JSON.parse(full_content_before_js)
  rescue JSON::ParserError => e
    # the full error message is too long to be useful
    puts 'Failed to parse JSON..' # #{e.message}"
    nil
  end

  # 1 mar 2025: below def is to do with scraping a single item
  # from a single listing page!
  def property_hash_from_scrape_item
    unless full_content_before_js && full_content_before_js.length > 500
      puts "full_content_before_js: #{full_content_before_js&.length || 0} characters"
      puts "Content preview: #{full_content_before_js&.first(200)}"
      return nil # Return nil instead of raising error to allow graceful handling
    end

    puts "Processing Zillow content with #{full_content_before_js.length} characters"
    
    zillow_html = full_content_before_js

    puts top_level_url
    doc = Nokogiri::HTML(zillow_html)
    property_data = {}
    
    # Try to find JSON data in script tags (Zillow often uses __NEXT_DATA__ like other React apps)
    script_tags = doc.css('script[type="application/json"]')
    json_content = nil
    
    # Look for __NEXT_DATA__ script first
    next_data_script = doc.at('script#__NEXT_DATA__')
    if next_data_script
      begin
        json_content = JSON.parse(next_data_script.text)
      rescue JSON::ParserError
        json_content = nil
      end
    end
    
    # If no __NEXT_DATA__, try other script tags
    if json_content.nil?
      script_tags.each do |script|
        begin
          json_content = JSON.parse(script.text)
          break if json_content && json_content.is_a?(Hash)
        rescue JSON::ParserError
          next
        end
      end
    end
    
    # If still no JSON, look for common patterns in regular script tags
    if json_content.nil?
      script_tags = doc.css('script')
      script_tags.each do |script|
        script_text = script.text
        # Look for window.__INITIAL_STATE__ or similar patterns
        if script_text.include?('window.__INITIAL_STATE__') || 
           script_text.include?('window.dataLayer') ||
           script_text.include?('window.utag_data')
          # Extract JSON from these patterns
          begin
            # Simple regex to extract JSON-like content
            json_match = script_text.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/)
            if json_match
              json_content = JSON.parse(json_match[1])
              break
            end
          rescue JSON::ParserError
            next
          end
        end
      end
    end

    # Transform the extracted data to our property schema
    if json_content
      puts "Found JSON content, extracting data..."
      property_data = transform_zillow_data(json_content, doc)
    else
      puts "No JSON content found, falling back to HTML extraction..."
      # Fallback to HTML parsing if no JSON found
      property_data = extract_from_html(doc)
    end

    puts "Property data extracted: #{property_data.keys.join(', ')}"

    # Ensure we always return a valid hash
    if property_data.nil? || property_data.empty?
      puts "Property data is empty, returning minimal fallback data"
      property_data = {
        'title' => doc.css('h1').first&.text&.strip || 'Unknown Property',
        'price' => 0,
        'bedrooms' => 0,
        'bathrooms' => 0,
        'area' => 0,
        'location' => 'Unknown Location'
      }
    end

    property_data.stringify_keys!
  end

  private

  def transform_zillow_data(json_data, doc)
    # Extract the property data from Zillow's JSON structure
    zillow_property = json_data.dig('props', 'pageProps', 'property') || json_data['property'] || {}

    # Create the basic property data structure
    property_data = {
      'title' => extract_title(json_data, doc),
      'price' => extract_price(json_data, doc),
      'description' => extract_description(json_data, doc),
      'bedrooms' => extract_bedrooms(json_data, doc),
      'bathrooms' => extract_bathrooms(json_data, doc),
      'area' => extract_area(json_data, doc),
      'location' => extract_location(json_data, doc),
      'images' => extract_images(json_data, doc),
      'features' => extract_features(json_data, doc),
      'property_type' => extract_property_type(json_data, doc),
      'year_built' => extract_year_built(json_data, doc),
      'lot_size' => extract_lot_size(json_data, doc)
    }

    # Map to the expected schema structure
    listing_data = map_zillow_to_listing_schema(zillow_property)
    asset_data = map_zillow_to_asset_schema(zillow_property)

    # Return the structure expected by sale_listing_from_scrape_item
    {
      listing_data: listing_data,
      asset_data: asset_data,
      listing_image_urls: property_data['images'] || [],
      raw_zillow_data: zillow_property
    }
  end

  def extract_from_html(doc)
    # Fallback HTML extraction method
    property_data = {
      'title' => doc.css('h1').first&.text&.strip,
      'price' => extract_price_from_html(doc),
      'description' => extract_description_from_html(doc),
      'bedrooms' => extract_bedrooms_from_html(doc),
      'bathrooms' => extract_bathrooms_from_html(doc),
      'area' => extract_area_from_html(doc),
      'location' => extract_location_from_html(doc),
      'images' => extract_images_from_html(doc),
      'features' => extract_features_from_html(doc),
      'property_type' => extract_property_type_from_html(doc),
      'year_built' => extract_year_built_from_html(doc),
      'lot_size' => extract_lot_size_from_html(doc)
    }

    # Map to the expected schema structure for HTML fallback
    listing_data = map_html_to_listing_schema(property_data)
    asset_data = map_html_to_asset_schema(property_data)

    # Return the structure expected by sale_listing_from_scrape_item
    {
      listing_data: listing_data,
      asset_data: asset_data,
      listing_image_urls: property_data['images'] || [],
      raw_zillow_data: property_data
    }
  end

  # JSON extraction methods
  def extract_title(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'streetAddress') || 
    json_data.dig('property', 'address') ||
    doc.css('h1').first&.text&.strip
  end

  def extract_price(json_data, doc)
    price = json_data.dig('props', 'pageProps', 'property', 'price') || 
            json_data.dig('property', 'price')
    
    if price.is_a?(Hash)
      price['value'] || price['amount']
    else
      price || extract_price_from_html(doc)
    end
  end

  def extract_description(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'description') || 
    json_data.dig('property', 'description') ||
    extract_description_from_html(doc)
  end

  def extract_bedrooms(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'bedrooms') || 
    json_data.dig('property', 'bedrooms') ||
    extract_bedrooms_from_html(doc)
  end

  def extract_bathrooms(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'bathrooms') || 
    json_data.dig('property', 'bathrooms') ||
    extract_bathrooms_from_html(doc)
  end

  def extract_area(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'livingArea') || 
    json_data.dig('property', 'livingArea') ||
    extract_area_from_html(doc)
  end

  def extract_location(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'address') || 
    json_data.dig('property', 'address') ||
    extract_location_from_html(doc)
  end

  def extract_images(json_data, doc)
    images = json_data.dig('props', 'pageProps', 'property', 'photos') || 
             json_data.dig('property', 'photos') ||
             []
    
    if images.empty?
      extract_images_from_html(doc)
    else
      images.map { |img| img.is_a?(Hash) ? img['url'] || img['src'] : img }.compact
    end
  end

  def extract_features(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'features') || 
    json_data.dig('property', 'features') ||
    extract_features_from_html(doc)
  end

  def extract_property_type(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'homeType') || 
    json_data.dig('property', 'homeType') ||
    extract_property_type_from_html(doc)
  end

  def extract_year_built(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'yearBuilt') || 
    json_data.dig('property', 'yearBuilt') ||
    extract_year_built_from_html(doc)
  end

  def extract_lot_size(json_data, doc)
    json_data.dig('props', 'pageProps', 'property', 'lotSize') || 
    json_data.dig('property', 'lotSize') ||
    extract_lot_size_from_html(doc)
  end

  # HTML extraction fallback methods
  def extract_price_from_html(doc)
    price_element = doc.css('[data-testid="price"], .price, [class*="price"]').first
    price_text = price_element&.text&.strip
    return nil unless price_text
    
    # Extract numeric value from price text
    price_match = price_text.match(/[\d,]+/)
    price_match ? price_match[0].gsub(',', '').to_i : 0
  end

  def extract_description_from_html(doc)
    desc_element = doc.css('[data-testid="description"], .description, [class*="description"]').first
    desc_element&.text&.strip
  end

  def extract_bedrooms_from_html(doc)
    bedroom_text = doc.text
    bedroom_match = bedroom_text.match(/(\d+)\s*(bed|bedroom)/i)
    bedroom_match ? bedroom_match[1].to_i : 0
  end

  def extract_bathrooms_from_html(doc)
    bathroom_text = doc.text
    bathroom_match = bathroom_text.match(/(\d+)\s*(bath|bathroom)/i)
    bathroom_match ? bathroom_match[1].to_i : 0
  end

  def extract_area_from_html(doc)
    area_text = doc.text
    area_match = area_text.match(/(\d+,?\d*)\s*sqft/i)
    area_match ? area_match[1].gsub(',', '').to_i : 0
  end

  def extract_location_from_html(doc)
    location_element = doc.css('h1, [data-testid="address"], .address').first
    location_element&.text&.strip
  end

  def extract_images_from_html(doc)
    images = []
    doc.css('img').each do |img|
      src = img['src'] || img['data-src']
      images << src if src && (src.include?('zillow') || src.start_with?('http'))
    end
    images
  end

  def extract_features_from_html(doc)
    features = []
    # Look for common feature indicators
    feature_elements = doc.css('.features, [class*="feature"], [data-testid*="feature"]')
    feature_elements.each do |element|
      features << element.text.strip if element.text.present?
    end
    features
  end

  def extract_property_type_from_html(doc)
    # Look for property type indicators
    type_text = doc.text
    if type_text.match(/single.family/i)
      'SingleFamily'
    elsif type_text.match(/condo|condominium/i)
      'Condo'
    elsif type_text.match(/townhouse|townhome/i)
      'Townhouse'
    elsif type_text.match(/apartment/i)
      'Apartment'
    else
      'Unknown'
    end
  end

  def extract_year_built_from_html(doc)
    year_text = doc.text
    year_match = year_text.match(/built.{0,10}(\d{4})/i)
    year_match ? year_match[1].to_i : nil
  end

  def extract_lot_size_from_html(doc)
    lot_text = doc.text
    lot_match = lot_text.match(/([\d.]+)\s*acre/i)
    lot_match ? lot_match[1].to_f : 0.0
  end

  # Schema mapping methods for JSON data
  def map_zillow_to_listing_schema(zillow_property)
    {
      'title' => zillow_property['streetAddress'] || zillow_property['address'] || 'Unknown Property',
      'description_long' => zillow_property['description'] || '',
      'price_sale_current_cents' => ((zillow_property.dig('price', 'value') || zillow_property['price'] || 0) * 100).to_i,
      'price_sale_current_currency' => zillow_property.dig('price', 'currency') || 'USD',
      'price_sale_original_cents' => ((zillow_property.dig('price', 'value') || zillow_property['price'] || 0) * 100).to_i,
      'price_sale_original_currency' => zillow_property.dig('price', 'currency') || 'USD',
      'reference' => zillow_property['zpid'] || zillow_property['id'],
      'visible' => true,
      'sale_listing_flags' => 0,
      'currency' => zillow_property.dig('price', 'currency') || 'USD'
    }
  end

  def map_zillow_to_asset_schema(zillow_property)
    address = zillow_property['address'] || {}

    {
      'title' => zillow_property['streetAddress'] || zillow_property['address'] || 'Unknown Property',
      'description' => zillow_property['description'] || '',
      'count_bedrooms' => zillow_property['bedrooms'] || 0,
      'count_bathrooms' => zillow_property['bathrooms'] || 0,
      'constructed_area' => zillow_property['livingArea'] || 0,
      'plot_area' => zillow_property['lotSize'] || 0,
      'year_construction' => zillow_property['yearBuilt'],
      'prop_type_key' => zillow_property['homeType']&.downcase || 'unknown',
      'city' => address['city'] || '',
      'province' => address['state'] || '',
      'postal_code' => address['zipcode'] || address['zip_code'] || '',
      'street_address' => address['streetAddress'] || zillow_property['streetAddress'] || '',
      'country' => 'US',
      'latitude' => address['latitude'],
      'longitude' => address['longitude'],
      'realty_asset_flags' => 0,
      'has_sale_listings' => true,
      'sale_listings_count' => 1
    }
  end

  # Schema mapping methods for HTML fallback data
  def map_html_to_listing_schema(property_data)
    {
      'title' => property_data['title'] || 'Unknown Property',
      'description_long' => property_data['description'] || '',
      'price_sale_current_cents' => ((property_data['price'] || 0) * 100).to_i,
      'price_sale_current_currency' => 'USD',
      'price_sale_original_cents' => ((property_data['price'] || 0) * 100).to_i,
      'price_sale_original_currency' => 'USD',
      'reference' => 'unknown',
      'visible' => true,
      'sale_listing_flags' => 0,
      'currency' => 'USD'
    }
  end

  def map_html_to_asset_schema(property_data)
    {
      'title' => property_data['title'] || 'Unknown Property',
      'description' => property_data['description'] || '',
      'count_bedrooms' => property_data['bedrooms'] || 0,
      'count_bathrooms' => property_data['bathrooms'] || 0,
      'constructed_area' => property_data['area'] || 0,
      'plot_area' => property_data['lot_size'] || 0,
      'year_construction' => property_data['year_built'],
      'prop_type_key' => property_data['property_type']&.downcase || 'unknown',
      'city' => extract_city_from_location(property_data['location']),
      'province' => extract_state_from_location(property_data['location']),
      'postal_code' => extract_zip_from_location(property_data['location']),
      'street_address' => property_data['location'] || '',
      'country' => 'US',
      'realty_asset_flags' => 0,
      'has_sale_listings' => true,
      'sale_listings_count' => 1
    }
  end

  def extract_city_from_location(location_text)
    return nil unless location_text
    parts = location_text.split(',').map(&:strip)
    parts.first
  end

  def extract_state_from_location(location_text)
    return nil unless location_text
    state_match = location_text.match(/,\s*([A-Z]{2})\s*\d{5}/)
    state_match ? state_match[1] : nil
  end

  def extract_zip_from_location(location_text)
    return nil unless location_text
    zip_match = location_text.match(/(\d{5})/)
    zip_match ? zip_match[1] : nil
  end
end
