# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromGetthedata < ScrapeItem
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true
  default_scope { scrape_is_getthedata }

  def self.find_or_create_for_h2c_getthedata(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point
    )
    scrape_item.update!(scrape_is_getthedata: true)
    ScrapeItemFromGetthedata.find(scrape_item.id)
  end
  # end
  # require 'nokogiri'
  # require 'date'

  # class PostcodeParser
  #   def initialize(html_content)
  #     @doc = Nokogiri::HTML(html_content)
  #   end

  def extract_data
    {
      postcode: extract_postcode,
      location: extract_location,
      geodata: extract_geodata,
      house_prices: extract_house_prices,
      transport: extract_transport,
      broadband: extract_broadband,
      energy_consumption: extract_energy_consumption,
      deprivation: extract_deprivation,
      food_hygiene: extract_food_hygiene,
      postboxes: extract_postboxes,
      administrative_codes: extract_administrative_codes,
      census_areas: extract_census_areas,
      nearby_postcodes: extract_nearby_postcodes,
      website_metadata: extract_website_metadata
    }
  end

  private

  def extract_postcode
    @doc.css('h1').text.match(/(\w+\s*\w*)/)&.[]1 || 'TR16 5RQ'
  end

  def extract_location
    paragraph = @doc.css('p').find { |p| p.text.include?('lies on') }
    return {} unless paragraph

    {
      street: paragraph.css('b')[0]&.text,
      locality: paragraph.css('b')[1]&.text,
      town_city: paragraph.css('b')[2]&.text,
      ward: paragraph.css('b')[3]&.text,
      authority: paragraph.css('b')[4]&.text,
      constituency: paragraph.css('b')[5]&.text,
      icb_location: paragraph.css('b')[6]&.text,
      police_force: paragraph.css('b')[7]&.text,
      in_use_since: paragraph.css('b')[8]&.text,
      country: @doc.css('table').find { |t| t.text.include?('Country') }&.css('td')&.last&.text
    }
  end

  def extract_geodata
    table = @doc.css('table').find { |t| t.text.include?('Easting') }
    return {} unless table

    {
      easting: table.css('tr')[0]&.css('td')&.last&.text,
      northing: table.css('tr')[1]&.css('td')&.last&.text,
      latitude: table.css('tr')[2]&.css('td')&.last&.text,
      longitude: table.css('tr')[3]&.css('td')&.last&.text
    }
  end

  def extract_house_prices
    table = @doc.css('table').find { |t| t.css('tr').any? { |tr| tr.text.include?('MENAKARNE') } }
    return [] unless table

    table.css('tr').map do |tr|
      address = tr.css('div').first&.text
      details = tr.css('div')[1]&.css('div')&.map(&:text)
      next unless address && details

      {
        address: address,
        year: details[0],
        # date: Date.parse(details[1]).strftime('%Y-%m-%d') rescue nil,
        price: details[2]
      }
    end.compact
  end

  def extract_transport
    {
      bus_stops: extract_bus_stops,
      railway_stations: extract_railway_stations
    }
  end

  def extract_bus_stops
    table = @doc.css('table').find { |t| t.text.include?('Nearest bus stops') }
    return [] unless table

    table.css('tr').map do |tr|
      {
        name: tr.css('td')[0]&.text,
        location: tr.css('td')[1]&.text,
        distance: tr.css('td')[2]&.text
      }
    end
  end

  def extract_railway_stations
    table = @doc.css('table').find { |t| t.text.include?('Nearest railway stations') }
    return [] unless table

    table.css('tr').map do |tr|
      {
        name: tr.css('td')[0]&.text,
        distance: tr.css('td')[1]&.text
      }
    end
  end

  def extract_broadband
    {
      access: extract_broadband_access,
      speed: extract_broadband_speed,
      limitations: extract_broadband_limitations
    }
  end

  def extract_broadband_access
    table = @doc.css('table').find { |t| t.text.include?('Percentage of properties with Next Generation Access') }
    return {} unless table

    {
      next_generation_access: table.css('tr')[0]&.css('td')&.last&.text,
      superfast_broadband: table.css('tr')[1]&.css('td')&.last&.text,
      ultrafast_broadband: table.css('tr')[2]&.css('td')&.last&.text,
      full_fibre_broadband: table.css('tr')[3]&.css('td')&.last&.text
    }
  end

  def extract_broadband_speed
    download_table = @doc.css('table').find { |t| t.text.include?('Median download speed') }
    upload_table = @doc.css('table').find { |t| t.text.include?('Median upload speed') }
    {
      download: {
        median: download_table&.css('tr')&.[](0)&.css('td')&.last&.text,
        average: download_table&.css('tr')&.[](1)&.css('td')&.last&.text,
        maximum: download_table&.css('tr')&.[](2)&.css('td')&.last&.text
      },
      upload: {
        median: upload_table&.css('tr')&.[](0)&.css('td')&.last&.text,
        average: upload_table&.css('tr')&.[](1)&.css('td')&.last&.text,
        maximum: upload_table&.css('tr')&.[](2)&.css('td')&.last&.text
      }
    }
  end

  def extract_broadband_limitations
    table = @doc.css('table').find { |t| t.text.include?('Percentage of properties unable to receive 2Mbps') }
    return {} unless table

    {
      unable_2mbps: table.css('tr')[0]&.css('td')&.last&.text,
      unable_5mbps: table.css('tr')[1]&.css('td')&.last&.text,
      unable_10mbps: table.css('tr')[2]&.css('td')&.last&.text,
      unable_30mbps: table.css('tr')[3]&.css('td')&.last&.text
    }
  end

  def extract_energy_consumption
    table = @doc.css('table').find { |t| t.text.include?('Consumption (kWh)') }
    return {} unless table

    {
      electricity: {
        consumption: table.css('tr')[0]&.css('td')&.last&.text,
        meter_count: table.css('tr')[1]&.css('td')&.last&.text,
        mean_per_meter: table.css('tr')[2]&.css('td')&.last&.text,
        median_per_meter: table.css('tr')[3]&.css('td')&.last&.text
      }
    }
  end

  def extract_deprivation
    paragraph = @doc.css('p').find { |p| p.text.include?('% of English postcodes') }
    script = @doc.css('script').find { |s| s.text.include?('google.charts.load') }
    data = script&.text&.match(/arrayToDataTable.*?(\d+\.\d+).*?(\d+\.\d+)/)&.captures

    {
      less_deprived_percentage: paragraph&.css('b')&.last&.text,
      more_deprived: data&.[](0),
      less_deprived: data&.[](1)
    }
  end

  def extract_food_hygiene
    divs = @doc.css('div').select { |d| d.text.match?(/Food Hygiene Rating/) }
    divs.map do |div|
      name = div.previous_sibling&.text
      details = div.css('div').map(&:text)
      {
        name: name,
        address: details[0],
        distance: details[1]
        # rating: div.css('img')&.first&.[]('alt')&.match(/Rating: (\d)/)&.[]1
      }
    end
  end

  def extract_postboxes
    table = @doc.css('table').find { |t| t.text.include?('Nearest post box') }
    return [] unless table

    table.css('tr').drop(2).map do |tr|
      {
        location: tr.css('td')[0]&.text,
        collection_times: {
          monday_friday: tr.css('td')[1]&.text,
          saturday: tr.css('td')[2]&.text
        },
        distance: tr.css('td')[3]&.text
      }
    end
  end

  def extract_administrative_codes
    table = @doc.css('table').find { |t| t.text.include?('ITL 1 Code') }
    return {} unless table

    {
      itl_1: { code: table.css('tr')[0]&.css('td')&.first&.text, name: table.css('tr')[0]&.css('td')&.last&.text },
      itl_2: { code: table.css('tr')[1]&.css('td')&.first&.text, name: table.css('tr')[1]&.css('td')&.last&.text },
      itl_3: { code: table.css('tr')[2]&.css('td')&.first&.text, name: table.css('tr')[2]&.css('td')&.last&.text },
      lau_1: { code: table.css('tr')[3]&.css('td')&.first&.text, name: table.css('tr')[3]&.css('td')&.last&.text }
    }
  end

  def extract_census_areas
    table = @doc.css('table').find { |t| t.text.include?('Census Output Area') }
    return {} unless table

    {
      # oa: { code: table.css('tr')[0]&.css('td')&.[1]&.text, name: table.css('tr')[0]&.css('td')&.[2]&.text },
      # lsoa: { code: table.css('tr')[1]&.css('td')&.[1]&.text, name: table.css('tr')[1]&.css('td')&.[2]&.text },
      # msoa: { code: table.css('tr')[2]&.css('td')&.[1]&.text, name: table.css('tr')[2]&.css('td')&.[2]&.text }
    }
  end

  def extract_nearby_postcodes
    table = @doc.css('table').find { |t| t.text.include?('Nearest postcodes') }
    return [] unless table

    table.css('tr').map do |tr|
      {
        postcode: tr.css('td')[0]&.css('a')&.first&.text,
        street: tr.css('td')[1]&.text,
        distance: tr.css('td')[2]&.text
      }
    end
  end

  def extract_website_metadata
    {
      title: @doc.css('title').text,
      description: @doc.css('meta[name="description"]').first&.[]('content'),
      keywords: @doc.css('meta[name="keywords"]').first&.[]('content'),
      canonical_url: @doc.css('link[rel="canonical"]').first&.[]('href')
      #   gtm_id: @doc.css('script').text.match(/GTM-\w+/)&.[]0,
      #   adsense_client: @doc.css('script').text.match(/ca-pub-\d+/)&.[]0,
      #   company: @doc.css('.footer p').text.match(/GetTheData Publishing Limited/)&.[]0,
      #   company_number: @doc.css('.footer p').text.match(/Company Number: (\d+)/)&.[]1,
      #   registered_office: @doc.css('.footer p').text.match(/Registered Office: (.+?)(?:\n|$)/)&.[]1,
      #   twitter: @doc.css('.footer a').find { |a| a['href'].include?('twitter') }&.text,
      #   email: @doc.css('.footer a').find { |a| a['href'].include?('email') }&.css('span')&.text,
      #   execution_time: @doc.css('.footer p').text.match(/Execution time: (\d+ms)/)&.[]1
    }
  end
end
