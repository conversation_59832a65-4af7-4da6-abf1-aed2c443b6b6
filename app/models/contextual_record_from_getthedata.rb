# == Schema Information
#
# Table name: contextual_records
#
#  id                      :bigint           not null, primary key
#  aasm_state              :string
#  agency_tenant_uuid      :uuid
#  contxt_flags            :integer          default(0), not null
#  contxt_outcode          :string
#  contxt_postcode         :string
#  contxt_type             :integer          default(0), not null
#  discarded_at            :datetime
#  extra_contxt_details    :jsonb
#  latest_scrape_item_uuid :uuid
#  raw_contxt              :text
#  record_source_url       :string
#  translations            :jsonb
#  uuid                    :uuid
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#
# Indexes
#
#  index_contextual_records_on_agency_tenant_uuid  (agency_tenant_uuid)
#  index_contextual_records_on_contxt_flags        (contxt_flags)
#  index_contextual_records_on_contxt_type         (contxt_type)
#  index_contextual_records_on_discarded_at        (discarded_at)
#  index_contextual_records_on_uuid                (uuid)
#
class ContextualRecordFromGetthedata < ContextualRecord
  # acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  # has_many :dossier_asset_contextual_records, primary_key: 'uuid', foreign_key: 'dossier_asset_uuid'
  # has_many :dossier_assets, through: :dossier_asset_contextual_records
  # has_one :scrape_item, primary_key: 'latest_scrape_item_uuid', foreign_key: 'uuid'

  store_attribute :extra_contxt_details, :maps, :json, default: []
  store_attribute :extra_contxt_details, :geodata, :json, default: {}
  store_attribute :extra_contxt_details, :politics, :json, default: {}
  store_attribute :extra_contxt_details, :broadband, :json, default: {}
  store_attribute :extra_contxt_details, :elevation, :json, default: {}
  store_attribute :extra_contxt_details, :transport, :json, default: {}
  store_attribute :extra_contxt_details, :deprivation, :json, default: {}
  store_attribute :extra_contxt_details, :census_areas, :json, default: {}
  store_attribute :extra_contxt_details, :house_prices, :json, default: []
  store_attribute :extra_contxt_details, :page_metadata, :json, default: {}
  store_attribute :extra_contxt_details, :postcode_header, :string
  store_attribute :extra_contxt_details, :location_details, :json, default: {}
  store_attribute :extra_contxt_details, :location_summary, :json, default: {}
  store_attribute :extra_contxt_details, :nearest_postcodes, :json, default: []
  store_attribute :extra_contxt_details, :energy_consumption, :json, default: {}
  store_attribute :extra_contxt_details, :nearest_post_boxes, :json, default: []
  store_attribute :extra_contxt_details, :food_standards_ratings, :json, default: []

  default_scope { record_is_getthedata }

  def self.scrape_and_update_from_postcode(
    dossier_asset, playwright_connector, force_retrieval: false
  )
    raise ArgumentError, 'dossier_asset must be an instance of DossierAsset' unless dossier_asset.is_a?(DossierAsset)
    raise ArgumentError, 'playwright_connector must be an instance of ScraperConnectors::LocalPlaywright' unless playwright_connector.is_a?(ScraperConnectors::LocalPlaywright)
    raise ArgumentError, 'dossier_asset.postal_code is blank' unless dossier_asset.postal_code.present?

    Rails.logger.info "[SCRAPE] Starting scrape_and_update_from_postcode for postcode: #{dossier_asset.postal_code}, DossierAsset: #{dossier_asset.uuid}"

    contextual_record = nil
    ActsAsTenant.with_tenant(dossier_asset.agency_tenant) do
      # 1. Check for existing valid ContextualRecordFromGetthedata
      contextual_record = ContextualRecordFromGetthedata
                          .joins(:dossier_asset_contextual_records)
                          .find_by(
                            contxt_postcode: dossier_asset.postal_code,
                            agency_tenant_uuid: ActsAsTenant.current_tenant&.uuid,
                            dossier_asset_contextual_records: { dossier_asset_uuid: dossier_asset.uuid },
                            aasm_state: 'active'
                          )

      if contextual_record
        Rails.logger.info "[FOUND] Existing valid ContextualRecord found for postcode: #{dossier_asset.postal_code}, UUID: #{contextual_record.uuid}"
        return contextual_record unless force_retrieval
      end

      # 2. Find or initialize ContextualRecordFromGetthedata
      contextual_record ||= ContextualRecordFromGetthedata.find_or_initialize_by(
        contxt_postcode: dossier_asset.postal_code,
        agency_tenant_uuid: ActsAsTenant.current_tenant&.uuid
      ) do |record|
        record.record_is_getthedata = true
      end

      # 3. Build the URL and find/create ScrapeItem
      postcode_slug = dossier_asset.postal_code.gsub(/\s+/, '-').upcase
      url = "https://www.getthedata.com/postcode/#{postcode_slug}"
      scrape_item = ScrapeItem.find_or_create_for_h2c(url, is_search_url: false)

      unless scrape_item.scraper_connector_name.present? && scrape_item.scrape_is_getthedata?
        scrape_item.update!(
          scraper_connector_name: 'ScraperConnectors::LocalPlaywright',
          scrape_is_getthedata: true
        )
      end

      # 4. Check if ScrapeItem has valid content
      if !force_retrieval && scrape_item.is_valid_scrape? && (scrape_item.full_content_before_js.present? || scrape_item.full_content_after_js.present?)
        Rails.logger.info "[SKIP] Valid ScrapeItem content exists for UUID: #{scrape_item.uuid}, URL: #{url}"
      else
        # 5. Retrieve content using ScrapeItem's playwright method
        Rails.logger.info "[SCRAPE_ITEM] Retrieving content for ScrapeItem #{scrape_item.uuid}, URL: #{url}"
        scrape_item.retrieve_and_set_content_with_playwright(
          playwright_connector,
          force_retrieval: force_retrieval
        )
      end

      # 6. Parse the scraped content
      scrape_item.reload
      html_content = scrape_item.full_content_before_js.presence || scrape_item.full_content_after_js.presence
      if html_content.blank?
        error_message = "No HTML content found for ScrapeItem #{scrape_item.uuid}, URL: #{url}"
        Rails.logger.warn "[WARN] #{error_message}"
        scrape_item.update!(scrape_failure_message: error_message, is_valid_scrape: false)
        raise error_message
      end

      Rails.logger.info "[PARSE] Parsing HTML for postcode: #{dossier_asset.postal_code}"
      doc = Nokogiri::HTML(html_content)
      content_text = doc.css('body').text.strip

      if content_text.blank?
        error_message = "Parsed content is blank for ScrapeItem #{scrape_item.uuid}, URL: #{url}"
        Rails.logger.warn "[WARN] #{error_message}"
        scrape_item.update!(scrape_failure_message: error_message, is_valid_scrape: false)
        raise error_message
      end

      # 7. Update ContextualRecordFromGetthedata
      Rails.logger.info "[UPDATE] Updating ContextualRecord for postcode: #{dossier_asset.postal_code}"
      contextual_record.assign_attributes(
        latest_scrape_item_uuid: scrape_item.uuid,
        record_source_url: url,
        raw_contxt: content_text,
        contxt_type: 0, # Adjust based on your contxt_type enum
        aasm_state: 'active' # Adjust based on your AASM states
      )
      contextual_record.set_content_from_scrape # Calls existing method to parse and update extra_contxt_details
      contextual_record.save!

      # 8. Link to DossierAsset via DossierAssetContextualRecord
      Rails.logger.info "[LINK] Linking ContextualRecord to DossierAsset #{dossier_asset.uuid}"
      DossierAssetContextualRecord.find_or_create_by!(
        dossier_asset: dossier_asset,
        contextual_record: contextual_record
      )

      Rails.logger.info "[SUCCESS] Successfully processed ContextualRecord for postcode: #{dossier_asset.postal_code}"
    end

    contextual_record
  rescue StandardError => e
    error_message = "Failed for postcode #{dossier_asset.postal_code}, DossierAsset #{dossier_asset.uuid}: #{e.class} - #{e.message}"
    Rails.logger.error "[ERROR] #{error_message}"
    Rails.logger.error e.backtrace.join("\n")
    if defined?(scrape_item) && scrape_item
      scrape_item.update(
        scrape_failure_message: "#{e.class}: #{e.message}".truncate(250),
        is_valid_scrape: false
      )
    end
    raise
  end

  def set_content_from_scrape
    html_content = scrape_item.full_content_before_js
    parser = RealtyParsers::ParseGetthedata.new(html_content)
    json_output = parser.parse
    update_from_json(JSON.parse(json_output))
  end

  def update_from_json(json_data)
    return unless json_data.is_a?(Hash)

    self.maps = json_data['maps'] if json_data.key?('maps')
    self.geodata = json_data['geodata'] if json_data.key?('geodata')
    self.politics = json_data['politics'] if json_data.key?('politics')
    self.broadband = json_data['broadband'] if json_data.key?('broadband')
    self.elevation = json_data['elevation'] if json_data.key?('elevation')
    self.transport = json_data['transport'] if json_data.key?('transport')
    self.deprivation = json_data['deprivation'] if json_data.key?('deprivation')
    self.census_areas = json_data['census_areas'] if json_data.key?('census_areas')
    self.house_prices = json_data['house_prices'] if json_data.key?('house_prices')
    self.page_metadata = json_data['page_metadata'] if json_data.key?('page_metadata')
    self.postcode_header = json_data['postcode_header'] if json_data.key?('postcode_header')
    self.location_details = json_data['location_details'] if json_data.key?('location_details')
    self.location_summary = json_data['location_summary'] if json_data.key?('location_summary')
    self.nearest_postcodes = json_data['nearest_postcodes'] if json_data.key?('nearest_postcodes')
    self.energy_consumption = json_data['energy_consumption'] if json_data.key?('energy_consumption')
    self.nearest_post_boxes = json_data['nearest_post_boxes'] if json_data.key?('nearest_post_boxes')
    self.food_standards_ratings = json_data['food_standards_ratings'] if json_data.key?('food_standards_ratings')

    save
  end

  validates :contxt_postcode, uniqueness: { scope: :contxt_flags, message: 'and contxt_flags combination must be unique' }
end
