# == Schema Information
#
# Table name: pwb_users
#
#  id                     :integer          not null, primary key
#  admin                  :boolean          default(FALSE)
#  agency_tenant_uuid     :uuid
#  authentication_token   :string
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  default_admin_locale   :string
#  default_client_locale  :string
#  default_currency       :string
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  failed_attempts        :integer          default(0), not null
#  first_names            :string
#  last_names             :string
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  locked_at              :datetime
#  phone_number_primary   :string
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  sign_in_count          :integer          default(0), not null
#  skype                  :string
#  unconfirmed_email      :string
#  unlock_token           :string
#  uuid                   :uuid
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_pwb_users_on_confirmation_token    (confirmation_token) UNIQUE
#  index_pwb_users_on_email                 (email) UNIQUE
#  index_pwb_users_on_reset_password_token  (reset_password_token) UNIQUE
#  index_pwb_users_on_uuid                  (uuid)
#
module Pwb
  class User < ActiveRecord::Base
    # self.table_name_prefix = 'pwb_'
    self.table_name = 'pwb_users'

    # has_one :ad_hoc_data_scrape, class_name: 'AdHocData::Scrape', foreign_key: 'primary_pwb_user_uuid'

    has_one :primary_agency_tenant, foreign_key: 'primary_pwb_user_uuid',
                                    primary_key: 'uuid',
                                    class_name: '::AgencyTenant'

    # Include default devise modules. Others available are:
    # :confirmable, :lockable, :timeoutable and :omniauthable
    devise :database_authenticatable, :registerable,
           :recoverable, :rememberable, :trackable,
           :validatable
    #  , :omniauthable, omniauth_providers: [:facebook]

    has_many :authorizations

    def pat_name
      primary_agency_tenant&.subdomain
    end

    def is_admin_admin?
      # admin
      email == '<EMAIL>'
    end

    def self.find_or_create_from(email:)
      user = Pwb::User.where(email:).first
      user ||= Pwb::User.create!(
        admin: false,
        email:,
        password: Devise.friendly_token[0, 20]
      )
      user
    end

    # # TODO: - use db col for below
    # def default_client_locale
    #   :en
    # end

    # def self.find_for_oauth(auth)
    #   authorization = Authorization.where(provider: auth.provider, uid: auth.uid.to_s).first
    #   return authorization.user if authorization

    #   email = auth.info[:email]
    #   unless email.present?
    #     # below is a workaround for when email is not available from auth provider
    #     email = "#{SecureRandom.urlsafe_base64}@example.com"
    #     # in future might redirect to a page where email can be requested
    #   end
    #   user = User.where(email:).first
    #   if user
    #     user.create_authorization(auth)
    #   else
    #     # need to prefix Devise with :: to avoid confusion with Pwb::Devise
    #     password = ::Devise.friendly_token[0, 20]
    #     user = User.create!(email:, password:, password_confirmation: password)
    #     user.create_authorization(auth)
    #   end

    #   user
    # end

    # def create_authorization(auth)
    #   authorizations.create(provider: auth.provider, uid: auth.uid)
    # end
  end
end
