# == Schema Information
#
# Table name: scrape_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_scrape_item_details                     :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  guest_uuid                                    :uuid
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_realty_search_scrape                       :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_search_query_uuid                      :uuid
#  related_scrape_items                          :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_item_flags                             :integer          default(0), not null
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  summary_listings_count                        :integer
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  user_uuid                                     :uuid
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_scrape_items_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_scrape_items_on_discarded_at              (discarded_at)
#  index_scrape_items_on_realty_search_query_uuid  (realty_search_query_uuid)
#  index_scrape_items_on_scrape_item_flags         (scrape_item_flags)
#  index_scrape_items_on_uuid                      (uuid)
#
class ScrapeItemFromCapeverdeproperty < ScrapeItem
  default_scope { scrape_is_capeverdeproperty }

  def self.find_or_create_for_h2c_capeverdeproperty(retrieval_end_point)
    scrape_item = ScrapeItem.find_or_create_for_h2c(
      retrieval_end_point
    )
    scrape_item.update!(scrape_is_capeverdeproperty: true)
    ScrapeItemFromCapeverdeproperty.find(scrape_item.id)
  end

  # Main method to extract property data from scraped HTML content
  def property_hash_from_scrape_item
    unless full_content_before_js && full_content_before_js.length > 1000
      puts "full_content_before_js: #{full_content_before_js}"
      raise 'full_content_before_js unavailable or too short'
    end
    
    capeverdeproperty_html = full_content_before_js
    puts top_level_url
    doc = Nokogiri::HTML(capeverdeproperty_html)
    property_data = {}

    # Extract property data from HTML structure
    cvp_property = extract_property_from_html(doc)
    
    listing_data = map_property_to_listing_schema(cvp_property)
    asset_data = map_property_to_asset_schema(cvp_property)

    property_data[:listing_data] = listing_data
    property_data[:asset_data] = asset_data

    # Extract image URLs
    image_urls = extract_image_urls(doc)
    property_data[:listing_image_urls] = image_urls

    # Store raw data for debugging/future use
    property_data['raw_cvp_data'] = cvp_property

    # Extract canonical URL
    property_data['import_url'] = doc.at('link[rel="canonical"]')['href'] if doc.at('link[rel="canonical"]')

    property_data.stringify_keys!
  end

  private

  def extract_property_from_html(doc)
    # Extract property details from Cape Verde Property HTML structure
    property = {}

    # Title and basic info
    property['title'] = doc.at('h1')&.text&.strip
    
    # Price extraction
    price_element = doc.css('h2').find { |h2| h2.text.include?('€') }
    if price_element
      price_text = price_element.text.strip
      property['price_raw'] = price_text.gsub(/[^\d]/, '').to_i
      property['price_display'] = price_text
    end

    # Property type and bedrooms from title or content
    property['property_type'] = extract_property_type(doc)
    property['bedrooms'] = extract_bedrooms(doc)
    property['bathrooms'] = extract_bathrooms(doc)

    # Location extraction
    property['location'] = extract_location(doc)
    
    # Description
    property['description'] = extract_description(doc)
    
    # Features
    property['features'] = extract_features(doc)
    
    # Contact details
    property['agent'] = extract_agent_details(doc)
    
    # Extract property reference from URL or page
    property['reference'] = extract_reference

    property
  end

  def extract_property_type(doc)
    # Look for property type in title or specific elements
    title = doc.at('h1')&.text&.strip || ''
    if title.downcase.include?('apartment')
      'apartment'
    elsif title.downcase.include?('villa')
      'villa'  
    elsif title.downcase.include?('plot') || title.downcase.include?('land')
      'plot'
    else
      'property'
    end
  end

  def extract_bedrooms(doc)
    # Extract bedroom count from title or main features
    title = doc.at('h1')&.text&.strip || ''
    bedroom_match = title.match(/(\d+)\s*bedroom/i)
    return bedroom_match[1].to_i if bedroom_match

    # Look in main features section
    features_text = doc.css('.main-features, .property-features').text
    bedroom_match = features_text.match(/(\d+)\s*bedroom/i)
    bedroom_match ? bedroom_match[1].to_i : 0
  end

  def extract_bathrooms(doc)
    # Extract bathroom count from features
    features_text = doc.css('.main-features, .property-features').text
    bathroom_match = features_text.match(/(\d+)\s*bathroom/i)
    bathroom_match ? bathroom_match[1].to_i : 0
  end

  def extract_location(doc)
    # Extract location from title - format is typically "PROPERTY NAME CITY, REGION"
    title = doc.at('h1')&.text&.strip || ''
    
    location = {}
    
    # Split by comma to separate city and region
    if title.include?(',')
      parts = title.split(',')
      region_part = parts.last.strip # SAL
      city_part = parts.first.strip # CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA
      
      # Extract city from the end of the first part
      city_words = city_part.split(' ')
      if city_words.length >= 2
        # Take the last 2 words as potential city name
        potential_city = city_words.last(2).join(' ')
        # Check if it looks like a city name (contains MARIA, etc.)
        if potential_city.upcase.include?('MARIA') || potential_city.upcase.include?('PRAIA')
          location['city'] = potential_city.upcase
        else
          location['city'] = city_words.last.upcase
        end
      end
      
      location['region'] = region_part.upcase
    else
      # Fallback: try to extract from URL or other sources
      canonical_url = doc.at('link[rel="canonical"]')&.[]('href')
      if canonical_url
        url_parts = canonical_url.split('/')
        if url_parts.include?('santa-maria')
          location['city'] = 'SANTA MARIA'
        end
        if url_parts.include?('sal')
          location['region'] = 'SAL'
        end
      end
    end
    
    # Try to extract coordinates from map if available
    map_element = doc.at('img[src*="maps.googleapis.com"]')
    if map_element && map_element['src']
      coord_match = map_element['src'].match(/center=([-\d.]+),([-\d.]+)/)
      if coord_match
        location['lat'] = coord_match[1].to_f
        location['lon'] = coord_match[2].to_f
      end
    end
    
    location
  end

  def extract_description(doc)
    # Look for description in various possible locations
    description_selectors = [
      'p:contains("Sea Views")',
      '.property-description',
      '.description',
      'p'
    ]
    
    description_selectors.each do |selector|
      element = doc.at(selector)
      if element && element.text.length > 20
        return element.text.strip
      end
    end
    
    # Fallback: extract text that looks like a description
    doc.css('p').each do |p|
      text = p.text.strip
      if text.length > 20 && !text.match?(/^\d+$/) && !text.include?('©')
        return text
      end
    end
    
    ''
  end

  def extract_features(doc)
    features = []
    
    # Extract from main features section specifically
    doc.css('.main-features li, .property-features li, .features li').each do |feature_element|
      feature_text = feature_element.text.strip
      if feature_text.present? && feature_text.length < 100
        features << feature_text
      end
    end
    
    # Also extract from other potential feature lists
    doc.css('li, .feature').each do |feature_element|
      feature_text = feature_element.text.strip
      if feature_text.present? && feature_text.length < 100 && !features.include?(feature_text)
        # Only add if it looks like a property feature
        if feature_text.match?(/\b(bedroom|bathroom|furnished|balcony|parking|view|condition|distance|air|pool|garden)\b/i)
          features << feature_text
        end
      end
    end
    
    # Also check description for features like "furnished"
    description = doc.css('.property-description, .description, p').text.downcase
    
    # Add standard features based on content
    if description.include?('fully furnished') && !features.any? { |f| f.downcase.include?('furnished') }
      features << 'Furnished'
    end
    
    if description.include?('parking') && !features.any? { |f| f.downcase.include?('parking') }
      features << 'Parking'
    end
    
    if description.include?('balcony') && !features.any? { |f| f.downcase.include?('balcony') }
      features << 'Balcony'
    end
    
    features
  end

  def extract_agent_details(doc)
    agent = {}
    
    # Extract office details
    office_section = doc.css('h3:contains("OFFICE DETAILS")').first&.parent
    if office_section
      agent['office_address'] = office_section.css('a[href*="offices"]').first&.text&.strip
      agent['phone'] = office_section.css('a[href^="tel:"]').first&.text&.strip
      agent['email'] = office_section.css('a[href^="mailto:"]').first&.text&.strip
    end
    
    agent['company_name'] = 'Cape Verde Property'
    agent
  end

  def extract_reference
    # Extract reference from URL
    url_parts = scrapable_url.split('/')
    reference_part = url_parts.find { |part| part.match?(/^[a-z0-9]+$/) && part.length > 5 }
    reference_part || "cvp_#{SecureRandom.hex(4)}"
  end

  def extract_image_urls(doc)
    image_urls = []
    
    # Extract from various image sources
    image_selectors = [
      'img[src*="wdcdn.co"]',
      '.property-image img',
      '.gallery img',
      'img[alt*="Property Image"]'
    ]
    
    image_selectors.each do |selector|
      doc.css(selector).each do |img|
        src = img['src'] || img['data-src']
        if src && src.include?('wdcdn.co')
          # Convert to higher quality if possible
          high_quality_src = src.gsub('/webp/l/', '/l/').gsub('.webp', '.jpg')
          image_urls << high_quality_src
        end
      end
    end
    
    image_urls.uniq
  end

  def map_property_to_asset_schema(property)
    location = property['location'] || {}
    
    {
      'categories' => property['features']&.map { |f| { 'name' => f } } || [],
      'city' => location['city'] || 'Unknown',
      'city_search_key' => location['city']&.downcase&.gsub(/\s+/, '-') || 'unknown',
      'constructed_area' => 0.0, # Not available, default to 0.0
      'count_bathrooms' => property['bathrooms'].to_f || 0.0,
      'count_bedrooms' => property['bedrooms'] || 0,
      'count_garages' => property['description'].to_s.downcase.include?('garage') ? 1 : 0,
      'count_toilets' => 0, # Not available, default to 0
      'country' => 'Cape Verde',
      'description' => property['description'] || '',
      'details' => {},
      'discarded_at' => nil,
      'energy_performance' => nil,
      'energy_rating' => nil,
      'floor' => nil,
      'has_rental_listings' => false,
      'has_sale_listings' => true,
      'has_sold_transactions' => false,
      'host_on_create' => 'unknown_host',
      'is_ai_generated_realty_asset' => false,
      'latitude' => location['lat'],
      'longitude' => location['lon'],
      'neighborhood' => nil,
      'neighborhood_search_key' => '',
      'plot_area' => 0.0,
      'postal_code' => nil, # Not available in Cape Verde format
      'prop_state_key' => 'for_sale',
      'prop_type_key' => property['property_type']&.downcase&.gsub(/\s+/, '-') || 'property',
      'province' => location['region'] || 'Sal',
      'ra_photos_count' => 0, # Will be updated when images are processed
      'realty_asset_flags' => 0,
      'realty_asset_tags' => property['features'] || [],
      'reference' => property['reference'],
      'region' => location['region'] || 'Sal',
      'rental_listings_count' => 0,
      'sale_listings_count' => 1,
      'sold_transactions_count' => 0,
      'street_address' => property['title'] || '',
      'street_number' => nil,
      'title' => property['title'] || '',
      'year_construction' => 0
    }
  end

  def map_property_to_listing_schema(property)
    agent = property['agent'] || {}
    
    {
      'title' => property['title'] || '',
      'description' => property['description'] || '',
      'archived' => false,
      'commission_cents' => 0,
      'commission_currency' => 'EUR',
      'currency' => 'EUR',
      'design_style' => nil,
      'details_of_rooms' => {},
      'discarded_at' => nil,
      'extra_sale_details' => {
        'agent_details' => agent
      },
      'furnished' => property['description'].to_s.downcase.include?('furnished') || 
                    property['features']&.any? { |f| f.downcase.include?('furnished') } || false,
      'hide_map' => false,
      'highlighted' => false,
      'host_on_create' => 'unknown_host',
      'is_ai_generated_listing' => false,
      'listing_pages_count' => 0,
      'listing_slug' => property['reference'],
      'listing_tags' => [],
      'main_video_url' => nil,
      'obscure_map' => false,
      'page_section_listings_count' => 0,
      'position_in_list' => nil,
      'price_sale_current_cents' => (property['price_raw'] * 100).to_i,
      'price_sale_current_currency' => 'EUR',
      'price_sale_original_cents' => (property['price_raw'] * 100).to_i,
      'price_sale_original_currency' => 'EUR',
      'property_board_items_count' => 0,
      'publish_from' => nil,
      'publish_till' => nil,
      'reference' => property['reference'],
      'related_urls' => {},
      'reserved' => false,
      'sale_listing_features' => property['features']&.map { |f| [f.parameterize, f] }.to_h || {},
      'sale_listing_flags' => 0,
      'sale_listing_gen_prompt' => nil,
      'service_charge_yearly_cents' => 0,
      'service_charge_yearly_currency' => 'EUR',
      'sl_photos_count' => 0, # Will be updated when images are processed
      'visible' => true
    }
  end
end
