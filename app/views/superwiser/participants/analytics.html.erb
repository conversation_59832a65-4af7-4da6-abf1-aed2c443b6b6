<%= javascript_import_module_tag 'participant_analytics' %>

<% content_for(:title) { "Analytics for #{@participant.visitor_token[0..8]}..." } %>

<header class="main-content__header" role="banner">
  <h1 class="main-content__page-title">
    <%= content_for(:title) %>
  </h1>

  <div class="main-content__header-actions">
    <%= link_to "Back to Participant", superwiser_participant_path(@participant), 
        class: "btn btn--secondary" %>
  </div>
</header>

<section class="main-content__body">
  <!-- Engagement Timeline -->
  <div class="analytics-section">
    <h2>Engagement Timeline (Last 30 Days)</h2>
    <div class="chart-container">
      <%= line_chart @analytics_data[:engagement_timeline], 
          height: "300px", 
          colors: ["#3B82F6"],
          library: { 
            responsive: true,
            scales: {
              y: { beginAtZero: true }
            }
          } %>
    </div>
  </div>

  <!-- Page Views -->
  <div class="analytics-section">
    <h2>Top Page Views</h2>
    <div class="chart-container">
      <%= bar_chart @analytics_data[:page_views], 
          height: "300px", 
          colors: ["#10B981"],
          library: { 
            responsive: true,
            scales: {
              x: { beginAtZero: true }
            }
          } %>
    </div>
  </div>

  <!-- Event Breakdown -->
  <div class="analytics-section">
    <h2>Event Breakdown</h2>
    <div class="chart-container">
      <%= pie_chart @analytics_data[:event_breakdown], 
          height: "300px",
          colors: ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"] %>
    </div>
  </div>

  <!-- Session Analysis -->
  <div class="analytics-section">
    <h2>Session Analysis</h2>
    <div class="session-stats">
      <div class="stat-grid">
        <div class="stat-item">
          <h3>Total Sessions</h3>
          <p class="stat-value"><%= @analytics_data[:session_analysis][:total_sessions] %></p>
        </div>
        
        <div class="stat-item">
          <h3>Average Duration</h3>
          <p class="stat-value"><%= @analytics_data[:session_analysis][:average_duration] %> min</p>
        </div>
        
        <div class="stat-item">
          <h3>Longest Session</h3>
          <p class="stat-value"><%= @analytics_data[:session_analysis][:longest_session].round(2) %> min</p>
        </div>
        
        <div class="stat-item">
          <h3>Sessions with Events</h3>
          <p class="stat-value"><%= @analytics_data[:session_analysis][:sessions_with_events] %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Tables -->
  <div class="analytics-section">
    <h2>Page Views Detail</h2>
    <table class="analytics-table">
      <thead>
        <tr>
          <th>Page URL</th>
          <th>Views</th>
          <th>Percentage</th>
        </tr>
      </thead>
      <tbody>
        <% total_views = @analytics_data[:page_views].values.sum %>
        <% @analytics_data[:page_views].each do |url, count| %>
          <tr>
            <td><%= url || 'Unknown' %></td>
            <td><%= count %></td>
            <td><%= total_views > 0 ? ((count.to_f / total_views) * 100).round(1) : 0 %>%</td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <div class="analytics-section">
    <h2>Event Details</h2>
    <table class="analytics-table">
      <thead>
        <tr>
          <th>Event Type</th>
          <th>Count</th>
          <th>Percentage</th>
        </tr>
      </thead>
      <tbody>
        <% total_events = @analytics_data[:event_breakdown].values.sum %>
        <% @analytics_data[:event_breakdown].each do |event_name, count| %>
          <tr>
            <td><%= event_name %></td>
            <td><%= count %></td>
            <td><%= total_events > 0 ? ((count.to_f / total_events) * 100).round(1) : 0 %>%</td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Participant Summary -->
  <div class="analytics-section">
    <h2>Participant Summary</h2>
    <div class="summary-grid">
      <div class="summary-item">
        <h3>Engagement Score</h3>
        <p class="summary-value engagement-score-<%= @participant.engagement_score > 50 ? 'high' : 'low' %>">
          <%= @participant.engagement_score %>
        </p>
      </div>
      
      <div class="summary-item">
        <h3>Behavior Category</h3>
        <p class="summary-value"><%= @participant.behavior_category.capitalize %></p>
      </div>
      
      <div class="summary-item">
        <h3>First Visit</h3>
        <p class="summary-value"><%= @participant.first_visit_at&.strftime('%Y-%m-%d') %></p>
      </div>
      
      <div class="summary-item">
        <h3>Last Visit</h3>
        <p class="summary-value"><%= @participant.last_visit_at&.strftime('%Y-%m-%d') %></p>
      </div>
      
      <div class="summary-item">
        <h3>Days Active</h3>
        <p class="summary-value"><%= @participant.days_since_first_visit.to_i %></p>
      </div>
      
      <div class="summary-item">
        <h3>Returning Visitor</h3>
        <p class="summary-value"><%= @participant.returning_visitor? ? 'Yes' : 'No' %></p>
      </div>
    </div>
  </div>
</section>

<style>
  .analytics-section {
    margin: 2rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
  }
  
  .analytics-section h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #ddd;
    padding-bottom: 0.5rem;
  }
  
  .chart-container {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .stat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
  }
  
  .stat-item {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .stat-item h3 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #666;
  }
  
  .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0;
    color: #333;
  }
  
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
  }
  
  .summary-item {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .summary-item h3 {
    margin: 0 0 0.5rem 0;
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
  }
  
  .summary-value {
    font-size: 1.2rem;
    font-weight: bold;
    margin: 0;
    color: #333;
  }
  
  .engagement-score-high {
    color: #10B981;
  }
  
  .engagement-score-low {
    color: #F59E0B;
  }
  
  .analytics-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background: white;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .analytics-table th,
  .analytics-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }
  
  .analytics-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
  }
  
  .analytics-table tr:hover {
    background: #f8f9fa;
  }
</style>
