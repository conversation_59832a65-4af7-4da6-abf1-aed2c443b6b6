<% content_for(:title) { "Participant #{@participant.visitor_token[0..8]}..." } %>

<header class="main-content__header" role="banner">
  <h1 class="main-content__page-title">
    <%= content_for(:title) %>
  </h1>

  <div class="main-content__header-actions">
    <%= link_to "Analytics", analytics_superwiser_participant_path(@participant), 
        class: "btn btn--primary" %>
    <%= link_to "Sync Data", sync_superwiser_participant_path(@participant), 
        method: :post, class: "btn btn--secondary" %>
    <%= link_to "Edit", edit_superwiser_participant_path(@participant), 
        class: "btn btn--secondary" %>
  </div>
</header>

<section class="main-content__body">
  <!-- Participant Overview -->
  <div class="participant-overview">
    <div class="attribute-data attribute-data--show">
      <dt class="attribute-data__dt">Visitor Token</dt>
      <dd class="attribute-data__dd"><code><%= @participant.visitor_token %></code></dd>
    </div>

    <div class="attribute-data attribute-data--show">
      <dt class="attribute-data__dt">Engagement Score</dt>
      <dd class="attribute-data__dd">
        <span class="badge badge--<%= @participant.engagement_score > 50 ? 'success' : 'warning' %>">
          <%= @participant.engagement_score %>
        </span>
      </dd>
    </div>

    <div class="attribute-data attribute-data--show">
      <dt class="attribute-data__dt">Behavior Category</dt>
      <dd class="attribute-data__dd">
        <span class="badge badge--info"><%= @participant.behavior_category.capitalize %></span>
      </dd>
    </div>

    <div class="attribute-data attribute-data--show">
      <dt class="attribute-data__dt">Returning Visitor</dt>
      <dd class="attribute-data__dd">
        <%= @participant.returning_visitor? ? "Yes" : "No" %>
      </dd>
    </div>
  </div>

  <!-- Analytics Summary -->
  <div class="analytics-summary">
    <h2>Analytics Summary</h2>
    <div class="stats-grid">
      <div class="stat-card">
        <h3>Total Visits</h3>
        <p class="stat-number"><%= @analytics_summary[:total_sessions] %></p>
      </div>
      
      <div class="stat-card">
        <h3>Total Events</h3>
        <p class="stat-number"><%= @analytics_summary[:total_events] %></p>
      </div>
      
      <div class="stat-card">
        <h3>Avg Events/Session</h3>
        <p class="stat-number"><%= @analytics_summary[:average_events_per_session] %></p>
      </div>
      
      <div class="stat-card">
        <h3>Days Active</h3>
        <p class="stat-number"><%= @analytics_summary[:days_active] %></p>
      </div>
    </div>
  </div>

  <!-- Most Visited Pages -->
  <% if @analytics_summary[:most_visited_pages].any? %>
    <div class="most-visited-pages">
      <h2>Most Visited Pages</h2>
      <table class="table">
        <thead>
          <tr>
            <th>Page URL</th>
            <th>Views</th>
          </tr>
        </thead>
        <tbody>
          <% @analytics_summary[:most_visited_pages].each do |url, count| %>
            <tr>
              <td><%= url || 'Unknown' %></td>
              <td><%= count %></td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  <% end %>

  <!-- Recent Visits -->
  <div class="recent-visits">
    <h2>Recent Visits</h2>
    <table class="table">
      <thead>
        <tr>
          <th>Started At</th>
          <th>Landing Page</th>
          <th>Referrer</th>
          <th>Device</th>
          <th>Events</th>
        </tr>
      </thead>
      <tbody>
        <% @recent_visits.each do |visit| %>
          <tr>
            <td><%= visit.started_at&.strftime('%Y-%m-%d %H:%M') %></td>
            <td><%= truncate(visit.landing_page, length: 50) %></td>
            <td><%= truncate(visit.referrer, length: 30) %></td>
            <td><%= visit.device_type %></td>
            <td><%= visit.events.count %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Recent Events -->
  <div class="recent-events">
    <h2>Recent Events</h2>
    <table class="table">
      <thead>
        <tr>
          <th>Time</th>
          <th>Event Name</th>
          <th>Properties</th>
        </tr>
      </thead>
      <tbody>
        <% @recent_events.each do |event| %>
          <tr>
            <td><%= event.time&.strftime('%Y-%m-%d %H:%M:%S') %></td>
            <td><%= event.name %></td>
            <td><%= truncate(event.properties.to_json, length: 100) %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Standard Administrate Show Fields -->
  <% dashboard.show_page_attributes.each do |attribute| %>
    <% next if [:id, :visitor_token, :engagement_score, :behavior_category, :returning_visitor, :ahoy_visits].include?(attribute) %>
    <div class="attribute-data attribute-data--show">
      <dt class="attribute-data__dt">
        <%= t(
          "helpers.label.#{resource_name}.#{attribute}",
          default: attribute.to_s.humanize,
        ) %>
      </dt>
      <dd class="attribute-data__dd">
        <%= render_field attribute, resource: @participant %>
      </dd>
    </div>
  <% end %>
</section>

<style>
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
  }
  
  .stat-card {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
  }
  
  .stat-card h3 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #666;
  }
  
  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
    color: #333;
  }
  
  .badge {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: bold;
  }
  
  .badge--success { background: #d4edda; color: #155724; }
  .badge--warning { background: #fff3cd; color: #856404; }
  .badge--info { background: #d1ecf1; color: #0c5460; }
  
  .analytics-summary,
  .most-visited-pages,
  .recent-visits,
  .recent-events {
    margin: 2rem 0;
  }
  
  .table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
  }
  
  .table th,
  .table td {
    padding: 0.5rem;
    border: 1px solid #ddd;
    text-align: left;
  }
  
  .table th {
    background: #f8f9fa;
    font-weight: bold;
  }
</style>
