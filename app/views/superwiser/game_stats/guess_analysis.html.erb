<h1>Guess Analysis Statistics</h1>

<h2>Summary</h2>
<ul>
  <li><strong>Total Guesses Made:</strong> <%= @total_guesses %></li>
  <li><strong>Average Percentage Above/Below Actual:</strong> <%= @average_accuracy ? number_with_precision(@average_accuracy, precision: 2) : 'N/A' %>%</li>
</ul>

<h2>Top 10 Most Common Guessed Amounts</h2>
<% if @top_guessed_amounts.present? %>
  <table>
    <thead>
      <tr>
        <th>Guessed Amount</th>
        <th>Currency</th>
        <th>Count</th>
      </tr>
    </thead>
    <tbody>
      <% @top_guessed_amounts.each do |(price_cents, currency), count| %>
        <tr>
          <td><%= number_to_currency(price_cents / 100.0, unit: currency_symbol(currency)) %></td>
          <td><%= currency %></td>
          <td><%= count %></td>
        </tr>
      <% end %>
    </tbody>
  </table>
<% else %>
  <p>No guess data available for top amounts.</p>
<% end %>

<h2>Guess Distribution by Percentage</h2>
<div id="guess-distribution-chart">
  <!-- Chart will be rendered here -->
  <p>Chart data: <%= @guess_distribution_by_percentage.to_json %></p>
</div>

<%# Helper to get currency symbol - this might ideally be in a helper file %>
<% content_for :extra_script_tags do %>
  <script type="text/javascript">
    function currency_symbol(currency_code) {
      // This is a very basic mapping, a more robust solution might be needed
      const symbols = {
        "USD": "$",
        "EUR": "€",
        "GBP": "£",
        // Add other currencies as needed
      };
      return symbols[currency_code] || currency_code;
    }
  </script>
<% end %>
