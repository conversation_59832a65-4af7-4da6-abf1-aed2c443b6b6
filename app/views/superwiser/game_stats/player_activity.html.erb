<h1>Player Activity Statistics</h1>

<h2>Player Leaderboard</h2>
<% if @player_stats.present? %>
  <table>
    <thead>
      <tr>
        <th>Player Identifier</th>
        <th>Games Played</th>
        <th>Average Score</th>
        <th>Last Played Date</th>
      </tr>
    </thead>
    <tbody>
      <% @player_stats.each do |stat| %>
        <tr>
          <td><%= stat.player_identifier %></td>
          <td><%= stat.games_played %></td>
          <td><%= stat.average_player_score ? number_with_precision(stat.average_player_score, precision: 2) : 'N/A' %></td>
          <td><%= stat.last_played_date ? l(stat.last_played_date, format: :long) : 'N/A' %></td>
        </tr>
      <% end %>
    </tbody>
  </table>
<% else %>
  <p>No player activity data available.</p>
<% end %>

<h2>Score Distribution</h2>
<div id="score-distribution-chart">
  <!-- Chart will be rendered here -->
  <p>Chart data: <%= @score_distribution.to_json %></p>
</div>
