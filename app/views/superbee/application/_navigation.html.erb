<%#
# Navigation

This partial is used to display the navigation in Administrate.
By default, the navigation contains navigation links
for all resources in the admin dashboard,
as defined by the routes in the `admin/` namespace
%>
<nav class="navigation">
  <%= link_to("/superwiser", "/superwiser", class: "button button--alt button--nav")  %>
  <% Administrate::Namespace.new(namespace).resources_with_index_route.each do |resource| %>
    <%= link_to(
      display_resource_name(resource),
      resource_index_route(resource),
      class: "navigation__link navigation__link--#{nav_link_state(resource)}"
    ) if accessible_action?(model_from_resource(resource), :index) %>
  <% end %>
</nav>
