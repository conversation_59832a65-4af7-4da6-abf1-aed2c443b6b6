<%= javascript_import_module_tag 'participant_analytics' %>

<div class="participant-analytics-dashboard">
  <div class="dashboard-header bg-white shadow-sm border-b border-gray-200 px-6 py-4">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Participant Analytics</h1>
        <p class="text-gray-600 mt-1">Insights into visitor behavior and engagement</p>
      </div>
      
      <div class="flex space-x-4">
        <%= form_with url: request.path, method: :get, local: true, class: "flex space-x-2" do |form| %>
          <%= form.date_field :start_date, value: params[:start_date] || 30.days.ago.to_date, 
                              class: "px-3 py-2 border border-gray-300 rounded-md text-sm" %>
          <%= form.date_field :end_date, value: params[:end_date] || Date.current, 
                              class: "px-3 py-2 border border-gray-300 rounded-md text-sm" %>
          <%= form.submit "Update", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700" %>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="dashboard-nav border-b border-gray-200 px-6">
    <nav class="-mb-px flex space-x-8">
      <%= link_to "Overview", participant_analytics_index_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'index'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'index'}" %>
      <%= link_to "Dashboard", participant_analytics_dashboard_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'dashboard'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'dashboard'}" %>
      <%= link_to "Engagement", participant_analytics_engagement_dashboard_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'engagement_dashboard'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'engagement_dashboard'}" %>
      <%= link_to "Traffic", participant_analytics_traffic_dashboard_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'traffic_dashboard'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'traffic_dashboard'}" %>
      <%= link_to "Behavior", participant_analytics_behavior_dashboard_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'behavior_dashboard'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'behavior_dashboard'}" %>
    </nav>
  </div>
</div>

<style>
  .chart-wrapper {
    height: 400px;
  }
  
  .metric-card {
    transition: transform 0.2s;
  }
  
  .metric-card:hover {
    transform: translateY(-2px);
  }
  
  .chart-container {
    transition: box-shadow 0.2s;
  }
  
  .chart-container:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
</style>

<script>
  function initializeChart(elementId, dataUrl, chartType) {
    fetch(dataUrl)
      .then(response => response.json())
      .then(data => {
        const element = document.getElementById(elementId);
        if (element && data.chart_data) {
          const chartOptions = {
            height: '400px',
            library: {
              responsive: true,
              maintainAspectRatio: false
            }
          };

          if (chartType === 'line') {
            chartOptions.colors = ['#3B82F6'];
            new Chartkick.LineChart(element, data.chart_data, chartOptions);
          } else if (chartType === 'pie') {
            new Chartkick.PieChart(element, data.chart_data, chartOptions);
          } else if (chartType === 'bar') {
            chartOptions.colors = ['#10B981'];
            new Chartkick.BarChart(element, data.chart_data, chartOptions);
          } else if (chartType === 'column') {
            chartOptions.colors = ['#8B5CF6'];
            new Chartkick.ColumnChart(element, data.chart_data, chartOptions);
          }
        }
      })
      .catch(error => console.error('Error loading chart data:', error));
  }
</script>
