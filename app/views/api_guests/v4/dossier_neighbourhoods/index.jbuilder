json.neighbourhoods @dossier.dossier_assets do |dossier_asset|
  # json.call(dossier_asset,
  #           'uuid',
  #           'neighbourhood_records',
  #           'contextual_records',
  #           'dossier_asset_contextual_records',
  #           'created_at',
  #           'updated_at')

  if dossier_asset.main_neighbourhood_record
    json.call(dossier_asset.main_neighbourhood_record, 'id',
              'contxt_postcode', 'contxt_outcode',
              'record_source_url',
              'location_details',
              'location_summary',
              'nearest_postcodes',
              'energy_consumption',
              'nearest_post_boxes',
              'food_standards_ratings',
              'maps',
              'geodata',
              'politics',
              'broadband',
              'elevation',
              'transport',
              'deprivation',
              'census_areas',
              'house_prices',
              'page_metadata',
              'postcode_header')
  else
    json.set! 'contxt_postcode', dossier_asset.postal_code
    json.set! 'main_neighbourhood_record', 'not found'

    #             store_attribute :extra_contxt_details, :maps, :json, default: []
    # store_attribute :extra_contxt_details, :geodata, :json, default: {}
    # store_attribute :extra_contxt_details, :politics, :json, default: {}
    # store_attribute :extra_contxt_details, :broadband, :json, default: {}
    # store_attribute :extra_contxt_details, :elevation, :json, default: {}
    # store_attribute :extra_contxt_details, :transport, :json, default: {}
    # store_attribute :extra_contxt_details, :deprivation, :json, default: {}
    # store_attribute :extra_contxt_details, :census_areas, :json, default: {}
    # store_attribute :extra_contxt_details, :house_prices, :json, default: []
    # store_attribute :extra_contxt_details, :page_metadata, :json, default: {}
    # store_attribute :extra_contxt_details, :postcode_header, :string
    # store_attribute :extra_contxt_details, :location_details, :json, default: {}
    # store_attribute :extra_contxt_details, :location_summary, :json, default: {}
    # store_attribute :extra_contxt_details, :nearest_postcodes, :json, default: []
    # store_attribute :extra_contxt_details, :energy_consumption, :json, default: {}
    # store_attribute :extra_contxt_details, :nearest_post_boxes, :json, default: []
    # store_attribute :extra_contxt_details, :food_standards_ratings, :json, default: []

  end
  # json.inn do
  #   json.array! dossier_asset.neighbourhood_records do |neighbourhood_record|
  #     json.call(neighbourhood_record,
  #               'id', 'maps')
  #   end
  # end
end
