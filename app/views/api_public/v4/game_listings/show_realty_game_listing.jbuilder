if @realty_game_listing
  json.realty_game_listing do
    # TODO: return more and use on front end
    json.call(@realty_game_listing, 'uuid',
              'visible_in_game', 'position_in_game', 'visible_photo_uuids',
              'ordered_photo_uuids', 'gl_vicinity_atr', 'gl_image_url_atr',
              'gl_country_code_atr',
              'gl_title_atr', 'gl_description_atr')
  end
end

if @sale_listing
  json.sale_listing do
    json.partial! 'api_public/v4/realty_price_games/sale_listing_game_details', sale_listing: @sale_listing
    # json.partial! 'api_public/v4/sale_listings/sale_listing_public_details', sale_listing: @sale_listing
  end
end
