json.price_guess_inputs do
  json.set! 'game_title', @realty_game ? @realty_game.game_title : 'Property Price Challenge'
  json.set! 'game_desc', @realty_game ? @realty_game.game_description : 'Test your property valuation skills'
  json.set! 'should_show_out_links', @scoot.should_show_out_links
  json.set! 'guessed_price_validation', @guessed_price_validation
  json.set! 'game_communities_details', @game_communities_details
  json.property_details do
    json.array! @scoot.listings_for_price_guess do |price_guess_listing|
      json.partial! 'api_public/v4/sale_listings/sale_listing_public_details', sale_listing: price_guess_listing
    end
  end
end
