json.round_up_realty_games do
  json.array! @round_up_realty_games do |round_up_realty_game|
    json.partial! 'api_public/v4/realty_price_games/realty_game_details', realty_game: round_up_realty_game
    json.game_listings do
      json.array! round_up_realty_game.ordered_game_listings do |realty_game_listing|
        json.partial! 'api_public/v4/realty_price_games/game_listing',
                      realty_game_listing: realty_game_listing
      end
    end
  end
end
json.scoot do
  json.call(@scoot, :scoot_title)
  json.call(@scoot, :scoot_notice)
  # above method effectively same as below:
  json.is_price_guess_enabled @scoot.is_price_guess_enabled
  json.available_games_details @scoot.available_games_details
  json.supports_multiple_games @scoot.supports_multiple_games
  json.is_price_guess_only @scoot.is_price_guess_only
  json.is_price_guess_public @scoot.is_price_guess_public
end
