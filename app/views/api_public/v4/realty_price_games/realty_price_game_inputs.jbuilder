json.cache! ['price_guess_inputs', @realty_game, @scoot, @guessed_price_validation, @game_communities_details], expires_in: 1.hour do
  json.price_guess_inputs do
    if @realty_game
      json.realty_game_summary do
        json.call(@realty_game,
                  'default_game_currency', 'global_game_slug', 'game_bg_image_url',
                  'game_title', 'game_description')
      end
      # 24 june 2025 - was silly not having realty_game_summary from the start
      # Will now pass that down from layout in fe and eventually get rid
      # of the 5 fields below
      json.set! 'default_game_currency', @realty_game.default_game_currency
      json.set! 'global_game_slug', @realty_game.global_game_slug
      json.set! 'game_bg_image_url', @realty_game.game_bg_image_url
      json.set! 'game_title', @realty_game.game_title || 'Property Price Challenge'
      json.set! 'game_description', @realty_game.game_description || 'Property Price Challenge'
      json.game_listings do
        json.array! @realty_game.realty_game_listings do |realty_game_listing|
          json.call(realty_game_listing, 'guessed_prices_count', 'game_sessions_count',
                    'id')
          # json.set! 'realty_game_listing', realty_game_listing
          json.listing_details do
            json.partial! 'api_public/v4/realty_price_games/sale_listing_game_details', sale_listing: realty_game_listing.sale_listing
          end
        end
      end
    else
      json.set! 'game_listings', []
    end
    json.set! 'game_desc', 'Test your property valuation skills' # 'You will love it'
    json.set! 'should_show_out_links', @scoot.should_show_out_links
    # json.set! 'realty_games', @scoot.realty_games
    json.set! 'guessed_price_validation', @guessed_price_validation
    json.set! 'game_communities_details', @game_communities_details
    # json.property_details do
    #   json.array! @realty_game.realty_game_listings do |realty_game_listing|
    #     json.set! 'realty_game_listing', realty_game_listing
    #   end
    # end
  end
end
