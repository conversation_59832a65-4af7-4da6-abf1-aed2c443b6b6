json.set! 'model_name', 'sale_listing'
hide_listing_pics ||= false
if sale_listing
  # json.set! 'close_uprn_details_json', @close_uprn_details_json
  json.set! 'listing_geo_json', sale_listing.listing_geo_json
  # using realty_asset title and desc was hiding issue when
  # retrieving sale_listing (march 2025)
  json.set! 'title', sale_listing.title # || sale_listing.realty_asset&.title
  json.set! 'description', sale_listing.description # || sale_listing.realty_asset&.description
  json.set! 'catchy_title', sale_listing.catchy_title
  json.set! 'description_short', sale_listing.description_short
  json.set! 'description_medium', sale_listing.description_medium
  json.set! 'description_long', sale_listing.description_long
  json.set! 'description_bullet_points', sale_listing.description_bullet_points
  # TODO: add recent_sales_eval based on LiaToEvalRecentSales
  # json.set! 'recent_sales_eval', {}
  # json.llm_feedback do
  #   if sale_listing.llm_interaction_with_sale_listing
  #     json.call(
  #       sale_listing.llm_interaction_with_sale_listing, 'estimated_fair_value_price',
  #       'is_asking_price_competitive_or_overpriced', 'reasoning_content'
  #     )
  #     json.most_similar_recent_sale do
  #       if sale_listing.llm_interaction_with_sale_listing
  #         json.call(sale_listing.llm_interaction_with_sale_listing.most_similar_sold_transaction_epc, 'uuid',
  #                   'st_epc_uprn', 'st_uprn', 'epc_uprn',
  #                   'sold_transaction_date', 'epc_inspection_date', 'days_difference_epc_and_sale',
  #                   'formatted_sold_price', 'total_floor_area',
  #                   'price_per_floor_area_meter', 'price_per_habitable_room',
  #                   'postcode_area_uuid', 'is_outlier', 'outlier_reason', 'outlier_score', 'epc_address', 'st_address',
  #                   'st_epc_outcode', 'st_epc_postcode', 'st_epc_latitude', 'st_epc_longitude')
  #       end
  #     end
  #     json.other_recent_sales do
  #       if sale_listing.llm_interaction_with_sale_listing.other_similar_sold_transactions_epcs
  #         json.array!(sale_listing.llm_interaction_with_sale_listing.other_similar_sold_transactions_epcs) do |st_epc|
  #           json.call(st_epc, 'uuid',
  #                     'st_epc_uprn', 'st_uprn', 'epc_uprn',
  #                     'is_best_combo', 'sold_transaction_date', 'epc_inspection_date', 'days_difference_epc_and_sale',
  #                     'formatted_sold_price', 'total_floor_area',
  #                     'price_per_floor_area_meter', 'price_per_habitable_room',
  #                     'postcode_area_uuid', 'is_outlier', 'outlier_reason', 'outlier_score', 'epc_address', 'st_address',
  #                     'st_epc_outcode', 'st_epc_postcode', 'st_epc_latitude', 'st_epc_longitude')
  #         end
  #       end
  #     end
  #   end
  # end
  if sale_listing.realty_asset
    json.call(sale_listing.realty_asset, 'ra_uprn', 'count_toilets', 'count_garages', 'area_unit', 'plot_area',
              'constructed_area', 'year_construction', 'count_bedrooms', 'count_bathrooms', 'latitude', 'longitude', 'city', 'region', 'country', 'street_name', 'street_number', 'postal_code', 'street_address')
  end
  json.call(sale_listing, 'sl_uprn', 'created_at', 'updated_at', 'uuid',
            # 'listing_display_url',
            # 'listing_display_url',
            'formatted_display_price',
            'price_sale_current_cents',
            # june 2025 - TODO - stop sending above 2
            # after I figure out a solution for the front end
            'title_meta',
            'main_video_url', 'reference',
            'highlighted', 'archived', 'visible', 'hide_map', 'obscure_map',
            'currency')
  unless hide_listing_pics
    json.sale_listing_pics do
      json.array!(sale_listing.listing_photos.kept) do |asset_photo|
        json.call(asset_photo, 'flag_is_hidden', 'photo_title',
                  'id',
                  # 'discarded_at',
                  'photo_title', 'photo_description',
                  # 'photo_ai_desc', 'realty_asset_photo_tags',
                  'photo_slug',
                  # 'raw_ai_analysis',
                  'image_details',
                  # 'sale_listing_uuid',
                  # 'rental_listing_uuid', 'file_size', 'height', 'width', 'content_type',
                  'sort_order',
                  'uuid')
      end
    end
  end
end
