<%= render 'shared/participant_analytics_header' %>

<div class="traffic-dashboard p-6">
  <!-- Traffic Overview -->
  <div class="traffic-overview mb-8">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="metric-card bg-white rounded-lg shadow p-6">
        <h4 class="text-sm font-medium text-gray-500 mb-2">Top Traffic Source</h4>
        <p class="text-2xl font-bold text-blue-600">
          <%= @traffic_metrics[:sources].max_by { |_, count| count }&.first || 'N/A' %>
        </p>
      </div>
      
      <div class="metric-card bg-white rounded-lg shadow p-6">
        <h4 class="text-sm font-medium text-gray-500 mb-2">Top Country</h4>
        <p class="text-2xl font-bold text-green-600">
          <%= @traffic_metrics[:geographic].first&.first || 'N/A' %>
        </p>
      </div>
      
      <div class="metric-card bg-white rounded-lg shadow p-6">
        <h4 class="text-sm font-medium text-gray-500 mb-2">Top Device</h4>
        <p class="text-2xl font-bold text-purple-600">
          <%= @traffic_metrics[:devices].max_by { |_, count| count }&.first || 'N/A' %>
        </p>
      </div>
    </div>
  </div>

  <!-- Charts -->
  <div class="charts-grid">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <% @chart_configs.each do |config| %>
        <div class="chart-container bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4"><%= config[:title] %></h3>
          <div id="<%= config[:id] %>" class="chart-wrapper" style="height: <%= config[:height] %>"></div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Traffic Source Breakdown -->
  <div class="traffic-breakdown mt-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Traffic Sources Table -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Traffic Sources Breakdown</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <% total_traffic = @traffic_metrics[:sources].values.sum %>
            <% @traffic_metrics[:sources].sort_by { |_, count| -count }.each do |source, count| %>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="w-4 h-4 rounded-full mr-3 
                    <%= case source
                        when 'Direct' then 'bg-blue-500'
                        when 'Search Engine' then 'bg-green-500'
                        when 'Social Media' then 'bg-purple-500'
                        when 'Email' then 'bg-yellow-500'
                        when 'Paid Ads' then 'bg-red-500'
                        else 'bg-gray-500'
                        end %>"></div>
                  <span class="text-sm font-medium text-gray-900"><%= source %></span>
                </div>
                <div class="flex items-center">
                  <div class="w-24 h-2 bg-gray-200 rounded-full mr-3">
                    <div class="h-2 bg-blue-500 rounded-full" 
                         style="width: <%= total_traffic > 0 ? (count.to_f / total_traffic * 100).round(1) : 0 %>%"></div>
                  </div>
                  <span class="text-sm text-gray-600"><%= count %> (<%= total_traffic > 0 ? (count.to_f / total_traffic * 100).round(1) : 0 %>%)</span>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Geographic Distribution Table -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Top Countries</h3>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Participants</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% total_geographic = @traffic_metrics[:geographic].sum { |_, count| count } %>
              <% @traffic_metrics[:geographic].first(10).each do |country, count| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <%= country || 'Unknown' %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= count %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= total_geographic > 0 ? (count.to_f / total_geographic * 100).round(1) : 0 %>%
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    <% @chart_configs.each do |config| %>
      initializeChart('<%= config[:id] %>', '<%= config[:endpoint] %>', '<%= config[:type] %>');
    <% end %>
  });
</script>
