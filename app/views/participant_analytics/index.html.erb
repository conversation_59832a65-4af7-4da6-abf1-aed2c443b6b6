<%= javascript_import_module_tag 'participant_analytics' %>

<div class="participant-analytics-dashboard">
  <div class="dashboard-header bg-white shadow-sm border-b border-gray-200 px-6 py-4">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Participant Analytics</h1>
        <p class="text-gray-600 mt-1">Insights into visitor behavior and engagement</p>
      </div>
      
      <div class="flex space-x-4">
        <%= form_with url: participant_analytics_index_path, method: :get, local: true, class: "flex space-x-2" do |form| %>
          <%= form.date_field :start_date, value: params[:start_date] || 30.days.ago.to_date, 
                              class: "px-3 py-2 border border-gray-300 rounded-md text-sm" %>
          <%= form.date_field :end_date, value: params[:end_date] || Date.current, 
                              class: "px-3 py-2 border border-gray-300 rounded-md text-sm" %>
          <%= form.submit "Update", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700" %>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Overview Metrics -->
  <div class="overview-metrics p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="metric-card bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Participants</p>
            <p class="text-2xl font-semibold text-gray-900"><%= number_with_delimiter(@overview_metrics[:total_participants]) %></p>
          </div>
        </div>
      </div>

      <div class="metric-card bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Active Participants</p>
            <p class="text-2xl font-semibold text-gray-900"><%= number_with_delimiter(@overview_metrics[:active_participants]) %></p>
          </div>
        </div>
      </div>

      <div class="metric-card bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Avg Visits/Participant</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @overview_metrics[:average_visits_per_participant] %></p>
          </div>
        </div>
      </div>

      <div class="metric-card bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">High Engagement</p>
            <p class="text-2xl font-semibold text-gray-900"><%= number_with_delimiter(@overview_metrics[:high_engagement_count]) %></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="dashboard-nav border-b border-gray-200 px-6">
    <nav class="-mb-px flex space-x-8">
      <%= link_to "Overview", participant_analytics_dashboard_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'dashboard'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'dashboard'}" %>
      <%= link_to "Engagement", participant_analytics_engagement_dashboard_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'engagement_dashboard'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'engagement_dashboard'}" %>
      <%= link_to "Traffic", participant_analytics_traffic_dashboard_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'traffic_dashboard'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'traffic_dashboard'}" %>
      <%= link_to "Behavior", participant_analytics_behavior_dashboard_path, 
          class: "py-2 px-1 border-b-2 font-medium text-sm #{'border-blue-500 text-blue-600' if action_name == 'behavior_dashboard'} #{'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' unless action_name == 'behavior_dashboard'}" %>
    </nav>
  </div>

  <!-- Charts Grid -->
  <div class="charts-grid p-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Participants Over Time -->
      <div class="chart-container bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">New Participants Over Time</h3>
        <div id="participants-over-time-chart" class="chart-wrapper"></div>
      </div>

      <!-- Visit Distribution -->
      <div class="chart-container bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Visit Distribution</h3>
        <div id="visit-distribution-chart" class="chart-wrapper"></div>
      </div>

      <!-- Engagement Scores -->
      <div class="chart-container bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Engagement Score Distribution</h3>
        <div id="engagement-scores-chart" class="chart-wrapper"></div>
      </div>

      <!-- Behavior Categories -->
      <div class="chart-container bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Behavior Categories</h3>
        <div id="behavior-categories-chart" class="chart-wrapper"></div>
      </div>
    </div>
  </div>

  <!-- Top Participants Table -->
  <div class="top-participants p-6">
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Top Participants</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visitor Token</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Engagement Score</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Visits</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Events</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">First Visit</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Visit</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @top_participants.each do |participant| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                  <%= participant[:visitor_token][0..8] %>...
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <%= participant[:engagement_score] %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= participant[:total_visits] %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= participant[:total_events] %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span class="capitalize"><%= participant[:behavior_category] %></span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= participant[:first_visit_at]&.strftime('%Y-%m-%d') %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= participant[:last_visit_at]&.strftime('%Y-%m-%d') %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<style>
  .chart-wrapper {
    height: 400px;
  }
  
  .metric-card {
    transition: transform 0.2s;
  }
  
  .metric-card:hover {
    transform: translateY(-2px);
  }
  
  .chart-container {
    transition: box-shadow 0.2s;
  }
  
  .chart-container:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initializeChart('participants-over-time-chart', '<%= participant_analytics_participants_over_time_path(format: :json) %>', 'line');
    initializeChart('visit-distribution-chart', '<%= participant_analytics_visit_distribution_path(format: :json) %>', 'pie');
    initializeChart('engagement-scores-chart', '<%= participant_analytics_engagement_scores_path(format: :json) %>', 'pie');
    initializeChart('behavior-categories-chart', '<%= participant_analytics_behavior_categories_path(format: :json) %>', 'pie');
  });

  function initializeChart(elementId, dataUrl, chartType) {
    fetch(dataUrl)
      .then(response => response.json())
      .then(data => {
        const element = document.getElementById(elementId);
        if (element && data.chart_data) {
          if (chartType === 'line') {
            new Chartkick.LineChart(element, data.chart_data, {
              height: '400px',
              colors: ['#3B82F6'],
              library: {
                responsive: true,
                maintainAspectRatio: false
              }
            });
          } else if (chartType === 'pie') {
            new Chartkick.PieChart(element, data.chart_data, {
              height: '400px',
              library: {
                responsive: true,
                maintainAspectRatio: false
              }
            });
          }
        }
      })
      .catch(error => console.error('Error loading chart data:', error));
  }
</script>
