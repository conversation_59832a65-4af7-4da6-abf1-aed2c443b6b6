<%= render 'shared/participant_analytics_header' %>

<div class="engagement-dashboard p-6">
  <!-- Engagement Overview -->
  <div class="engagement-overview mb-8">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="metric-card bg-white rounded-lg shadow p-6">
        <h4 class="text-sm font-medium text-gray-500 mb-2">Average Engagement Score</h4>
        <p class="text-3xl font-bold text-blue-600">
          <%= (@engagement_metrics[:top_participants].sum { |p| p[:engagement_score] } / [@engagement_metrics[:top_participants].size, 1].max).round(1) %>
        </p>
      </div>
      
      <div class="metric-card bg-white rounded-lg shadow p-6">
        <h4 class="text-sm font-medium text-gray-500 mb-2">High Engagement Participants</h4>
        <p class="text-3xl font-bold text-green-600">
          <%= @engagement_metrics[:overview][:high_engagement_count] %>
        </p>
      </div>
      
      <div class="metric-card bg-white rounded-lg shadow p-6">
        <h4 class="text-sm font-medium text-gray-500 mb-2">Avg Session Duration</h4>
        <p class="text-3xl font-bold text-purple-600">
          <%= @engagement_metrics[:overview][:average_session_duration] %> min
        </p>
      </div>
    </div>
  </div>

  <!-- Charts -->
  <div class="charts-grid">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <% @chart_configs.each do |config| %>
        <div class="chart-container bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4"><%= config[:title] %></h3>
          <div id="<%= config[:id] %>" class="chart-wrapper" style="height: <%= config[:height] %>"></div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Top Engaged Participants -->
  <div class="top-engaged mt-8">
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Most Engaged Participants</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visitor</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Engagement Score</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visits</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Events</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Active</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @engagement_metrics[:top_participants].each_with_index do |participant, index| %>
              <tr class="<%= 'bg-yellow-50' if index < 3 %>">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <% if index == 0 %>
                    🥇
                  <% elsif index == 1 %>
                    🥈
                  <% elsif index == 2 %>
                    🥉
                  <% else %>
                    <%= index + 1 %>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                  <%= participant[:visitor_token][0..8] %>...
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 w-10 h-2 bg-gray-200 rounded-full mr-3">
                      <div class="h-2 bg-gradient-to-r from-green-400 to-blue-500 rounded-full" 
                           style="width: <%= [participant[:engagement_score], 100].min %>%"></div>
                    </div>
                    <span class="font-semibold"><%= participant[:engagement_score] %></span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= participant[:total_visits] %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= participant[:total_events] %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    <%= case participant[:behavior_category]
                        when 'explorer' then 'bg-purple-100 text-purple-800'
                        when 'engaged' then 'bg-green-100 text-green-800'
                        when 'regular' then 'bg-blue-100 text-blue-800'
                        when 'casual' then 'bg-yellow-100 text-yellow-800'
                        else 'bg-gray-100 text-gray-800'
                        end %>">
                    <%= participant[:behavior_category].capitalize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= participant[:days_active] %></td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    <% @chart_configs.each do |config| %>
      initializeChart('<%= config[:id] %>', '<%= config[:endpoint] %>', '<%= config[:type] %>');
    <% end %>
  });
</script>
