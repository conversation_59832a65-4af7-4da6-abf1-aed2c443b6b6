<%= render 'shared/participant_analytics_header' %>

<div class="behavior-dashboard p-6">
  <!-- Behavior Overview -->
  <div class="behavior-overview mb-8">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <% @behavior_metrics[:categories].each do |category, count| %>
        <div class="metric-card bg-white rounded-lg shadow p-6">
          <h4 class="text-sm font-medium text-gray-500 mb-2"><%= category.capitalize %> Users</h4>
          <p class="text-2xl font-bold 
            <%= case category
                when 'explorer' then 'text-purple-600'
                when 'engaged' then 'text-green-600'
                when 'regular' then 'text-blue-600'
                when 'casual' then 'text-yellow-600'
                else 'text-gray-600'
                end %>">
            <%= count %>
          </p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Charts -->
  <div class="charts-grid">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <% @chart_configs.each do |config| %>
        <div class="chart-container bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4"><%= config[:title] %></h3>
          <div id="<%= config[:id] %>" class="chart-wrapper" style="height: <%= config[:height] %>"></div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Behavior Analysis -->
  <div class="behavior-analysis mt-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Behavior Categories Breakdown -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Behavior Categories</h3>
          <p class="text-sm text-gray-600 mt-1">Understanding user engagement patterns</p>
        </div>
        <div class="p-6">
          <div class="space-y-6">
            <% total_behavior = @behavior_metrics[:categories].values.sum %>
            <% @behavior_metrics[:categories].sort_by { |_, count| -count }.each do |category, count| %>
              <div class="behavior-category-item">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full mr-3 
                      <%= case category
                          when 'explorer' then 'bg-purple-500'
                          when 'engaged' then 'bg-green-500'
                          when 'regular' then 'bg-blue-500'
                          when 'casual' then 'bg-yellow-500'
                          else 'bg-gray-500'
                          end %>"></div>
                    <span class="text-sm font-medium text-gray-900 capitalize"><%= category %></span>
                  </div>
                  <span class="text-sm text-gray-600"><%= count %> users</span>
                </div>
                <div class="w-full h-2 bg-gray-200 rounded-full">
                  <div class="h-2 rounded-full 
                    <%= case category
                        when 'explorer' then 'bg-purple-500'
                        when 'engaged' then 'bg-green-500'
                        when 'regular' then 'bg-blue-500'
                        when 'casual' then 'bg-yellow-500'
                        else 'bg-gray-500'
                        end %>" 
                       style="width: <%= total_behavior > 0 ? (count.to_f / total_behavior * 100).round(1) : 0 %>%"></div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                  <%= case category
                      when 'explorer' then 'Users who visit many different pages'
                      when 'engaged' then 'Users with high event activity'
                      when 'regular' then 'Returning visitors with consistent activity'
                      when 'casual' then 'Users with minimal engagement'
                      else 'Other user behavior patterns'
                      end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Session Duration Analysis -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Session Duration Insights</h3>
          <p class="text-sm text-gray-600 mt-1">How long users spend on your site</p>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <% @behavior_metrics[:session_duration].each do |duration_range, count| %>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                  <span class="text-sm font-medium text-gray-900"><%= duration_range %></span>
                </div>
                <div class="flex items-center">
                  <div class="w-20 h-2 bg-gray-200 rounded-full mr-3">
                    <% max_count = @behavior_metrics[:session_duration].values.max %>
                    <div class="h-2 bg-blue-500 rounded-full" 
                         style="width: <%= max_count > 0 ? (count.to_f / max_count * 100).round(1) : 0 %>%"></div>
                  </div>
                  <span class="text-sm text-gray-600"><%= count %></span>
                </div>
              </div>
            <% end %>
          </div>
          
          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 class="text-sm font-medium text-blue-900 mb-2">💡 Insights</h4>
            <ul class="text-xs text-blue-800 space-y-1">
              <li>• Sessions under 1 minute may indicate bounce behavior</li>
              <li>• Sessions over 5 minutes show strong engagement</li>
              <li>• Optimize content for your most common session length</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Visit Patterns -->
  <div class="visit-patterns mt-8">
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Visit Patterns</h3>
        <p class="text-sm text-gray-600 mt-1">Understanding how often users return</p>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <% @behavior_metrics[:visit_distribution].each do |visit_range, count| %>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
              <div class="text-2xl font-bold text-gray-900 mb-1"><%= count %></div>
              <div class="text-sm text-gray-600"><%= visit_range %></div>
              <div class="mt-2 w-full h-1 bg-gray-200 rounded-full">
                <% max_visits = @behavior_metrics[:visit_distribution].values.max %>
                <div class="h-1 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full" 
                     style="width: <%= max_visits > 0 ? (count.to_f / max_visits * 100).round(1) : 0 %>%"></div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    <% @chart_configs.each do |config| %>
      initializeChart('<%= config[:id] %>', '<%= config[:endpoint] %>', '<%= config[:type] %>');
    <% end %>
  });
</script>
