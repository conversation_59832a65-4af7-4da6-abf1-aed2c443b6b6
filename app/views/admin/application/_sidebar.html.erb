<%#
# Sidebar
#
# This partial is used to display the sidebar navigation.
# It is rendered by Administrate::ApplicationController,
# if it is present at `app/views/admin/application/_sidebar.html.erb`.
#
# It is not rendered if `Administrate::ApplicationController.show_sidebar?`
# returns false.
#
# It is generated on every request by `Administrate::Navigation#render_sidebar`.
#
# Content determined by the Admin::Navigation class in `config/navigation.rb`
# or by `dashboard.navigation_items` in each dashboard.
%>

<nav class="sidebar" data-administrate-comfy-sidebar>
  <% Administrate::Namespace.new(namespace).resources.each do |resource| %>
    <%= link_to(
      display_resource_name(resource),
      [namespace, resource_index_route_key(resource)],
      class: "sidebar__link sidebar__link--#{nav_link_state(resource)}"
    ) if show_action?(:index, model_from_resource(resource)) %>
  <% end %>

  <%# Custom Links for Game Stats %>
  <hr class="sidebar__separator">
  <h3 class="sidebar__header">Game Statistics</h3>
  <ul class="sidebar__links">
    <li>
      <%= link_to 'Overview', superwiser_game_stats_overview_path, class: "sidebar__link" %>
    </li>
    <li>
      <%= link_to 'Player Activity', superwiser_game_stats_player_activity_path, class: "sidebar__link" %>
    </li>
    <li>
      <%= link_to 'Guess Analysis', superwiser_game_stats_guess_analysis_path, class: "sidebar__link" %>
    </li>
  </ul>
</nav>
