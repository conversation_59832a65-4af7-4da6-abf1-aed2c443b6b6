var PwbPropsController = Paloma.controller('Pwb/Props');

PwbPropsController.prototype.show = function() {
  // // Below for properties slider
  // // don't have something similar for main landing yet
  // $(".carousel-inner .item").click(function() {
  //   var carouselStatus = $('#propCarousel').data('status');
  //   if (carouselStatus && carouselStatus === "paused") {
  //     $('#propCarousel').carousel('cycle');
  //     $('#propCarousel').data('status', "cycling");
  //   } else {
  //     $('#propCarousel').carousel('pause');
  //     $('#propCarousel').data('status', "paused");
  //   }
  // });

  // var currentItemForMap = this.params.property_details;

};
