// This is a manifest file that'll be compiled into application.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, vendor/assets/javascripts,
// or any plugin's vendor/assets/javascripts directory can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of the
// compiled file. JavaScript code in this file should be added after the last require_* statement.
//
// Read Sprockets README (https://github.com/rails/sprockets#sprockets-directives) for details
// about supported directives.
//
//= require jquery
// require jquery.turbolinks

// below needed for bootstrap-select to work
// require berlin/bootstrap
// require berlin/custom

//= require lodash-4.17.15
// lodash used for google maps

<%
# ...
if Rails.env.development?
  require_asset ("vue-2.4.2")
else
  require_asset ("vue-2.4.2.prod")
end

require_asset ("vue-router-2.7.0")
require_asset ("firebase-4.0.0")
require_asset ("vuefire-1.4.3")
require_asset ("vue-google-maps")
require_asset ("vue-material-0.7.4")
# require_asset ("vue-social-sharing")
# require_asset ("squares-manager-0.0.1")
require_asset ("../shared/squares-container")
require_asset ("../shared/page-content")
require_asset ("../squares/tabable-section")
require_asset ("../shared/vue-squares")

%>


