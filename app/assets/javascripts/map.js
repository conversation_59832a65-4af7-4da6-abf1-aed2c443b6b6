mapboxgl.accessToken = 'pk.eyJ1IjoiYWNodXNlcjIwMCIsImEiOiJjbGI2cHNkZXcwM2poM3RsajNhOXlzYm5nIn0.HuTFJfcXAQAKBU35ltOhHg';

const startingLatLng = { lat: 52.5275364, lng: -1.4442473 }; //initial latlon passed to map on page load
const startingzoom = 18; //initial zoom level passed to map on page load
const heatmapzoomthreshold = 17; //zoom level variable to swap between heatmap and plotoutlines. Need to also change global variable called 'HEATMAPTHRESHOLD' in mapper.py
let currentZoom = startingzoom; // Initialize variable to be used later

const UserIP = "*************";
const UserID = "None"; 

const map = new mapboxgl.Map({
      container: 'map',
      style: 'mapbox://styles/mapbox/dark-v11', // satellite-streets-v11, light-v11
      center: startingLatLng, //[-0.17, 51.5], // starting position [lng, lat]
      zoom: startingzoom, // starting zoom
      minZoom: 6, // minimum zoom
      attributionControl: false
    });

    // Add zoom and rotation controls to the map.
    map.addControl(new mapboxgl.NavigationControl());
    // Add attribution control
    map.addControl(new mapboxgl.AttributionControl({
      customAttribution: ''
      }));

//colors for layer shading
const colors_avgprice = [
  'case',
  ['==', ['get', 'avm'], null], 'rgba(0, 0, 0, 0)', // Transparent color for null values
  ['interpolate', // If 'psqm' is not null, apply the interpolation
  ['linear'],
  ['get', 'avm'],
  50000, '#a24f93',
  100000, '#884fa2',
  150000, '#5e4fa2',
  200000, '#406eb6',
  250000, '#3288bd',
  300000, '#00a9bb',
  350000, '#66c2a5',
  400000, '#abdda4',
  450000, '#e6f598',
  500000, '#ffffbf',
  600000, '#fee08b',
  700000, '#fec873',
  800000, '#fdae61',
  900000, '#f46d43',
  1000000, '#d53e4f',
  1500000, '#ba2449',
  2000000, '#9e0142',
  4000000, '#850137'
  ]
];
const colors_psqm = [
  'case',
  ['==', ['get', 'psqm'], null], 'rgba(0, 0, 0, 0)', // Transparent color for null values
  ['interpolate', // If 'psqm' is not null, apply the interpolation
  ['linear'],
    ['get', 'psqm'],
    500, '#a24f93',
    1000, '#884fa2',
    1500, '#5e4fa2',
    2000, '#406eb6',
    2500, '#3288bd',
    3000, '#00a9bb',
    3500, '#66c2a5',
    4000, '#abdda4',
    4500, '#e6f598',
    5000, '#ffffbf',
    5500, '#fee08b',
    6000, '#fec873',
    6500, '#fdae61',
    7000, '#f46d43',
    8000, '#d53e4f',
    9000, '#ba2449',
    10000, '#9e0142',
    15000, '#850137'
  ]
];

//Sets display of HTML elements (legend and control panel options)...
//...according to zoom level and radio button selections
function SetVisibility() {
  // variables used by legend chooser on zoom //
  const Legendavgprice = document.getElementById('legend-avgprice');
  const LegendEPC = document.getElementById('legend-EPC');
  const Legendpsqm = document.getElementById('legend-psqm');
  //radio button selection state
  const avgPriceSelected = document.getElementById('toggle_avgprice') ? document.getElementById('toggle_avgprice').checked : true; // Assume avgPriceSelected is checked if we can't see the radio button
  const pricePerSqmSelected = document.getElementById('toggle_psqm') ? document.getElementById('toggle_psqm').checked : false; // Assume pricePerSqmSelected is not checked if we can't see the radio button
  const EPCSelected = document.getElementById('toggleEPC') ? document.getElementById('toggleEPC').checked : true; // Assume EPCSelected is checked if we can't see the radio button
  //which options to show user
  const heatmapSelectionDiv = document.getElementById('heatmap-selection');   // Get the heatmap selection div (in map options control panel)
  const pinSelectionDiv = document.getElementById('pin-selection');   // Get the pin selection div (in map options control panel)

  if (currentZoom > heatmapzoomthreshold) {
    Legendavgprice.style.display = 'none'; // Hide
    Legendpsqm.style.display = 'none'; // Hide
    heatmapSelectionDiv.style.display = 'none'; // Hide the heatmap selection div  (in map options control panel)
    pinSelectionDiv.style.display = 'block'; // Show the pin selection div (in map options control panel)
    if (EPCSelected) {
          LegendEPC.style.display = 'block'; // Show EPC legend
        } else {
          LegendEPC.style.display = 'none'; // Hide  EPC legend
        }
  } else {
    LegendEPC.style.display = 'none';
    heatmapSelectionDiv.style.display = 'block'; // Show the heatmap selection div  (in map options control panel)
    pinSelectionDiv.style.display = 'none'; // Hide the pin selection div (in map options control panel)
    if (pricePerSqmSelected) {
          Legendpsqm.style.display = 'block'; // Show the Price per SQM legend
          Legendavgprice.style.display = 'none'; // Hide the Average price legend
        } else {
          Legendavgprice.style.display = 'block'; // Show the Average price legend
          Legendpsqm.style.display = 'none'; // Hide the Price per SQM legend
        }

  }
}

//We swap to satellite at higher zoom by adding another layer for satellite on high zoom
//reducing raster tilesize to 256 will save costs
function addSatLayer() {
  map.addLayer({
    id: 'satellite',
    source: {"type": "raster",  "url": "mapbox://mapbox.satellite", "tileSize": 512},
    type: "raster",
    'minzoom': heatmapzoomthreshold
  });
}


//FUNCTIONS TO ADD MAP SOURCES AND LAYERS
/*needed as functions so we can call its source/addLayer on zoom or control panel change.
Unlike a mapping library like Leaftlet, Mapbox GL JS doesn't have a concept of "basemap" vs "other layers."
All layers are part of the same entity: the style. So you need to keep some state of the data layer around and call its source/addLayer on each change.*/

//EPCs
function addEPCSource() {
  map.addSource('EPCsource', {
    type: "vector",
    'tiles': ['https://housemetric.co.uk/pins/{z}/{x}/{y}'],
    'minzoom': heatmapzoomthreshold //Never attempt to fetch any tile when zoom is wider/lower value than this.
  });
}
function addEPCLayer() {
map.addLayer({
  "id": "EPClayer",
  "source": "EPCsource",
  "source-layer": "default",
  "type": "circle",
'minzoom': heatmapzoomthreshold, //Never display this layer at zooms less than (heatmapzoomthreshold)
'paint': {
'circle-radius': 7,
'circle-opacity': 0,
'circle-stroke-width': 3,
//'circle-color': 'blue',
//https://gka.github.io/palettes/#/9|s|e60079,ffffff|ffffe0,ff005e,93003a|1|0
'circle-stroke-color': [
      'interpolate',
      ['linear'],
      ["to-number",['get','datediff']],
      150,'#e60079',
      300,'#ec409a',
      600,'#f065ae',
      1200,'#f382bd',
      2400,'#f59dcc',
      4800,'#f8b7d9',
      9600,'#facfe6',
      15000,'#fde7f3',
      20000,'#ffffff'
    ]
}
});
}

//planning applications
function addPlanSource() {
    map.addSource('planappsource', {
        type: 'geojson',
        data: '/planapps', //Uses the GeoJSON Endpoint for planning applications
        cluster: true,
        clusterMaxZoom: 19, // Max zoom to cluster points, 
        clusterRadius: 50 // Radius of each cluster when clustering points
    });
}
function addPlanLayer() {
    // Add clusters layer
    map.addLayer({
        id: 'clusters',
        type: 'circle',
        source: 'planappsource',
        filter: ['has', 'point_count'],
        'minzoom': heatmapzoomthreshold, //Never display this layer at zooms less than heatmapzoomthreshold
        paint: {
            'circle-color': [
                'step',
                ['get', 'point_count'],
                '#51bbd6',
                100,
                '#f1f075',
                750,
                '#f28cb1'
            ],
            'circle-radius': [
                'step',
                ['get', 'point_count'],
                20,
                100,
                30,
                750,
                40
            ]
        }
    });
    // Add cluster count labels
    map.addLayer({
        id: 'cluster-count',
        type: 'symbol',
        source: 'planappsource',
        filter: ['has', 'point_count'],
        'minzoom': heatmapzoomthreshold, //Never display this layer at zooms less than heatmapzoomthreshold
        layout: {
            'text-field': '{point_count_abbreviated}',
            'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
            'text-size': 12
        }
    });
    // Add unclustered point layer
    map.addLayer({
        id: 'unclustered-point',
        type: 'circle',
        source: 'planappsource',
        filter: ['!', ['has', 'point_count']],
        'minzoom': heatmapzoomthreshold, //Never display this layer at zooms less than heatmapzoomthreshold
        paint: {
            'circle-color': '#11b4da',
            'circle-radius': 7,
            'circle-opacity': 0,
            'circle-stroke-width': 3,
            'circle-stroke-color': '#11b4da'
        }
    });
}



//plot outlines
function addPlotoutlineLayer() {
map.addLayer({
  "id": "plotoutlines",
  "source": "inspiremvt",
  "source-layer": "default",
  "type": "line",
  'minzoom': heatmapzoomthreshold, //Never display this layer at zooms less than heatmapzoomthreshold
  "paint": {
    'line-color': colors_avgprice,
    
    'line-width': [
      'case',
      ['==', ['get','inspireid'], 43379994], 5, // Thicker line for this property
      1 // Default line width
    ]
    
  }
});
}

function addMVTSource() {
  map.addSource('inspiremvt', {
    'type': 'vector',
    'tiles': ['https://housemetric.co.uk/tiles/{z}/{x}/{y}'],
    //'minzoom': 8, // hide layer when zoom is wider/lower value than this.
    'maxzoom': heatmapzoomthreshold //Never attempt to fetch any tile with zoom greater than heatmapzoomthreshold
    //If a tile is needed at, say, zoom 13, then fetch the equivalent tile at zoom 12, and overzoom it instead.
  });
}
function addMVTLayer() {
  //keep base layer labels as top layer
  const layers = map.getStyle().layers;
  // Find the index of the first symbol layer in the map style.
  let firstSymbolId;
  for (const layer of layers) {
  if (layer.type === 'symbol') {
  firstSymbolId = layer.id;
  break;}}
  map.addLayer({
    "id": "inspiremvt",
    "source": "inspiremvt",
    "source-layer": "default",
    // "type": "line",
    "type": "fill",
    "maxzoom": heatmapzoomthreshold,
    "paint": {
      'fill-color': colors_avgprice,
      'fill-opacity': [
        'interpolate',
        ['linear'],
        ['zoom'],//zoom level
        14,1,
        18,0
        ],
    }
  },firstSymbolId // Insert the layer beneath the first symbol layer.
  );
}

//Function to fetch the GeoJSON data for the 'Planlayer'.  i.e. adds the source and the 3 plan layers
function fetchAndAddPlanLayer() {
    var bounds = map.getBounds();
    var bbox = [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()];
    const zoomLVL = map.getZoom();

    fetch(`/planapps?bbox=${bbox.join(',')}&zoom=${zoomLVL}`)
        .then(response => response.json())
        .then(data => {
            map.addSource('planappsource', {
                type: 'geojson',
                data: data,
                cluster: true,
                clusterMaxZoom: 19, // Max zoom to cluster points, 
                clusterRadius: 50 // Radius of each cluster when clustering points
            });
            addPlanLayer();
        });
}

//convert abbreviations to full description
function getAppTypeDescription(appTypeCode) {
  const appTypeDescriptions = {
    'ad': 'Advertising and signs',
    'am': 'Amendments',
    'cd': 'Discharge of conditions',
    'fl': 'Householder application',
    'ht': 'Heritage',
    'ot': 'Outline application',
    'oh': 'Other',
    'te': 'Telecoms',
    'tr': 'Tree work'
  };
  return appTypeDescriptions[appTypeCode] || 'Unknown type';
}
//convert abbreviations to full description
function getAppStateDescription(appStateCode) {
  const appStateDescriptions = {
    'cd': 'Approved with conditions',
    'pm': 'Approved',
    'rf': 'Referred',
    'rj': 'Rejected',
    'ud': 'Undecided',
    'ur': 'Unresolved',
    'wd': 'Withdrawn'
  };  
  return appStateDescriptions[appStateCode] || 'Not known';
}

//Function to log map interactions
var logMapInteractionUrl = "/log/map_interaction";
function logEvent(logData) {
    // Send the data to the server using Fetch API
    fetch(logMapInteractionUrl, {
      method: 'POST',
      headers: {
          'Content-Type': 'application/json',
      },
      body: JSON.stringify(logData),
  })
  //.then(response => response.json())
  // .then(data => console.log(data)) for debugging
  .catch(error => console.error('Error:', error));
}


//—————————————————————————————————— MAP LOAD ——————————————————————————————————
map.on('style.load', function () {
  addMVTSource();
  addMVTLayer();
  addSatLayer(); //only appears at heatmapzoomthreshold or more
  addPlotoutlineLayer(); 
  addEPCSource(); // don't need to worry about these loading for a zoomed out map because the tiles don't get called for wide zooms.
  addEPCLayer();  

  SetVisibility(); // Set the initial visibility of map options control panel and legend divs

// Change text color to make town names more readable 
// see debugging code in footer to log layer IDs to the console
map.setPaintProperty('settlement-subdivision-label', 'text-color', '#FFFFFF');
map.setPaintProperty('settlement-minor-label', 'text-color', '#FFFFFF');
map.setPaintProperty('settlement-major-label', 'text-color', '#FFFFFF');

//end of map on load function
});


//—————————————————————————————————— MAP CLICKS ——————————————————————————————————

//PLANNING APPLICATIONS
//  Inspect cluster
/*  If the zoom level is less than 18, the map will zoom in towards the cluster. 
If the zoom level is 18 or higher, it will display the children of the cluster in a popup. */
map.on('click', 'clusters', (e) => {
  const features = map.queryRenderedFeatures(e.point, {
      layers: ['clusters']
  });
  const clusterId = features[0].properties.cluster_id;
  const clusterSource = map.getSource('planappsource'); // Make sure 'planappsource' matches your source ID

  currentZoom = map.getZoom(); // update the current zoom level

  if (currentZoom < 18) {
      // If zoom level is less than 18, zoom in
      clusterSource.getClusterExpansionZoom(clusterId, (err, zoom) => {
          if (err) return;

          map.easeTo({
              center: features[0].geometry.coordinates,
              zoom: zoom
          });
      });
  } else {
      // If zoom level is 18 or higher, show the children in a popup
      clusterSource.getClusterLeaves(clusterId, 50, 0, (err, aFeatures) => { // Adjust the limit (50) as needed
          if (err) return console.error('Error getting cluster leaves:', err);

          // Sort the features by date_summary
          aFeatures.sort((a, b) => {
              return b.properties.date_summary.localeCompare(a.properties.date_summary);
          });

          // Create content for a popup showing properties of all features in the cluster
          let popupContent = '<div class="popup-content"><strong>Planning applications:</strong>';
          aFeatures.forEach((feature, index) => {
            const apptype = getAppTypeDescription(feature.properties.app_type);
            const appstate = getAppStateDescription(feature.properties.app_state);
            popupContent += `<hr><div><b>${feature.properties.date_summary}</b></br><i>${feature.properties.address_summary}</i></br><u>${apptype} - ${appstate}</u></br>${feature.properties.description}</br>ref: ${feature.properties.uid}</div>`;
          });
          popupContent += '</div>';
          new mapboxgl.Popup()
              .setLngLat(e.lngLat)
              .setHTML(popupContent)
              .addTo(map);
              // Log Infowindow click
              const logData = {
                  route: '/map-infowindow-planning',
                  searchstring: 'cluster',
                  userip: UserIP,
                  userid: UserID
              };
              logEvent(logData);
      });
  }
});
// INSPECT unclustered-point
map.on('click', 'unclustered-point', (e) => {
    // Access the first feature from the event's features array
    const feature = e.features[0];
    const apptype = getAppTypeDescription(feature.properties.app_type);
    const appstate = getAppStateDescription(feature.properties.app_state);
    const uid = feature.properties.uid;
    let popupContent = '<div class="popup-content">';
      popupContent += `<div><b>${feature.properties.date_summary}</b></br><i>${feature.properties.address_summary}</i></br><u>${apptype} - ${appstate}</u></br>${feature.properties.description}</br>Planning ref: ${uid}</div>`;
    popupContent += '</div>';
    new mapboxgl.Popup()
      .setLngLat(e.lngLat)
      .setHTML(popupContent)
      .addTo(map);
    // Log Infowindow click
    const logData = {
        route: '/map-infowindow-planning',
        searchstring: uid,
        userip: UserIP,
        userid: UserID
    };
    logEvent(logData);
});

//INFO WINDOW for EPC points
/* When a click event occurs on a feature in the places layer, open a popup at the
location of the feature, with description HTML from its properties.*/
map.on('click', 'EPClayer', (e) => {
    // Copy coordinates array.
    const coordinates = e.features[0].geometry.coordinates.slice();
    //Info window values
    let UPRN = e.features[0].properties.uprn;
    let idlrtrans4window = e.features[0].properties.idlrtrans;
    let isfreehold = e.features[0].properties.isfreehold;
    let hasimg = e.features[0].properties.hasimg;
    let imgText = hasimg ? "photos and " : "";
    //let saon;
    //saon=isfreehold ? (saon = e.features[0].properties.saon) : (saon = ''); //javascript shorthand for if-else 
    let saon = e.features[0].properties.saon
    let paon = e.features[0].properties.paon;
    let street = e.features[0].properties.street;
    street = street.replace("Road","Rd").replace("Street","St").replace("Avenue","Av")
    let postcode = e.features[0].properties.pdistrict + ' ' + e.features[0].properties.outcode;
    let addresslnk = '/' + idlrtrans4window + '/' + postcode.replace(" ", "-") + '/' + 'fromMap'
    let price = e.features[0].properties.price;
    let pricestr = price.toLocaleString('en-US');
    let dated = e.features[0].properties.dated;
    let sqm = e.features[0].properties.sqm;
    let ppsqm = (price && sqm) ? Math.trunc(parseInt(price) / parseInt(sqm)).toLocaleString('en-US') : null;
    let inspectiondate = e.features[0].properties.inspectiondate;
    if (typeof UPRN == "undefined") UPRN = e.features[0].properties.uprn;
    //variables below removed from sql query
    //let transactiontype = e.features[0].properties.transactiontype; 
    //let tenure = "string";
    //if (e.features[0].properties.tenure == null) {tenure = "Unknown";}
    //else {tenure = e.features[0].properties.tenure.toString().replace("1","Owner occupied").replace("2","Private rental").replace("3","Social rental").replace("4","New build").replace("5","Unknown");}
    //let ratingcurrent = e.features[0].properties.ratingcurrent;

    // Set HTML for info window pop up
    let infowind;
    // To check for a 'falsy' string value, if (saon) {// saon was empty string, false, 0, null, undefined, ...}, A falsy value is something which evaluates to FALSE, this is needed to handle Central London leaseholds for the entire building.
    if (isfreehold==false && (saon)) {  // If the property is not a freehold and the saon (sale date) is not empty
      infowind = "<h6>"+paon+', '+street+"</h6>" +
        "<p>Contains multiple flats. Most recent sale in this block was "+saon+" which sold on "+dated+" for £"+pricestr+".</p>"+
        "<p>Floor area on EPC was " + sqm + " sqm, giving a price per sqm of £" + ppsqm + ".</p>" +
        "<p align='right'><a href=\"" +addresslnk+ "\">more details</a></p>"
    }
    else {
      infowind = "<h6>"+saon+' '+paon+', '+street+"</h6>" +
      "<table><tr><td>Last sold:</td><td>" + dated + "</td></tr>"+
      "<tr><td>Price paid:</td><td>£" + pricestr + "</td></tr>"+
      // "<tr><td>Inspected:</td><td>" + inspectiondate + "</td></tr>" +
      "<tr><td>Floor area:</td><td>" + (sqm ? sqm + " sqm" : "") + "</td></tr>" +
      "<tr><td>£ per sqm:</td><td>" + (ppsqm ? "£" + ppsqm : "") + "</td></tr>" +
      "</table><p class='small float-end mt-3 mb-0'><a href=\"" +addresslnk+ "\">" + imgText + "more details</a></p>"
    }
              
    /* Ensure that if the map is zoomed out such that multiple
    copies of the feature are visible, the popup appears
    over the copy being pointed to.*/
    while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
    coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
    }

    new mapboxgl.Popup()
    .setLngLat(coordinates)
    .setHTML(infowind)
    .addTo(map);

    // Log Infowindow click
    const logData = {
        route: '/map-infowindow-epc',
        searchstring: addresslnk,
        userip: UserIP,
        userid: UserID
    };
    logEvent(logData);

});


//—————————————————————————————————— MOUSEOVERS ——————————————————————————————————
// Change the cursor to a pointer when the mouse is over the places layer.
map.on('mouseenter', 'EPClayer', () => {
map.getCanvas().style.cursor = 'pointer';
});

// Change it back to a pointer when it leaves.
map.on('mouseleave', 'EPClayer', () => {
map.getCanvas().style.cursor = '';
});

//—————————————————————————————————— MAP MOVE ——————————————————————————————————
//On Map Move: refresh Planning data
/*EPC pins update automatically on map move because it is a vector source.*/
map.on('moveend', function() {
    // Check if the 'togglePlans' radio button is selected and zoom is >= heatmapzoomthreshold
    var togglePlansElement = document.getElementById('togglePlans');
    if (togglePlansElement && togglePlansElement.checked && currentZoom >= heatmapzoomthreshold) {
        // Assuming you have a function to construct the URL based on the current map bounds
        var bounds = map.getBounds();
        var bbox = [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()];
        const zoomLVL = map.getZoom();

        fetch(`/planapps?bbox=${bbox.join(',')}&zoom=${zoomLVL}`)
            .then(response => response.json())
            .then(data => {
                // Assuming 'planappsource' is the ID of your GeoJSON source
                map.getSource('planappsource').setData(data);
            });
    }
});




//—————————————————————————————————— MAP ZOOM ——————————————————————————————————
/* checks if zoom level has crossed threshold in either direction
and runs SetVisibility() if it has */
map.on('zoom', () => {
  const newZoom = map.getZoom(); // Get the new zoom level
  // Check if crossing the threshold in either direction
  if ((currentZoom <= heatmapzoomthreshold && newZoom > heatmapzoomthreshold) || 
      (currentZoom > heatmapzoomthreshold && newZoom <= heatmapzoomthreshold)) {
    currentZoom = newZoom;
    SetVisibility(); // Run SetVisibility only when crossing the threshold
  }
  currentZoom = newZoom; // Update currentZoom variable

  // update Zoom level display in map options control panel
  document.getElementById('zoomlevel').innerHTML = "The current zoom level is: " + currentZoom.toFixed(1);
});

//—————————————————————————————————— RADIO BUTTON ——————————————————————————————————
//Radio button listener
document.addEventListener('change', function(e) {
  // Event listener for the name="layerOption" radio buttons
  if (e.target.name === 'layerOption') {
    // Check which radio button was selected
    if (e.target.id === 'toggle_avgprice') {
      // Logic for when 'Average price' is selected
      map.setPaintProperty('inspiremvt', 'fill-color', colors_avgprice);
      //map.setPaintProperty('plotoutlines', 'line-color', colorInterpolation1);

    } else if (e.target.id === 'toggle_psqm') {
      // Logic for when 'Price per SQM' is selected
      map.setPaintProperty('inspiremvt', 'fill-color', colors_psqm);
      //map.setPaintProperty('plotoutlines', 'line-color', colorInterpolation2);
    }
  }
  // Event listener for the name="pinOption" radio buttons
  if (e.target.name === 'pinOption') {
    // Check which radio button was selected
    if (e.target.id === 'toggleEPC') {
      // Logic for when 'toggleEPC' is selected
      // Remove the Planning applications layers from the map
      if (map.getLayer('clusters')) {
        map.removeLayer('clusters');
      }
      if (map.getLayer('cluster-count')) {
        map.removeLayer('cluster-count');
      }
      if (map.getLayer('unclustered-point')) {
        map.removeLayer('unclustered-point');
      }
      if (map.getSource('planappsource')) {
        map.removeSource('planappsource');
      }
      // Add the EPC layer and its source if they don't exist
      if (!map.getLayer('EPClayer')) {
        addEPCSource(); // Assuming this function adds the source for EPCs
        addEPCLayer(); // Assuming this function adds the layer for EPCs
      }
    }
    else if (e.target.id === 'togglePlans') {
      // Logic for when 'togglePlans' is selected
      // Remove the EPC layer and its source if they exist
      if (map.getLayer('EPClayer')) {
        map.removeLayer('EPClayer');
      }
      if (map.getSource('EPCsource')) {
        map.removeSource('EPCsource');
      }
      // Add the set of 3Plan layers (unclustered-point,cluster-count,clusters) and the associated source "planappsource" 
      fetchAndAddPlanLayer(); // this function adds source and layers for planning applications
    }
  }
  //Reset visibility of legend and cpanel options depending on selection
  SetVisibility(); 
});


//—————————————————————————————————— DEBUGGING ——————————————————————————————————
//for debugging:
/*This code will let us click on the map and see the values embedded within the MVT in Developer tools console log*/
map.on('click', (e) => {
  map.queryRenderedFeatures(e.point).forEach((feature) => {
    console.log(feature.properties); // Logs properties of the feature under the mouse cursor
  });
});


// Map info window when zoomed out below 13
map.on('click', (e) => {
  const currentZoom = map.getZoom();
  console.log('Current zoom:', currentZoom);
  console.log('Heatmap threshold:', 13);
  
  if (currentZoom < 13) {
    console.log('Zoom is below threshold, querying features');
    const features = map.queryRenderedFeatures(e.point, { layers: ['inspiremvt'] });
    
    if (features.length > 0) {
      console.log('Features found:', features.length);
      const feature = features[0];
      const props = feature.properties;
      
      // Create popup content with custom labels and rounded values
      let popupContent = '<div class="popup-content"><h6>Cluster average</h6>';
      if ('avm' in props) {
        const avmValue = Math.round(props.avm / 1000); // Round to nearest thousand
        const formattedAvm = avmValue >= 1000 
          ? `£${(avmValue / 1000).toLocaleString(undefined, {maximumFractionDigits: 1})}m`
          : `£${avmValue}k`;
        popupContent += `<strong>Current value:</strong> ${formattedAvm}<br>`;
      }
      if ('psqm' in props) {
        const psqmValue = Math.round(props.psqm / 100) * 100; // Round to nearest hundred
        popupContent += `<strong>£/sqm:</strong> £${psqmValue.toLocaleString()}<br>`;
      }
      popupContent += '</div>';

      // Create and add popup to map
      new mapboxgl.Popup({
        closeButton: false, // Set this to false to hide the close button, or true to show it
      })
        .setLngLat(e.lngLat)
        .setHTML(popupContent)
        .addTo(map);
      console.log('Popup added to map');
    } else {
      console.log('No features found at click point');
    }
  } else {
    console.log('Zoom is above or equal to threshold, no popup created');
  }
});


//for debugging:
//logs all layer IDs to the console
// map.on('load', function() {
//     const layers = map.getStyle().layers;
//     layers.forEach(layer => {
//         console.log(layer.id); // Log the ID of each layer
//     });
// });
