require_dependency 'pwb/application_controller'

module Pwb
  class MarketingController < ActionController::Base # ApplicationController
    # before_action :header_image_url

    def index
      agency_tenant_name = request.subdomain.presence || 'www'
      # adding below to handle case where subdomain is like "agency.be.propertywebbuilder.com"
      # Essentially I only want the first part of the subdomain
      agency_tenant_name = agency_tenant_name.split('.')[0]
      puts "agency_tenant_name is: #{agency_tenant_name}"
      if agency_tenant_name == 'www'
        # render 'warrior_index', layout: 'marketing/warrior_layout' # json: {}
        # above was previously at https://warrior-exceptions-75267.netlify.app/
        render 'tailwind_index', layout: 'marketing/tailwind_layout' # json: {}
        # render '60sec_index', layout: 'marketing/60sec_tailwind_layout' # json: {}
      elsif agency_tenant_name == 'warrior'
        render 'warrior_index', layout: 'marketing/warrior_layout' # json: {}
        # above was previously at https://warrior-exceptions-75267.netlify.app/
      elsif agency_tenant_name == '60sec'
        render '60sec_index', layout: 'marketing/60sec_tailwind_layout' # json: {}
      else
        render 'tenants/tenant_home_index', layout: 'tenant_home_layout' # json: {}
      end
    end
  end
end
