module Pwb
  class ApplicationApiController < ActionController::Base
    protect_from_forgery with: :exception, prepend: true
    # include ActionController::HttpAuthentication::Token::ControllerMethods

    before_action :set_current_tenant
    # before_action :authenticate_user!, :current_agency, :check_user
    # , :authenticate_user_from_token!, :set_locale
    # after_action :set_csrf_token

    def self.default_url_options
      { locale: I18n.locale }
    end

    private

    def set_current_tenant
      agency_tenant_name = request.subdomain.presence || 'default'
      # adding below to handle case where subdomain is like "agency.be.propertywebbuilder.com"
      # Essentially I only want the first part of the subdomain
      agency_tenant_name = agency_tenant_name.split('.')[0]
      # You might want to adjust this logic based on your specific requirements
      # e.g., subdomain-based tenancy or request parameters
      # current_tenant = Agency.find_by!(uuid: request.env['HTTP_X_AGENCY_UUID'])
      # set_current_tenant(current_tenant)
      agency_tenant = AgencyTenant.find_by(
        {
          subdomain: agency_tenant_name,
          domain: 'propertywebbuilder.com'
        }
      )
      ActsAsTenant.current_tenant = agency_tenant
    end

    def render_json_error(message, opts = {})
      render json: message, status: opts[:status] || 422
    end
    # def check_user
    #   unless current_user && current_user.admin
    #     # unless request.subdomain.present? && (request.subdomain.downcase == current_user.tenants.first.subdomain.downcase)
    #     render_json_error "unauthorised_user"
    #   end
    # end

    # def current_agency
    #   @current_agency ||= (Agency.last || Agency.create)
    # end

    # def set_csrf_token
    #   # http://rajatsingla.in/ruby/2016/08/06/how-to-add-csrf-in-ember-app.html
    #   if request.xhr?
    #     response.headers["X-CSRF-Token"] = form_authenticity_token.to_s
    #     response.headers["X-CSRF-Param"] = "authenticity_token"
    #   end
    #   # works in conjunction with updating the headers via client app
    # end
  end
end
