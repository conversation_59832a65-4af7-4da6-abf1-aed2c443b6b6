module Pwb
  # class Api::V1::LitePropertiesController < JSONAPI::ResourceController
  #   # skip_before_action :verify_content_type_header
  #   # Skipping action below allows me to browse to endpoint
  #   # without having set mime type
  #   # skip_before_action :ensure_valid_accept_media_type
  #   # skip_before_action :verify_accept_header
  # end

  class Api::V1::LitePropertiesController < ApplicationController
    # Skipping actions to allow browsing to endpoint without setting mime type
    skip_before_action :verify_authenticity_token
    # skip_before_action :verify_content_type_header, if: -> { request.format.json? }
    # skip_before_action :ensure_valid_accept_media_type, if: -> { request.format.json? }
    # skip_before_action :verify_accept_header, if: -> { request.format.json? }

    # Define actions as needed, for example:
    def index
      properties = Pwb::Prop.all
      render json: properties
    end

    def show
      property = Pwb::Prop.find(params[:id])
      render json: property
    end

    def create
      property = Pwb::Prop.new(property_params)
      if property.save
        render json: property, status: :created
      else
        render json: property.errors, status: :unprocessable_entity
      end
    end

    def update
      property = Pwb::Prop.find(params[:id])
      if property.update(property_params)
        render json: property
      else
        render json: property.errors, status: :unprocessable_entity
      end
    end

    def destroy
      property = Pwb::Prop.find(params[:id])
      property.destroy
      head :no_content
    end

    private

    def property_params
      params.require(:property).permit(:attribute1, :attribute2, :attribute3) # Replace with actual attributes
    end
  end
end
