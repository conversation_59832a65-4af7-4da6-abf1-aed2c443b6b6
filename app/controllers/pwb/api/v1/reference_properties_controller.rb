module Pwb
  class Api::V1::ReferencePropertiesController < ApplicationApiController
    skip_before_action :verify_authenticity_token
    respond_to :json

    def show
      @reference_property = GenericProperty.find_by_uuid(params[:reference_property_uuid])
      if @reference_property.nil?
        render json: { error: 'Listing not found' }, status: :not_found
      else
        render 'pwb/api/v1/reference_properties/show'
        # 15 nov 2024:
        # above started off as copy of _reference_property])mgmt_details.json
        # Now I want it to be as close to app/views/pwb/api/v1/purchase_evaluations/show.json
        # for the front end
      end
      # file_path = Rails.root.join('app/views/', 'pwb/api/v1/purchase_evaluations/show.json')
      # render json: File.read(file_path), status: :ok
    end

    def list
      # @sale_listings = SaleListing.kept.order('created_at desc')
      @reference_properties = GenericProperty.order('created_at desc')

      render 'pwb/api/v1/reference_properties/list'
    end
  end
end
