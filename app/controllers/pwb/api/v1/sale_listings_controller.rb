module Pwb
  class Api::V1::SaleListingsController < ApplicationApiController
    skip_before_action :verify_authenticity_token
    respond_to :json

    def show
      # ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
      # ActsAsTenant.without_tenant do
      #   # ActsAsTenant.current_tenant = @sale_listing.agency_tenant if @sale_listing
      # end
      @sale_listing = SaleListing.find_by_uuid(params[:sale_listing_uuid])
      if @sale_listing.nil?
        render json: { error: 'Listing not found' }, status: :not_found
      else
        render 'pwb/api/v1/sale_listings/show'
        # 15 nov 2024:
        # above started off as copy of _sale_listing_mgmt_details.json
        # Now I want it to be as close to app/views/pwb/api/v1/purchase_evaluations/show.json
        # for the front end
      end
      # file_path = Rails.root.join('app/views/', 'pwb/api/v1/purchase_evaluations/show.json')
      # render json: File.read(file_path), status: :ok
    end

    def list
      @sale_listings = SaleListing.kept.order('created_at desc')

      # ActsAsTenant.without_tenant do
      #   @sale_listings = SaleListing.kept.order('created_at desc')
      # end
      # return render 'api_mgmt/v4/sale_listings/list'
      render 'pwb/api/v1/sale_listings/list'
    end
  end
end
