module Pwb
  class Api::V1::SaleListingsPopulatorController < ApplicationApiController
    skip_before_action :verify_authenticity_token
    respond_to :json

    # def from_saved_data
    #   saved_slug = params[:saved_slug] || 'luxury-villa-with-ocean-views'
    #   saved_filename = "sale_listing_#{saved_slug}.json"
    #   source_file = File.join(
    #     Rails.root, 'db', 'exports', 'ai_faked_listings',
    #     saved_filename
    #   )
    #   @sale_listing = ExportersAndLoaders::SaleListingsLoader.new(
    #     source_file
    #   ).load_sale_listings(tenant_to_use = ActsAsTenant.current_tenant)
    #   render 'pwb/api/v1/sale_listings/show'
    # end

    def from_saved_data
      saved_slug = params[:saved_slug] || 'luxury-villa-with-ocean-views'

      # Check if the sale listing already exists
      existing_listing = SaleListing.find_by(reference: saved_slug)
      if existing_listing
        @sale_listing = existing_listing
        render 'pwb/api/v1/sale_listings/show' and return
      end
      saved_filename = "sale_listing_#{saved_slug}.json"
      source_file = File.join(
        Rails.root, 'db', 'exports', 'ai_faked_listings',
        saved_filename
      )

      unless File.exist?(source_file)
        render json: { error: "File not found: #{saved_filename}" }, status: :not_found and return
      end

      begin
        @sale_listing = ExportersAndLoaders::SaleListingsLoader.new(
          source_file
        ).load_sale_listings(tenant_to_use = ActsAsTenant.current_tenant)
        render 'pwb/api/v1/sale_listings/show'
      rescue JSON::ParserError => e
        Rails.logger.error("JSON Parsing Error for file #{saved_filename}: #{e.message}")
        render json: { error: "Failed to parse the file: #{saved_filename}" }, status: :unprocessable_entity
      rescue StandardError => e
        Rails.logger.error("Error loading sale listing from file #{saved_filename}: #{e.message}")
        render json: { error: "An error occurred while processing the file: #{saved_filename}" },
               status: :internal_server_error
      end
    end

    def from_bvh_ref
      bvh_ref = params[:bvh_ref]
      retrieval_end_point = "https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/#{bvh_ref}"

      end_point_uri = URI(URI::Parser.new.escape(retrieval_end_point))

      begin
        page_to_parse = end_point_uri.open(redirect: false)
        harvested_hash = JSON.parse(page_to_parse.read)

        scrape_content_parser = RealtyParsers::ParseResalesResults.new
        standardised_hash = scrape_content_parser.get_property_hash_from_detailed_json(
          detailed_resales_hash: harvested_hash['property']
        )

        request_host = request.host || 'from_bvh_ref_endpoint'
        creator = Creators::Full::FullListingAndAssetCreator.new

        @sale_listing = creator.create_from_resales_hash(
          standardised_hash,
          request_host
        )
        puts "sale_listing: #{@sale_listing.inspect}"
        render 'pwb/api/v1/sale_listings/show'
      rescue OpenURI::HTTPError => e
        if e.io.status[0] == '404'
          render json: { error: 'BVH reference not found' }, status: :not_found
        else
          render json: { error: 'An error occurred' }, status: :internal_server_error
        end
      end
    end
  end
end
