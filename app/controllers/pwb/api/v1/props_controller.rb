require_dependency 'pwb/application_controller'

module Pwb
  class Api::V1::PropsController < ApplicationApiController
    # protect_from_forgery with: :null_session

    def index
      @props = Prop.all
      render json: @props
    end

    def show
      @prop = Prop.find(params[:id])
      if @prop
        render json: {
          prop: @prop
          # primary_address: @prop.primary_address
        }
      else
        render json: {
          prop: {},
          primary_address: {}
        }
      end
    end

    def update
      @prop = Prop.find(params[:id])
      if @prop
        @prop.update(prop_params)
        @prop.social_media = params[:prop][:social_media]
        @prop.save!
      end
      render json: @prop
    end

    def update_master_address
      @prop = Prop.find(params[:id])
      if @prop.primary_address
        @prop.primary_address.update(address_params)
        @prop.primary_address.save!
      else
        primary_address = Address.create(address_params)
        @prop.primary_address_id = primary_address.id
        @prop.save!
      end
      render json: @prop.primary_address
    end

    private

    def address_params
      params.require(:address).permit(
        :street_address, :street_number,
        :postal_code, :city,
        :region, :country,
        :longitude, :latitude
      )
    end

    def prop_params
      params.require(:prop).permit(
        :title, :description,
        :price, :area,
        :email_primary,
        :phone_number_primary, :phone_number_other,
        :theme_name, :default_currency,
        supported_locales: []
      )
    end
  end
end
