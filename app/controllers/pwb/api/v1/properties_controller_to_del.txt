module Pwb
  class Api::V1::PropertiesControllerToDel < JSONAPI::ResourceController
    # Skipping action below allows me to browse to endpoint
    # without having set mime type
    # skip_before_action :ensure_valid_accept_media_type

    # def set_default_currency
    #   @model
    # end

    def bulk_create
      propertiesJSON = params['propertiesJSON']
      propertiesJSON = JSON.parse propertiesJSON unless propertiesJSON.is_a? Array
      new_props = []
      existing_props = []
      errors = []
      properties_params(propertiesJSON).each_with_index do |property_params, index|
        propertyJSON = propertiesJSON[index]
        if Pwb::Prop.where(reference: propertyJSON['reference']).exists?
          existing_props.push Pwb::Prop.find_by_reference propertyJSON['reference']
          # propertyJSON
        else
          begin
            new_prop = Pwb::Prop.create(property_params)
            # new_prop = Pwb::Prop.create(propertyJSON.except("features", "property_photos", "image_urls", "last_retrieved_at"))

            # create will use website defaults for currency and area_unit
            # need to override that
            if propertyJSON['currency']
              new_prop.currency = propertyJSON['currency']
              new_prop.save!
            end
            if propertyJSON['area_unit']
              new_prop.area_unit = propertyJSON['area_unit']
              new_prop.save!
            end

            # TODO: - go over supported locales and save title and description
            # into them

            # if propertyJSON["features"]
            # TODO - process feature (currently not retrieved by PWS so not important)
            #   new_prop.set_features=propertyJSON["features"]
            # end
            if propertyJSON['property_photos']
              # uploading images can slow things down so worth setting a limit
              max_photos_to_process = 20
              # TODO: - retrieve above as a param
              propertyJSON['property_photos'].each_with_index do |property_photo, index|
                return if index > max_photos_to_process

                photo = PropPhoto.create
                photo.sort_order = property_photo['sort_order'] || nil
                photo.remote_image_url = property_photo['url']
                # photo.remote_image_url = property_photo["image"]["url"] || property_photo["url"]
                photo.save!
                new_prop.prop_photos.push photo
              end
            end

            new_props.push new_prop
          rescue StandardError => e
            errors.push e.message
            # logger.error err.message
          end
        end
      end

      render json: {
        new_props:,
        existing_props:,
        errors:
      }
    end

    # TODO: rename to update_features:
    def update_extras
      property = Prop.find(params[:id])
      # The set_features method goes through ea
      property.set_features = params[:extras].to_unsafe_hash
      property.save!
      render json: property.features
    end

    def order_photos
      ordered_photo_ids = params[:ordered_photo_ids]
      ordered_array = ordered_photo_ids.split(',')
      ordered_array.each.with_index(1) do |photo_id, index|
        photo = PropPhoto.find(photo_id)
        photo.sort_order = index
        photo.save!
      end
      @property = Prop.find(params[:prop_id])
      render json: @property.prop_photos
      # { "success": true }, status: :ok, head: :no_content
    end

    def add_photo_from_url
      # subdomain = request.subdomain || ""

      property = Prop.find(params[:id])
      remote_urls = params[:remote_urls].split(',')
      photos_array = []
      remote_urls.each do |remote_url|
        photo = PropPhoto.create
        # photo.subdomain = subdomain
        # photo.folder = current_tenant_model.whitelabel_country_code
        # photo.tenant_id = current_tenant_model.id
        # need the regex below to remove leading and trailing quotationmarks
        # photo.remote_image_url = remote_url.gsub!(/\A"|"\Z/, '')
        photo.remote_image_url = remote_url
        photo.save!
        property.prop_photos.push photo
        photos_array.push photo
      end
      # if json below is not valid, success callback on client will fail
      render json: photos_array.to_json
      # { "success": true }, status: :ok, head: :no_content
    end

    def add_photo
      # subdomain = request.subdomain || ""

      property = Prop.find(params[:id])
      files_array = params[:file]
      if files_array.class.to_s == 'ActionDispatch::Http::UploadedFile'
        # In case a single file has been sent, use as an array item
        files_array = [files_array]
      end
      photos_array = []
      files_array.each do |file|
        photo = PropPhoto.create
        # photo.subdomain = subdomain
        # photo.folder = current_tenant_model.whitelabel_country_code
        # photo.tenant_id = current_tenant_model.id
        photo.image = file
        photo.save!

        # photo.update_attributes(:url => photo.image.metadata['url'])
        # ul = Pwb::PropPhotoUploader.new
        # ul.store!(file)
        # tried various options like above to ensure photo.image.url
        # which is nil at this point gets updated
        # - in the end it was just a reload that was needed:
        photo.reload

        property.prop_photos.push photo
        photos_array.push photo
      end

      # if json below is not valid, success callback on client will fail
      render json: photos_array.to_json
      # { "success": true }, status: :ok, head: :no_content
    end

    def remove_photo
      photo = PropPhoto.find(params[:id])
      property = Prop.find(params[:prop_id])
      property.prop_photos.destroy photo
      # if json below is not valid, success callback on client will fail
      render json: { "success": true }, status: :ok, head: :no_content
    end

    # def set_owner
    #   property = Prop.find(params[:prop_id])
    #   client = Client.find(params[:client_id])
    #   property.owners = [client]
    #   property.save!
    #   return render json: "success"
    # end

    # def unset_owner
    #   property = Prop.find(params[:prop_id])
    #   client = Client.find(params[:client_id])
    #   property.owners = []
    #   property.save!
    #   return render json: "success"
    # end

    private

    def properties_params(propertiesJSON)
      # propertiesJSON = params["propertiesJSON"]
      # unless propertiesJSON.is_a? Array
      #   propertiesJSON = JSON.parse propertiesJSON
      # end
      # pp = ActionController::Parameters.new(propertiesJSON)
      pp = ActionController::Parameters.new({ propertiesJSON: })
      # https://github.com/rails/strong_parameters/issues/140
      # params.require(:propertiesJSON).map do |p|
      pp.require(:propertiesJSON).map do |p|
        p.permit(
          :title, :description,
          :reference, :street_address, :city,
          :postal_code, :price_rental_monthly_current,
          :for_rent_short_term, :visible,
          :count_bedrooms, :count_bathrooms,
          :longitude, :latitude
        )
        # ActionController::Parameters.new(p.to_hash).permit(:title, :description)
      end
    end
  end
end
