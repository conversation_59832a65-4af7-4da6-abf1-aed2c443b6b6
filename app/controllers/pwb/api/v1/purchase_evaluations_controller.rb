module Pwb
  class Api::V1::PurchaseEvaluationsController < ApplicationApiController
    skip_before_action :verify_authenticity_token
    respond_to :json

    def show
      file_path = Rails.root.join('app/views/', 'pwb/api/v1/purchase_evaluations/show.json')
      render json: File.read(file_path), status: :ok
      # render 'pwb/api/v1/purchase_evaluations/show'
    end

    def list
      # properties = Pwb::Prop.all
      # render json: properties

      # https://demo.homestocompare.com/q-dash
      # List is a hard coded list replica of
      # https://be-05.homestocompare.com/api_h2c/wlbl-sbd-v001/v1/sbd_guests/single_for_dash
      render 'pwb/api/v1/purchase_evaluations/list'

      # /Users/<USER>/dev/sites-2022-dec/homestocompare-be/app/controllers/api_h2c/v1/subdomain_site_guests_controller.rb
      # /Users/<USER>/dev/sites-2022-dec/homestocompare-be/app/views/api_h2c/v1/subdomain_site_guests/single_for_dash.json.jbuilder
      # The original list action was modelled on this endpoint:
      # def get_single_for_dash
      #   #get "/list_for_subdomain_quest_dashboard/:quest_uuid" => "purchase_evaluations#list_for_subdomain_quest_dashboard"
      #   # this endpoint is modelled on above
      #   subdomain_manager = Managers::SubdomainManager.new(request.referrer)
      #   # quest = Quest.find_by(uuid: params[:quest_uuid])
      #   # but instead of getting a quest that is passed in, I get it directly
      #   # from the subdomain instance
      #   sbd_primary_quest = subdomain_manager.subdomain_instance.primary_quest
      #   # @subdomain_site_guests = subdomain_manager.list_sbd_guests
      #   site_guest = SiteGuest.find_by(uuid: params[:guest_uuid])
      #   @purchase_evaluations = []
      #   @subdomain_site_guest = subdomain_manager.get_permissions_for_guest(
      #     site_guest: site_guest,
      #     package_code: params[:package_code],
      #     request_referrer: request.referrer,
      #   ) || {}
      #   if sbd_primary_quest
      #     @purchase_evaluations = sbd_primary_quest.purchase_evaluations
      #   end
      #   return render "api_h2c/v1/subdomain_site_guests/single_for_dash"
      #   # return render "api_h2c/v1/purchase_evaluations/list_for_subdomain_quest_dashboard"
      # end
    end
  end
end
