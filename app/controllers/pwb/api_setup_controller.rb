module Pwb
  class ApiSetupController < ApplicationController
    def check_subdomain
      is_valid_subdomain = false
      error_message = 'Sorry, invalid subdomain'
      if params[:domain] && params[:domain].split('.').length > 2
        # main_domain = params[:domain].split('.')[1]
        subdomain_only = params[:domain].split('.')[0]
        is_valid_subdomain = AgencyTenant.where(
          {
            subdomain: subdomain_only,
            domain: 'propertywebbuilder.com'
          }
        ).count.positive?
      end

      if is_valid_subdomain
        render json: {
          success: true
        }, status: 200, head: :no_content
      else
        render json: {
          success: false,
          error_message:
        }, status: 500, head: :no_content
      end
      # if params[:domain] && params[:domain].split('.').length > 2
      #   main_domain = params[:domain].split('.')[1]
      #   subdomain_only = params[:domain].split('.')[0]
      #   # if ["homestocompare", "househunthero"].include? main_domain
      #   # spt 2023 - can't remember now why I was checking for the main domain
      #   is_valid_subdomain = [
      #     'bob', 'hetal', 'lynette', 'sam',
      #     'brad',
      #     'ana', 'birmingham', 'tessa', 'dan', 'moseley',
      #     'rice-chamberlains',
      #     'universities', 'brum-uni', 'selly-oak', 'brum',
      #     'its-a-brum-ting', 'codebunker', 'whisk', 'nick-holzer',
      #     'what-3-words',
      #     'nuneaton',
      #     'darshek', 'monica',
      #     'femida', 'nadin', 'dubai', 'dubai-v-london',
      #     'london', 'clare', 'greg', 'croydon', 'hackney',
      #     'andrewgoldthorpe', 'PropertyPortal',
      #     'andrewstanton', 'gary-chimwa',
      #     'chimni', 'nigel-wally',
      #     'edmondibrahimi', 'propertalis',
      #     'gabriel', 'normandy', 'kiev',
      #     'lviv', 'ira', 'kate', 'moscow',
      #     'natalia', 'georgia',
      #     'tobylewis',
      #     'madrid', 'brian', 'movingtomadrid',
      #     'simon', 'just-landed',
      #     'giampietro-zebellin', 'taiwan',
      #     # just busting out people I've had conversations with
      #     # on linkedin here:
      #     'david-charitos',
      #     'chrisadam101', 'proptechalfredodiazaraque',
      #     'patcheung', 'nunoguerra',
      #     'martinoneill', 'marcusrimmer',
      #     'taiyab-raja', 'tobiaspfeiffer',
      #     'wendygilch', 'nataliadzidziguri',
      #     'krishnamalyala', 'tlcengine',
      #     'isabelabustos', 'chemalarrea',
      #     'zainjaffer', 'mattblack',
      #     'matthewpye', 'buscadom',
      #     'bmpaquette',
      #     'shaheen', 'veracontent', 'spotahome',
      #     'casas-loco-de-espana', 'locos-de-idealista',
      #     'adri', 'gabbi', 'rafeek',
      #     'madrid-belgian-guy', 'laetitia', 'french-from-montpelier',
      #     'pierre', 'finland',
      #     'monami', 'eduardo', 'james-ecclestone',
      #     'adam-madrid', 'geek-lunch-madrid', 'acid-tango',
      #     'leniolabs', 'iago',
      #     'utopicus', 'rafa',
      #     'inmolite', 'cesar',
      #     'leonardo247',
      #     'mark-lynch', 'luke-diebold', 'patrick-pohler',
      #     'chris-stappen', 'peachsquared',
      #     # should check for more people here:
      #     # https://trello.com/b/AXNawUW4/hiring-cofounders
      #     'teun', 'steve', 'steve-rideout',
      #     'cotswolds', 'olly', 'one-off',
      #     'bvh', 'golf', 'costa-del-golf',
      #     'ed',
      #     'vincentgarrigoux', 'lausanne', 'lisa-etter',
      #     'francis-hibbert',
      #     'nick-aussie', 'nick', 'aussie-nick',
      #     'malaga', 'innovationcampus',
      #     'emanuele', 'leo-lara',
      #     'pepe', 'ali',
      #     'manuel', 'mike-sutton',
      #     'gaz', 'pisa', 'sardinia',
      #     'diego-montefusco', 'cagliari',
      #     'corinna', 'lufthansa', 'homes-for-pilots',
      #     'energy-efficient', 'eco-homes', 'epc-ratings',
      #     # searching for homes by energy efficiency
      #     # must be a good niche
      #     'homes-for-nurses', 'digital-nomad-homes',
      #     'villages-for-sale', 'castles-for-sale',
      #     'riads', 'party-homes',
      #     'akyaka', 'kitesurfing',
      #     'trevor', 'barnes',
      #     'ariana', 'max', 'wendy',
      #     'greg', 'deepika',
      #     'ghunt', 'valencia', 'hussein',
      #     'inmolite', 'oneoffplaces',
      #     're-renting', 'raul', 'lavapies',
      #     'archiebreakballs',
      #     'boars-hill', 'berwick-salome', 'crozet',
      #     'saltpond', 'accra', 'rich-tandoh', 'star100',
      #     'argentina',
      #     'sheffield', 'emilly', 'bristol',
      #     'berlin-kate', 'berlin', 'pitch-doctor', 'zekki',
      #     'berlin-dave', 'contentful',
      #     'tilly', 'kenya',
      #     'kath', 'ethiopia',
      #     'mahindra', 'tanzania', 'moshi',
      #     'amer', 'zurich',
      #     'angelaguertler', 'angela', 'togo',
      #     'tim', 'tim-norwich',
      #     'charles', 'betty', 'amsterdam',
      #     'dave-amsterdam', 'sanne', 'haarlem', 'heemstede',
      #     'todoist', 'lisbon', 'porto', 'violetta', 'tiagoalves',
      #     'smtp', 'caddy',
      #     'jon', 'homesandgardens', 'drew', 'bart', 'san-francisco',
      #     'joe', 'pamela', 'argentina',
      #     # a share_link model should have info about subdomain and domain
      #     # and should either save full_url or be able to construct it.
      #     # Should also have a count of views and perhaps other interactions
      #     # - maybe a json field with who its targeted at or who its been shared with....
      #     # the referring url when its visited???
      #     # Its own r/n to jots???

      #     # Also, I want to save caddy subdomain checks to db
      #     # Use a subdomain model???

      #     'laura-morena',
      #     'outrageous', 'cheap', 'exotic', 'intrepid',
      #     'biggest-reductions-uk',
      #     'biggest-reductions',
      #     'stunning-architecture',
      #     # would be good
      #     'demo-private', 'demo-public',
      #     'demo-secured-access', 'demo-house-hunt',
      #     'demo-property-influencer',
      #     'github-demo',
      #     'demo-indiehackers', 'demo-reddit',
      #     'demo-product-hunt', 'demo-hn',
      #     'demo-quiz', 'demo-price-guess',
      #     'demo-for-sale-by-owner',
      #     'demo', 'demo-tawk', 'demo-estate-agent',
      #     'demo-content-creator', 'demo-zillowgonecrazy',
      #     'demo-ai', 'demo-chatgpt',
      #     'rentals', 'rental-yields',
      #     'demo-rentals', 'demo-rental-yields',
      #     'brown-list', 'homes-to-avoid', 'red-flag-homes',
      #     'homes-with-history',
      #     'homes-from-movies', 'film-location-homes',
      #     'musical-homes', 'haunted-homes',
      #     'spooky',
      #     'road-trip', # for when Dee and I hit the road
      #     'blog', 'forum', 'thredded',
      #     'madrid-v-london', 'europe-v-us',
      #     'campaspero', 'raquel',
      #     'alex', 'torrelodones',
      #     'munich', 'sandra',
      #     'barcelona', 'annie',
      #     'colombo', 'marissa',
      #     'jll-spark', 'joseph-darkins',
      #     'alan-darmanin', 'malta',
      #     'giles-brown-lausanne',
      #     # worth having some subdomain for things like selling and home-valuations??
      #     'selling-later',
      #     'robb-report', 'james-edition',

      #     # https://www.periodproperty.co.uk/forum/threads/water-ingress-windmill-restoration.18079/
      #     # https://www.themodernhouse.com/sales-list/gallery-lofts/
      #     # https://uniquepropertybulletin.co.uk/the-battery-observation-post-bawdsey-suffolk-ip12-3ap/
      #     # https://www.churchofscotland.org.uk/about-us/property-and-church-buildings/properties-for-sale/properties/residential/32-old-parr-wynd-kilmarnock-ka3-1uu
      #     # https://www.buildstore.co.uk/find-land-or-a-project

      #     'buildstore', 'churchofscotland', 'uniquepropertybulletin', 'themodernhouse', 'periodproperty',
      #     'first-time-homebuyers', 'retirees', 'eco-conscious',
      #     'retirement',
      #     # https://www.wearehazel.co.uk/about-us
      #     'old-age', 'shared-ownership', 'timeshare',
      #     'co-living',
      #     'Downsizers', 'Urban-Farming', 'Equestrian', 'Fixer-Upper-Enthusiasts', 'Millennial',
      #     'military-families', 'pet-friendly', 'greenfingered', 'seaside', 'riverside',
      #     'mountain-view',
      #     'developments', 'new-builds', 'self-build-homes', 'portable-homes',
      #     'land', 'garages', 'gas-stations',
      #     'farms',
      #     'livenearfriends', 'buywithfriends', 'homebuyersclub',
      #     'drakes-broughton',
      #     'football', 'footballers', 'aston-villa',
      #     'england-v-spain',
      #     'spain-v-england',
      #     'sunsetsold', 'sellingsunset',
      #     # lstpos
      #     # https://www.moneywehave.com/is-lake-como-expensive/?utm_source=twitter&utm_medium=social&utm_campaign=ReviveOldPost
      #     # below inspired by above
      #     'lake-como',
      #     'sydney', 'jmyeez', # for <EMAIL>
      #     'tarumalla', # for <EMAIL>
      #     'mcmansionhell', # "McMansionHell",
      #     # https://www.reddit.com/r/McMansionHell/
      #     'captivatinghouses',
      #     'up-north',
      #     # for https://www.reddit.com/r/HousingUK/comments/16c7l99/buying_up_north/
      #     'SpottedonRightmove', 'wild-rightmove', 'wild-zoopla', 'wild-zillow',
      #     'crazy-rightmove', 'crazy-zoopla', 'crazy-zillow',
      #     'zillowgonewild', 'wrongmove',
      #     'RidiculousRealEstate',
      #     'airbnbust', # https://twitter.com/hashtag/Airbnbust?src=hashtag_click
      #     # https://www.reddit.com/r/Airbnbust/
      #     'airbnb-investments',
      #     # https://corey-ashton-walters.beehiiv.com/p/beach-e4c2
      #     'south-facing', 'feng-shui',
      #     'most-viewed',
      #     'bvh-demo',
      #     # https://www.rightmove.co.uk/news/articles/dream-properties/most-viewed-homes-aug23/
      #     'location-scouter', 'location-finding', 'location-finder',
      #     # from an realty-influencer called cassie:
      #     'cassie-selects',
      #     'bvh-selects', # ????
      #     'dawson-selects',
      #     'ed-selects', 'treasures-of-the-week',
      #     # bluelabel for bvh:
      #     'weebrix',
      #     # bluelabel for moving2madrid:
      #     'propertysquares',
      #     'chattymaps', 'propertypornhub',
      #     'gippety', 'gipety', 'gipetty',
      #     'us-investments', # https://dealsletter.beehiiv.com/p/dealsletter-issue-2-coasttocoast-real-estate-treasure-hunt
      #     'san-diego-investments', # https://dealsletter.beehiiv.com/p/dealsletter-1-san-diego-edition
      #     'dealsletter',
      #     # herokish generic subdomains:
      #     'purple-cloud', 'young-fire', 'shrill-breeze', 'white-glitter',
      #     'young-cloud', 'green-fire', 'blue-breeze', 'green-glitter',
      #     'terriblerealestateagentphotos', 'terrible-real-estate-photos',
      #     'bad-photography',
      #     # https://www.reddit.com/r/RealEstatePhotography/comments/176n4zl/getting_clients/
      #     'great-real-estate-photos',
      #     'great-photography',
      #     'places-lived',
      #     # https://twitter.com/roadstraveler
      #     'places-less-lived', # off the beaten track places to live
      #     'remote-places', 'remote-homes',
      #     'my-airdna',
      #     # https://www.airdna.co/
      #     'habitat', 'mike', 'nyc',
      #     'best-new-york-investments', 'best-madrid-investments',
      #     'best-london-investments', 'best-marbella-investments',
      #     'london-sales-v-rentals',
      #     # seo experiments:
      #     'marbella-homes-with-pools',
      #     'prince-harry-london-house-hunt',
      #     'portugal-homes-for-chris', # https://twitter.com/excid3
      #     'orangepeel', # https://www.reddit.com/r/HousingUK/comments/14jpe7k/i_think_we_as_a_collective_could_solve_the/
      #     'spotted-in-tamworth', # https://www.facebook.com/SpottedMediaTamworth/ https://twitter.com/spottamworth?lang=en

      #     'housecreep',
      #     # https://www.housecreep.com/ee/11213-mescalero-avenue-morongo-valley-ca-92256-us
      #     'stigmatized-property', 'stigmatized', 'stigmatised',
      #     # https://en.wikipedia.org/wiki/Stigmatized_property
      #     'dummy-data', 'dummy-api-data', 'dummy-dev-data', # for devs who want data for their api
      #     'costa-specialist-exclusive',
      #     # https://www.wowhaus.co.uk/
      #     'wowhaus',
      #     # https://www.themodernhouse.com/ https://inigo.com/
      #     'inigo', 'themodernhouse',
      #     # Could be interesting to host my article on property-influencers here:
      #     'property-influencers',
      #     # For Adrian Showers
      #     'punchli-client'
      #     # lstpos
      #   ].include?(subdomain_only)
      #   # wonder if there is a way of modifying above to be case-insensitive
      #   # end
      # end

      # subdomain_creator = Creators::SubdomainCreator.new
      # subdomain_instance = subdomain_creator.find_or_create_subdomain_by_fqdn(
      #   fqdn: params[:domain],
      #   # sbd_slug: subdomain_only,
      #   new_subdomain_is_whitelisted: is_valid_subdomain
      #   # fqdn: self.fqdn,
      #   # sbd_slug: sbd_slug,
      # )
    end
    # def current
    #   render json: {
    #     "general": {
    #       "beTypeAndVersion": 'rs1',
    #       "dataApiBase": 'https://bvh.propertywebbuilder.com'
    #     },
    #     "app_langs": {
    #       "defaultLang": {
    #         "shortIsoName": 'es'
    #       },
    #       "allLangs": [
    #         {
    #           "isoName": 'es',
    #           "shortIsoName": 'es',
    #           "nativeName": 'Español'
    #         },
    #         {
    #           "isoName": 'en-US',
    #           "shortIsoName": 'en',
    #           "nativeName": 'English'
    #         }
    #       ]
    #     },
    #     "footer_section": {
    #       "links": [
    #         {
    #           "route_name": 'rContentPage',
    #           "label": {
    #             "en": 'About us',
    #             "es": 'Nosotros'
    #           },
    #           "slug": {
    #             "en": 'about-us',
    #             "es": 'about-us'
    #           }
    #         },
    #         {
    #           "route_name": 'rContentPage',
    #           "label": {
    #             "en": 'Privacy Policy',
    #             "es": 'Politica de Privacidad'
    #           },
    #           "slug": {
    #             "en": 'privacy-policy',
    #             "es": 'privacy-policy'
    #           }
    #         },
    #         {
    #           "route_name": 'rContentPage',
    #           "label": {
    #             "en": 'Legal Notice',
    #             "es": 'Noticia Legal'
    #           },
    #           "slug": {
    #             "en": 'legal-notice',
    #             "es": 'legal-notice'
    #           }
    #         },
    #         {
    #           "route_name": 'rAdminAgencyOverview',
    #           "label": {
    #             "en": 'Admin',
    #             "es": 'Admin'
    #           }
    #         }
    #       ]
    #     },
    #     "header_section": {
    #       "site_name": 'BVH',
    #       "site_logo_url": 'https://demo.propertywebbuilder.com/uploads/demo/website_photo/small_fit_PropertyWebBuilder-logo.jpg',
    #       "links": [
    #         {
    #           "route_name": 'rLocaleHomePage',
    #           "label": {
    #             "en": 'Home'
    #           },
    #           "slug": {
    #             "en": ''
    #           },
    #           "listings_grouping": {}
    #         },
    #         {
    #           "route_name": 'rListingsContainer',
    #           "label": {
    #             "en": 'For Sale'
    #           },
    #           "slug": {},
    #           "listings_grouping": {
    #             "en": 'for-sale'
    #           }
    #         },
    #         {
    #           "route_name": 'rListingsContainer',
    #           "label": {
    #             "en": 'For Rent'
    #           },
    #           "slug": {},
    #           "listings_grouping": {
    #             "en": 'for-rent'
    #           }
    #         },
    #         {
    #           "route_name": 'rContentPage',
    #           "label": {
    #             "en": 'About us'
    #           },
    #           "slug": {
    #             "en": 'about-us'
    #           },
    #           "listings_grouping": {}
    #         }
    #       ]
    #     }
    #   }
    # end
  end
end
