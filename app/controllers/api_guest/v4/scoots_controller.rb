# app/controllers/api_guest/v4/dossier_jots_controller.rb
module ApiGuest::V4
  # app/controllers/api/v1/scoots_controller.rb

  class ScootsController < ApiGuestApplicationController
    # If you use Devi<PERSON> or another auth gem, uncomment and adjust:
    # before_action :authenticate_user! # Or your API token authentication
    skip_before_action :verify_authenticity_token # Ensure this is appropriate for your auth setup
    respond_to :json

    before_action :set_scoot

    # GET /api/v1/scoots/:uuid
    def show
      render json: @scoot, status: :ok
    end

    # PATCH/PUT /api/v1/scoots/:uuid
    def update
      if @scoot.update(scoot_params)
        render json: @scoot, status: :ok
      else
        render json: { errors: @scoot.errors.full_messages }, status: :unprocessable_entity
      end
    end

    private

    def set_scoot
      incoming_subdomain = request.subdomain.presence || 'default'
      @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)

      # Find the scoot by its UUID from the URL parameter
      # @scoot = Scoot.find_by(uuid: params[:scoot_uuid])

      # OR, if you are scoping to the current user:
      # @scoot = current_user.scoots.find_by(uuid: params[:uuid])
      # OR, if a user has only one scoot:
      # @scoot = current_user.scoot
      # Ensure @scoot is found, otherwise return a 404
      return if @scoot

      render json: { error: 'Scoot not found' }, status: :not_found
    end

    def scoot_params
      # Permit only the fields that the frontend form is designed to update.
      # This is crucial for security (Mass Assignment Protection).
      params.require(:scoot).permit(
        :preferred_vicinity_name,
        :max_distance_from_vicinity_preferred,
        :max_distance_from_vicinity_absolute,
        :preferred_distance_unit,
        :country_code,
        :preferred_property_type,
        :bedrooms_min_preferred,
        :bedrooms_max_preferred,
        :bedrooms_min_absolute,
        :bedrooms_max_absolute,
        :bathrooms_min_preferred,
        :bathrooms_max_preferred,
        :bathrooms_min_absolute,
        :bathrooms_max_absolute,
        :indoor_area_min_absolute,
        :indoor_area_max_absolute,
        :plot_area_min_absolute,
        :plot_area_max_absolute,
        :preferred_area_unit,
        :preferred_currency,
        :price_min_preferred_cents,
        :price_max_preferred_cents,
        :price_min_absolute_cents,
        :price_max_absolute_cents,
        :preferred_locale,
        # For JSONB fields like feature_preferences, you can permit them as a hash.
        # If you know the specific keys within feature_preferences, you can list them:
        # feature_preferences: [:garden, :parking, :balcony, :pet_friendly, :other_custom_feature]
        # Or, to allow any key-value pairs within feature_preferences (use with caution):
        feature_preferences: {} # Allows any hash structure for feature_preferences
      )
    end
  end
end
