# app/controllers/api_guest/v4/dossier_jots_controller.rb
module ApiGuest::V4
  class DossierJotsController < ApiGuestApplicationController
    skip_before_action :verify_authenticity_token # Ensure this is appropriate for your auth setup
    respond_to :json

    before_action :set_dossier
    before_action :set_jot, only: %i[show update destroy]

    # GET /dossiers/:realty_dossier_uuid/jots
    def index
      @jots = @dossier.dossier_jots.kept.order(created_at: :desc) # Order by creation, or another relevant field
      # Ensure you have a view at 'api_public/v4/dossier_jots/index'
      render 'api_public/v4/dossier_jots/index'
    end

    # GET /dossiers/:realty_dossier_uuid/jots/:uuid
    def show
      # Ensure you have a view at 'api_public/v4/dossier_jots/show'
      render 'api_public/v4/dossier_jots/show'
    end

    # POST /dossiers/:realty_dossier_uuid/jots
    def create
      @jot = @dossier.dossier_jots.new(jot_params_for_create) # Use specific params for create
      # The frontend sends 'jot_text', 'comparisonId', 'isPrimary', 'photo_uuids'
      # These should be handled by jot_params if named correctly (snake_case)
      # If agency_tenant_uuid is not part of jot_params, set it explicitly
      @jot.agency_tenant_uuid ||= AgencyTenant.unique_tenant&.uuid # Or your tenant logic

      if @jot.save
        if @htoc_access_code.present?
          @jot.update!(
            jot_creator_token: @htoc_access_code, jot_creator_name: 'Guest'
          )
        end
        if jot_params_for_create[:primary_photo_uuid].present?
          # puts jot_params_for_create[:primary_photo_uuid]
          @jot.update(is_photo_specific: true)
        end
        if jot_params_for_create[:dossier_assets_comparison_uuid].present?
          dac = DossierAssetsComparison.find_by(uuid: jot_params_for_create[:dossier_assets_comparison_uuid])
          if dac&.second_dossier_asset
            @jot.update(
              is_primary_listing_jot: false,
              dossier_asset_uuid: dac&.second_dossier_asset&.uuid
            )
          end
        end
        if @dossier.primary_dossier_asset.uuid == jot_params_for_create[:dossier_asset_uuid]
          @jot.update(is_primary_listing_jot: true)
          # @jot.update(is_primary: true)
        end
        render 'api_public/v4/dossier_jots/show', status: :created
      else
        render json: { errors: @jot.errors.full_messages, message: 'Failed to create jot.' }, status: :unprocessable_entity
      end
    rescue StandardError => e
      Rails.logger.error "DossierJotsController#create error: #{e.message} \n #{e.backtrace.join("\n")}"
      render json: { errors: ["An unexpected error occurred: #{e.message}"] }, status: :internal_server_error
    end

    # PUT/PATCH /dossiers/:realty_dossier_uuid/jots/:uuid
    def update
      if @jot.update(jot_params_for_update) # Use specific params for update
        # Ensure you have a view at 'api_public/v4/dossier_jots/show'
        render 'api_public/v4/dossier_jots/show'
      else
        render json: { errors: @jot.errors.full_messages, message: 'Failed to update jot.' }, status: :unprocessable_entity
      end
    rescue StandardError => e
      Rails.logger.error "DossierJotsController#update error: #{e.message} \n #{e.backtrace.join("\n")}"
      render json: { errors: ["An unexpected error occurred: #{e.message}"] }, status: :internal_server_error
    end

    # DELETE /dossiers/:realty_dossier_uuid/jots/:uuid
    def destroy
      if @jot.discard # Or @jot.destroy if not using soft deletes
        head :no_content
      else
        # This case might not be hit if discard always returns true or raises an error on failure
        render json: { errors: @jot.errors.full_messages, message: 'Failed to delete jot.' }, status: :unprocessable_entity
      end
    rescue StandardError => e
      Rails.logger.error "DossierJotsController#destroy error: #{e.message} \n #{e.backtrace.join("\n")}"
      render json: { errors: ["An unexpected error occurred while deleting: #{e.message}"] }, status: :internal_server_error
    end

    private

    def set_dossier
      @dossier = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])
      render json: { error: 'Dossier not found', message: 'The specified dossier could not be found.' }, status: :not_found unless @dossier
    end

    def set_jot
      # Ensure @dossier is set before calling this
      return unless @dossier # Guard clause

      @jot = @dossier.dossier_jots.find_by_uuid(params[:uuid]) # Use :uuid as per your route param for the jot
      render json: { error: 'Jot not found', message: 'The specified jot could not be found.' }, status: :not_found unless @jot
    end

    # Strong parameters for creating a jot
    # Frontend sends: jot_text, comparisonId, isPrimary, photo_uuids
    def jot_params_for_create
      params.permit(:jot_title, :jot_text, :dossier_asset_uuid,
                    :dossier_assets_comparison_uuid,
                    :primary_photo_uuid, :is_primary_listing_jot, photo_uuids: %i[url name uuid])
            .tap do |whitelisted|
              # If your model uses 'jot_title' but frontend sends 'jot_text'
              # whitelisted[:jot_title] = whitelisted.delete(:jot_text) if whitelisted.key?(:jot_text)

              # Ensure photo_uuids is an array if it's optional and might be nil
              whitelisted[:photo_uuids] ||= [] if params.key?(:photo_uuids)
            end
    end

    # Strong parameters for updating a jot
    # Frontend sends: jot_text, comparisonId, isPrimary, photo_uuids (all optional for update)
    def jot_params_for_update
      params.permit(:jot_text, :dossier_asset_uuid, :is_primary_listing_jot, photo_uuids: %i[url name uuid])
            .tap do |whitelisted|
              # If your model uses 'jot_title' but frontend sends 'jot_text'
              # whitelisted[:jot_title] = whitelisted.delete(:jot_text) if whitelisted.key?(:jot_text)

              # Handle explicit null for photo_uuids to clear them, or ensure it's an array if provided
              if params.key?(:photo_uuids)
                whitelisted[:photo_uuids] = params[:photo_uuids].nil? ? [] : params[:photo_uuids]
              end
            end
    end

    # If create and update params are identical, you can use a single method:
    # def jot_params
    #   params.permit(:jot_text, :dossier_asset_uuid, :is_primary, photo_uuids: [:url, :name, :uuid])
    # end
    # Then use `jot_params` in both create and update actions.
    # The frontend composable sends a payload like:
    # { jot_text: itemjot_Text, dossier_asset_uuid: comparisonId, is_primary: isPrimary, photo_uuids: photo_uuids }
    # `params.require(:dossier_jot)` is removed because the frontend sends a flat JSON object,
    # not nested under `dossier_jot`. If it were nested, you'd use:
    # params.require(:dossier_jot).permit(:jot_text, :dossier_asset_uuid, :is_primary, photo_uuids: [:url, :name, :uuid])
  end
end
