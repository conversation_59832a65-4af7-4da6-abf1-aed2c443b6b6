class ApiGuest::V4::DossierAssetsComparisonsController < ApiGuestApplicationController
  skip_before_action :verify_authenticity_token
  respond_to :json

  def show
    @comparison = DossierAssetsComparison.find_by_uuid(params[:comparison_uuid])
    if @comparison.nil?
      render json: { error: 'Comparison not found' }, status: :not_found
    else
      # @first_sale_listing = @comparison.first_dossier_asset.default_sale_listing
      # @second_sale_listing = @comparison.second_dossier_asset.default_sale_listing
      render 'api_public/v4/dossier_assets_comparisons/show'
    end
  end

  # #     // for viewing listing photos of a dossier asset - in case I need to run plan_b
  # # should really have authentication on this
  # def show_dossier_asset
  #   @dossier_asset = DossierAsset.find(params[:dossier_asset_id])
  #   if @dossier_asset.nil?
  #     render json: { error: 'dossier_asset not found' }, status: :not_found
  #   else
  #     render 'api_public/v4/dossier_assets/show'
  #   end
  # end
end
