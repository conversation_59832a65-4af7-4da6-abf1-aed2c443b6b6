module ApiGuest::V4
  class NeighbourhoodsController < ApiGuestApplicationController
    # If you use Devi<PERSON> or another auth gem, uncomment and adjust:
    # before_action :authenticate_user! # Or your API token authentication
    skip_before_action :get_htoc_access_code, :verify_authenticity_token # Ensure this is appropriate for your auth setup
    respond_to :json

    before_action :set_dossier

    def list
      # @neighbourhoods = @dossier.neighbourhoods
      # render json: @dossier, status: :ok
      render 'api_guests/v4/dossier_neighbourhoods/index'
    end

    def show
      # @neighbourhood = @dossier.neighbourhoods.find_by_uuid(params[:scoot_uuid])
      render json: @dossier, status: :ok
    end

    private

    def set_dossier
      @dossier = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])
      render json: { error: 'Dossier not found', message: 'The specified dossier could not be found.' }, status: :not_found unless @dossier
    end
  end
end
