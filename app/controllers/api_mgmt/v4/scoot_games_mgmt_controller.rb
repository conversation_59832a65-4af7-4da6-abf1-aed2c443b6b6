module ApiMgmt::V4
  class ScootGamesMgmtController < ApplicationController
    skip_before_action :verify_authenticity_token

    def listing_in_game_visibility
      @realty_game_listing = RealtyGameListing.find_by_uuid(
        params[:realty_game_listing_uuid]
      )
      @realty_game = RealtyGame.find_by_uuid(
        params[:game_uuid]
      )
      # @listing = @realty_game_listing&.sale_listing
      if @realty_game_listing
        @realty_game_listing.update!(visible_in_game: params[:sale_listing][:visible])
        # below was the old way which would affect all games where listing is used...
        # 9 july 2025 - will eventually get rid of below and only use above
        # still using @listing.visible on the frontend though
        # 27 july - time to get rid of below
        # @listing.visible = params[:sale_listing][:visible]
        # @listing.save!
        render json: { realty_game_listing: @realty_game_listing }, status: :ok
      else
        render json: { error: 'realty_game_listing or listing not found' }, status: :not_found
      end
    rescue StandardError => e
      Rails.logger.error("Error in listing_visibility: #{e.message}")
      render json: { error: 'Internal server error' }, status: :internal_server_error
    end

    def update_game_listing_attributes
      @realty_game_listing = RealtyGameListing.find_by_uuid(
        params[:realty_game_listing_uuid]
      )

      unless @realty_game_listing
        render json: { error: 'Realty game listing not found' }, status: :not_found
        return
      end

      if @realty_game_listing.update(realty_game_listing_params)
        render json: {
          realty_game_listing: @realty_game_listing,
          message: 'Game listing attributes updated successfully'
        }, status: :ok
      else
        render json: {
          error: 'Failed to update game listing attributes',
          errors: @realty_game_listing.errors.full_messages
        }, status: :unprocessable_entity
      end
    rescue StandardError => e
      Rails.logger.error("Error in update_game_listing_attributes: #{e.message}")
      render json: { error: 'Internal server error' }, status: :internal_server_error
    end

    private

    def realty_game_listing_params
      params.require(:realty_game_listing).permit(
        :position_in_game, :visible_in_game,
        :gl_title_atr, :gl_description_atr,
        :gl_image_url_atr, :gl_vicinity_atr,
        :gl_country_code_atr,
        :gl_latitude_atr, :gl_longitude_atr
      )
    end
  end
end
