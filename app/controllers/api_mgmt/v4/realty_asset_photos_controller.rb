module ApiMgmt::V4
  class RealtyAssetPhotosController < ApplicationController
    #  ApiJwtH2cApplicationController
    # TODO: - add auth
    skip_before_action :verify_authenticity_token
    # before_action :authorized, only: [:auto_login]

    def set_photo_visibility
      @realty_asset_photo = RealtyAssetPhoto.find_by_uuid(
        params['realty_asset_photo_uuid']
      )
      unless @realty_asset_photo
        return render json: {
          success: false,
          error_messages: 'not found'
        }, status: 404, head: :no_content
      end
      # @realty_asset_photo.photo_title = params["photo_title"]
      @realty_asset_photo.flag_is_hidden = !(params['restore_or_remove'] == 'restore')
      @realty_asset_photo.save!
      render json: { success: true }, status: 200, head: :no_content
    end

    def discard_photo
      @realty_asset_photo = RealtyAssetPhoto.find_by_uuid(
        params['realty_asset_photo_uuid']
      )
      unless @realty_asset_photo
        return render json: {
          success: false,
          error_messages: 'not found'
        }, status: 404, head: :no_content
      end
      @realty_asset_photo.discard
      # @realty_asset_photo.save!
      render json: { success: true }, status: 200, head: :no_content
    end

    def set_pics_order
      params[:pics_order].as_json.each do |pic_to_order_array|
        realty_asset_photo = RealtyAssetPhoto.find_by_uuid(
          pic_to_order_array[0]
        )
        # pics_order is a zero based array
        # By default acts_as_list uses 1 based array (top_of_list = 1)
        # so I need to add 1 to the zero based array
        realty_asset_photo.sort_order = pic_to_order_array[1] + 1
        realty_asset_photo.save!
      end
      render json: { success: true }, status: 200, head: :no_content
    end

    def update_details
      @realty_asset_photo = RealtyAssetPhoto.find_by_uuid(
        params['realty_asset_photo_uuid']
      )
      @realty_asset_photo.photo_title = params['photo_title']
      @realty_asset_photo.photo_description = params['photo_description']
      @realty_asset_photo.save!
      render json: { success: true }, status: 200, head: :no_content
    end

    def set_photo_title
      @realty_asset_photo = RealtyAssetPhoto.find_by_uuid(
        params['realty_asset_photo_uuid']
      )
      @realty_asset_photo.photo_title = params['photo_title']
      @realty_asset_photo.save!
      render json: { success: true }, status: 200, head: :no_content
    end
  end
end
