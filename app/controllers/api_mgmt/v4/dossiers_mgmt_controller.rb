module ApiMgmt::V4
  class DossiersMgmtController < ApplicationController
    skip_before_action :verify_authenticity_token

    # def show_dossier
    #   @realty_dossier = RealtyDossier.last
    #   if @realty_dossier
    #     render json: { realty_dossier: @realty_dossier }, status: :ok
    #   else
    #     render json: { error: 'No dossier found' }, status: :not_found
    #   end
    # rescue StandardError => e
    #   Rails.logger.error("Error in show_dossier: #{e.message}")
    #   render json: { error: 'Internal server error' }, status: :internal_server_error
    # end

    def dossier_from_url
      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

      retrieval_end_point = params[:retrieval_end_point]
      retrieval_portal = params[:retrieval_portal]

      unless retrieval_end_point && retrieval_portal
        render json: { error: 'Missing required parameters' }, status: :bad_request
        return
      end

      @realty_dossier = Creators::PortalDossierCreator.new.create_from_url(retrieval_end_point, retrieval_portal)

      if @realty_dossier
        render json: { realty_dossier: @realty_dossier }, status: :ok
      else
        render json: { error: 'Failed to create dossier' }, status: :unprocessable_entity
      end
    rescue ArgumentError => e
      Rails.logger.error("Invalid argument in dossier_from_url: #{e.message}")
      render json: { error: "Invalid parameters: #{e.message}" }, status: :bad_request
    rescue StandardError => e
      Rails.logger.error("Error in dossier_from_url: #{e.message}")
      render json: { error: 'Internal server error' }, status: :internal_server_error
    end

    def add_asset_from_url
      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

      retrieval_end_point = params[:retrieval_end_point]
      retrieval_portal = params[:retrieval_portal]

      unless retrieval_end_point && retrieval_portal
        render json: { error: 'Missing required parameters' }, status: :bad_request
        return
      end

      @realty_dossier = RealtyDossier.find(params[:dossier_id])

      if @realty_dossier
        @new_asset = @realty_dossier.add_asset_from_portal_url(retrieval_end_point, retrieval_portal)

        # VCR.use_cassette("onthemarket_#{extra_prop_ref}_page_1") do
        #   # retrieval_end_point = "https://www.onthemarket.com/details/#{extra_prop_ref}/"
        #   puts asset_2
        # end
        render json: { realty_dossier: @realty_dossier }, status: :ok
      else
        render json: { error: 'No dossier found' }, status: :not_found
      end
    rescue StandardError => e
      Rails.logger.error("Error in add_url: #{e.message}")
      render json: { error: 'Internal server error' }, status: :internal_server_error
    end
  end
end
