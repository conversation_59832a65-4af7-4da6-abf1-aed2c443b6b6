module Superwiser
  class GameStatsController < Superwiser::ApplicationController
    def overview
      @total_games_played = GameSession.count
      @average_score = GameSession.average(:total_score)
      @unique_players_by_token = GameSession.distinct.count(:site_visitor_token)
      @unique_players_by_name = GameSession.where.not(session_guest_name: [nil, '']).distinct.count(:session_guest_name)
      @game_sessions_over_time = GameSession.group_by_day(:created_at).count
      # render inline: "<h1>Game Stats Overview</h1><p>Placeholder for overview page.</p>"
    end

    def player_activity
      @player_stats = GameSession.select(
        "COALESCE(NULLIF(session_guest_name, ''), site_visitor_token) AS player_identifier",
        "COUNT(id) AS games_played",
        "AVG(total_score) AS average_player_score",
        "MAX(created_at) AS last_played_date"
      ).group("player_identifier").order("games_played DESC")

      @score_distribution = GameSession.group(:total_score).count.sort_by { |score, count| score.to_i }
      # render inline: "<h1>Player Activity</h1><p>Placeholder for player activity page.</p>"
    end

    def guess_analysis
      @total_guesses = GuessedPrice.count
      @average_accuracy = GuessedPrice.average(:percentage_above_or_below)
      @guess_distribution_by_percentage = GuessedPrice.group(:percentage_above_or_below).count.sort_by { |percentage, count| percentage.to_i }
      @top_guessed_amounts = GuessedPrice.group(:guessed_price_amount_cents, :guessed_price_currency).count.sort_by { |_price_cents_currency, count| count }.reverse.first(10)
      # render inline: "<h1>Guess Analysis</h1><p>Placeholder for guess analysis page.</p>"
    end
  end
end
