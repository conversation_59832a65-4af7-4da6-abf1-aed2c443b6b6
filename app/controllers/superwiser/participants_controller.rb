module Superwiser
  class ParticipantsController < Superwiser::ApplicationController
    # Overwrite any of the RESTful controller actions to implement custom behavior
    # For example, you may want to send an email after a foo is updated.
    #
    # def update
    #   super
    #   send_foo_updated_email(requested_resource)
    # end

    def scoped_resource
      resource_class.includes(:ahoy_visits).order(last_visit_at: :desc)
    end

    def show
      super
      @analytics_summary = calculate_participant_analytics(requested_resource)
      @recent_visits = requested_resource.ahoy_visits.includes(:events).order(started_at: :desc).limit(10)
      @recent_events = requested_resource.ahoy_events.order(time: :desc).limit(20)
    end

    # Custom action to sync participant data
    def sync
      ParticipantSyncJob.perform_later(visitor_token: requested_resource.visitor_token)
      flash[:notice] = "Participant sync job has been queued."
      redirect_to [namespace, requested_resource]
    end

    # Custom action to view participant analytics
    def analytics
      @participant = requested_resource
      @analytics_data = {
        engagement_timeline: calculate_engagement_timeline(@participant),
        page_views: calculate_page_views(@participant),
        event_breakdown: calculate_event_breakdown(@participant),
        session_analysis: calculate_session_analysis(@participant)
      }
    end

    private

    def calculate_participant_analytics(participant)
      {
        total_sessions: participant.ahoy_visits.count,
        total_events: participant.ahoy_events.count,
        average_events_per_session: participant.total_visits > 0 ? (participant.total_events.to_f / participant.total_visits).round(2) : 0,
        engagement_score: participant.engagement_score,
        behavior_category: participant.behavior_category,
        days_active: participant.days_since_first_visit.to_i,
        days_since_last_visit: participant.days_since_last_visit.to_i,
        most_visited_pages: calculate_most_visited_pages(participant),
        device_breakdown: calculate_device_breakdown(participant),
        traffic_sources: calculate_traffic_sources(participant)
      }
    end

    def calculate_most_visited_pages(participant)
      participant.ahoy_events
                 .where(name: '$view')
                 .group("properties->>'url'")
                 .count
                 .sort_by { |_, count| -count }
                 .first(5)
                 .to_h
    end

    def calculate_device_breakdown(participant)
      participant.ahoy_visits
                 .group(:device_type)
                 .count
    end

    def calculate_traffic_sources(participant)
      sources = {}
      participant.ahoy_visits.each do |visit|
        source = categorize_traffic_source(visit)
        sources[source] = (sources[source] || 0) + 1
      end
      sources
    end

    def categorize_traffic_source(visit)
      return 'Direct' if visit.referrer.blank?
      return 'Email' if visit.utm_medium == 'email'
      return 'Social Media' if visit.referrer&.match?(/facebook|twitter|linkedin|instagram/)
      return 'Search Engine' if visit.referrer&.match?(/google|bing|yahoo|duckduckgo/)
      return 'Paid Ads' if visit.utm_medium&.match?(/cpc|ppc|paid/)
      
      'Other'
    end

    def calculate_engagement_timeline(participant)
      participant.ahoy_visits
                 .group_by_day(:started_at, range: 30.days.ago..Time.current)
                 .count
    end

    def calculate_page_views(participant)
      participant.ahoy_events
                 .where(name: '$view')
                 .group("properties->>'url'")
                 .count
                 .sort_by { |_, count| -count }
                 .first(10)
                 .to_h
    end

    def calculate_event_breakdown(participant)
      participant.ahoy_events
                 .group(:name)
                 .count
    end

    def calculate_session_analysis(participant)
      visits = participant.ahoy_visits.includes(:events)
      
      session_durations = visits.map do |visit|
        events = visit.events.order(:time)
        next 0 if events.count < 2
        
        (events.last.time - events.first.time) / 60.0 # in minutes
      end.compact

      {
        total_sessions: visits.count,
        average_duration: session_durations.any? ? (session_durations.sum / session_durations.size).round(2) : 0,
        longest_session: session_durations.max || 0,
        shortest_session: session_durations.min || 0,
        sessions_with_events: visits.joins(:events).distinct.count
      }
    end

    # Override this method to specify custom lookup behavior.
    # This will be used to set the resource for the `show`, `edit`, and `update`
    # actions.
    #
    # def find_resource(param)
    #   Foo.find_by!(slug: param)
    # end

    # The result of this lookup will be available as `requested_resource`

    # Override this if you have certain roles that require a subset
    # this will be used to set the records shown on the `index` action.
    #
    # def scoped_resource
    #   if current_user.super_admin?
    #     resource_class
    #   else
    #     resource_class.with_less_stuff
    #   end
    # end

    # Override `resource_params` if you want to transform the submitted
    # data before it's persisted. For example, the following would turn all
    # empty values into nil values. It uses other APIs such as `resource_class`
    # and `dashboard`:
    #
    # def resource_params
    #   params.require(resource_class.model_name.param_key).
    #     permit(dashboard.permitted_attributes(action_name)).
    #     transform_values { |value| value == "" ? nil : value }
    # end

    # See https://administrate-demo.herokuapp.com/customizing_controller_actions
    # for more information
  end
end
