module Superwiser
  module AiInsights
    class CompositeImageSourcesController < Superwiser::ApplicationController
      # Customize the ordering of records on index pages
      def order
        @order ||= Administrate::Order.new(
          params.fetch(resource_name, {}).fetch(:order, 'created_at'),
          params.fetch(resource_name, {}).fetch(:direction, 'desc')
        )
      end

      # Override this method to specify custom lookup behavior for show/edit/update
      # def find_resource(param)
      #   AiInsights::CompositeImageSource.find_by!(uuid: param)
      # end

      # Override scoped_resource if you need to limit records per user/admin
      # def scoped_resource
      #   super.where(agency_tenant_uuid: current_user.agency_tenant_uuid)
      # end
    end
  end
end