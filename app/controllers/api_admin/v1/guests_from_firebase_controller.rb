module ApiAdmin::V1
  class GuestsFromFirebaseController < ActionController::Base
    skip_before_action :verify_authenticity_token
    after_action :track_auth_event
    before_action :get_global_params

    def htoc_auth_reg
      id_token = params[:fid_tok]
      verifier = FirebaseTokenVerifier.new(id_token)
      fire_details = verifier.verify
      if fire_details
        email = fire_details[:payload][:email]

        # Check if a user with the given email already exists
        if Pwb::User.exists?(email:)
          render json: { error: 'Email already exists' }, status: :unprocessable_entity
          return
        end

        tenant_manager = Managers::TenantManager.new(request:, fire_details:)
        tenant_permissions = tenant_manager.get_tenant_and_permissions
        mgmt_prefix = 'https://medo.propertywebbuilder.com/superwiser/pwb/users/'
        Utils::NtfyUtils.new.send_public_ntfy "New reg by: #{email} #{mgmt_prefix}/#{tenant_permissions[:pwb_user_id]}"

        render json: {
          decoded_token: fire_details[:payload],
          tenant_permissions:
        }
      else
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    end

    def htoc_auth_on
      id_token = params[:fid_tok]
      verifier = FirebaseTokenVerifier.new(id_token)
      fire_details = verifier.verify
      if fire_details
        # email = fire_details[:payload][:email]
        tenant_manager = Managers::TenantManager.new(request:, fire_details:)
        htoc_data = tenant_manager.get_tenant_and_permissions
        # mgmt_prefix = 'https://medo.propertywebbuilder.com/superwiser/pwb/users'
        # Utils::NtfyUtils.new.send_public_ntfy "New sign in by: #{email} #{mgmt_prefix}/#{htoc_data[:pwb_user_id]}"

        render json: {
          # decoded_token: fire_details[:payload],
          htoc_data: {
            # agency_tenant: htoc_data[:agency_tenant],
            pwb_user: htoc_data[:pwb_user],
            permissions: htoc_data[:permissions]
          },
          user_data: {
            # pwb_user: htoc_data[:pwb_user],
            # permissions: htoc_data[:permissions]
          }
        }
      else
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    end

    def multi_tenant_frb_auth_reg
      id_token = params[:fid_tok]
      verifier = FirebaseTokenVerifier.new(id_token)
      fire_details = verifier.verify
      if fire_details
        email = fire_details[:payload][:email]

        # Check if a user with the given email already exists
        if Pwb::User.exists?(email:)
          render json: { error: 'Email already exists' }, status: :unprocessable_entity
          return
        end

        tenant_manager = Managers::TenantManager.new(request:, fire_details:)
        tenant_permissions = tenant_manager.get_tenant_and_permissions
        mgmt_prefix = 'https://medo.propertywebbuilder.com/superwiser/pwb/users/'
        Utils::NtfyUtils.new.send_public_ntfy "New reg by: #{email} #{mgmt_prefix}/#{tenant_permissions[:pwb_user_id]}"

        render json: {
          decoded_token: fire_details[:payload],
          tenant_permissions:
        }
      else
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    end

    def multi_tenant_frb_auth_on
      id_token = params[:fid_tok]
      verifier = FirebaseTokenVerifier.new(id_token)
      fire_details = verifier.verify
      if fire_details
        email = fire_details[:payload][:email]

        # # Check if a user with the given email already exists
        # if Pwb::User.exists?(email:)
        #   render json: { error: 'Email already exists' }, status: :unprocessable_entity
        #   return
        # end

        tenant_manager = Managers::TenantManager.new(request:, fire_details:)
        tenant_permissions = tenant_manager.get_tenant_and_permissions
        # mgmt_prefix = 'https://medo.propertywebbuilder.com/superwiser/pwb/users'
        # Utils::NtfyUtils.new.send_public_ntfy "New sign in by: #{email} #{mgmt_prefix}/#{tenant_permissions[:pwb_user_id]}"

        render json: {
          decoded_token: fire_details[:payload],
          tenant_permissions: {
            agency_tenant: tenant_permissions[:agency_tenant],
            pwb_user: tenant_permissions[:pwb_user],
            permissions: tenant_permissions[:permissions]
          }
        }
      else
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    end

    private

    def get_global_params
      request_referrer = request.referrer || 'no_request_referrer'
      fqdn_label = request_referrer.split('//')[1] || ''
      ref_sbd = ''
      ref_host = fqdn_label.split('.')[0] || 'no_ref_host'
      if fqdn_label.split('.').length > 2
        ref_sbd = fqdn_label.split('.')[0] || 'no_ref_sbd'
        ref_host = fqdn_label.split('.')[1] || 'no_ref_host'
      end

      @global_params = {
        ref_sbd:,
        ref_host:,
        fqdn_label:,
        package_code: params[:package_code],
        fid_tok: params[:fid_tok],
        loc_path: params[:loc_path]
      }
    end

    def track_auth_event
      return unless defined?(current_visit) && current_visit

      unless defined?(current_visit) && current_visit
        ahoy.track "Failed to #{action_name}", @global_params
      end

      safely do
        return if current_visit.browser == 'Wget'

        @global_params[:id_from_visit] = current_visit.id
        ahoy.track "Called #{action_name}", @global_params if controller_name != 'client'
      end
    end
  end
end
