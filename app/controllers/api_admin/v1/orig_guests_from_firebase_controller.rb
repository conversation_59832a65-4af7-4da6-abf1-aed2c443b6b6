module ApiAdmin::V1
  # require "eu_central_bank"

  class OrigGuestsFromFirebaseController < ActionController::Base
    #  ApiPublicApplicationController
    #  ApiJwtSubdomainRootController # ApiJwtH2cApplicationController
    skip_before_action :verify_authenticity_token
    # before_action :authorized, only: [:auto_login]
    after_action :track_auth_event
    before_action :get_global_params

    def frb_auth_reg
      id_token = params[:fid_tok]
      # res = FirebaseIdToken::Signature.verify(id_token)
      # res2  = JWT.decode(id_token, nil, false).first
      verifier = FirebaseTokenVerifier.new(id_token)
      fire_details = verifier.verify
      if fire_details
        tenant_manager = Managers::TenantManager.new(request:, fire_details:)
        tenant_permissions = tenant_manager.get_tenant_and_permissions
        # @current_user = decoded_token # Store decoded token info or fetch a user from your database
        render json: {
          decoded_token: fire_details[:payload],
          tenant_permissions:
        }
      else
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    end

    def frb_auth_on
      id_token = params[:fid_tok]
      # res = FirebaseIdToken::Signature.verify(id_token)
      # res2  = JWT.decode(id_token, nil, false).first
      verifier = FirebaseTokenVerifier.new(id_token)
      fire_details = verifier.verify
      if fire_details
        tenant_manager = Managers::TenantManager.new(request:, fire_details:)
        tenant_permissions = tenant_manager.get_tenant_and_permissions
        # @current_user = decoded_token # Store decoded token info or fetch a user from your database
        render json: {
          decoded_token: fire_details[:payload],
          tenant_permissions:
        }
      else
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    end

    def frb_auth_off
      # subdomain_manager = Managers::SubdomainManager.new(request.referrer)
      # subdomain_instance = subdomain_manager.ensure_subdomain_instance!
      # @subdomain_instance = subdomain_manager.subdomain_instance

      # # @subdomain_quest = get_quest_for_subdomain
      # # return if performed?
      # # Aug 2023 - Will now get quest from subdomain:
      # @subdomain_quest = @subdomain_instance ? @subdomain_instance.primary_quest : nil

      # if @subdomain_quest
      #   @site_visitor = SiteVisitor.find_by_avt(params[:svt]) || SiteVisitor.find_or_create_for_visit(current_visit,
      #                                                                                                 params[:site_tlc], request.host)
      #   # check_for_quest_and_site_visitor
      #   # return if performed?

      #   @current_site_visitor_quest = SiteVisitorQuest.find_or_create_by({
      #                                                                      quest_uuid: @subdomain_quest.uuid,
      #                                                                      site_visitor_token: @site_visitor.ahoy_visitor_token
      #                                                                    })
      # end
      # @has_valid_sbd_klave = false
      # @has_valid_sbd_klave = true if @subdomain_instance && @subdomain_instance.valid_sbd_klave?(params[:sbd_klave])
      # render 'api_h2c/v1/site_visitors/show_for_subdomain'
    end

    private

    def get_global_params
      request_referrer = request.referrer || 'no_request_referrer'
      fqdn_label = request_referrer.split('//')[1] || ''
      ref_sbd = ''
      ref_host = fqdn_label.split('.')[0] || 'no_ref_host'
      if fqdn_label.split('.').length > 2
        # for where there might be a subdomain
        ref_sbd = fqdn_label.split('.')[0] || 'no_ref_sbd'
        ref_host = fqdn_label.split('.')[1] || 'no_ref_host'
      end

      # To consider - use URI for breaking up referrer
      # URI(request.referer).path == '/adsense'
      @global_params = {
        ref_sbd:,
        ref_host:,
        fqdn_label:,
        package_code: params[:package_code],
        fid_tok: params[:fid_tok],
        loc_path: params[:loc_path]
        # event_ip: "",
      }
      # Should set @global_params in ApiJwtSubdomainRootController and do more with it.
      # For now it just gets sent to ahoy.track where
      # it is merged with fields from ahoy initializer
    end

    def track_auth_event
      return unless defined?(current_visit) && current_visit

      # Spt 2023
      # based on track_psq_action
      puts "Cookies are #{cookies.as_json[0]}"
      unless defined?(current_visit) && current_visit
        puts 'Unable to find ahoy current_visit'
        # sometimes current_visit isn't set - above seems to fix that
        ahoy.track "Failed to #{action_name}", @global_params
      end

      puts "current_visit is #{current_visit.as_json(only: %w[visit_token visitor_token started_at])}"

      safely do
        return if current_visit.browser == 'Wget'

        @global_params[:id_from_visit] = current_visit.id
        # elsif controller_name != "client"
        if controller_name != 'client'
          # ahoy.track "Called #{controller_name}##{action_name}", props
          ahoy_track_res = ahoy.track "Called #{action_name}", @global_params
          # Thought I might be able to use ahoy_track_res above to directly set
          # site_visitor_token but it is just a boolean
          #  request.filtered_parameters
        end
      end
    end

    # def check_for_quest_and_site_visitor
    #   unless @site_visitor.present?
    #     return handle_notifiable_error_json(
    #              error_messages: "error with visitor",
    #              notification_class: VisitorErrorNotification,
    #            )
    #   end
    # end

    def encode_token(payload)
      JWT.encode(payload, PsqConfig::General::JwtKey)
    end

    # def decoded_firebase_token
    #   if auth_header
    #     token = auth_header.split(" ")[1]
    #     # header: { 'Authorization': 'Bearer <token>' }
    #     begin
    #       #   JWT.decode(token, PsqConfig::General::JwtKey, true, algorithm: "HS256")
    #       # rescue JWT::DecodeError
    #       #   nil
    #     end
    #   end
    # end

    # def user_params
    #   params.permit(:username, :password, :age, :email)
    # end

    # def create_user_params
    #   params.require(:account_params).permit(:username, :password, :age, :email)
    # end
  end
end
