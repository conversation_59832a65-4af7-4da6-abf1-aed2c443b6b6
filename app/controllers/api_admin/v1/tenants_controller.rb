module ApiAdmin
  module V1
    class TenantsController < BaseController
      def provision
        new_subdomain = request.subdomain.presence || 'default'
        # adding below to handle case where subdomain is like "agency.be.propertywebbuilder.com"
        # Essentially I only want the first part of the subdomain
        new_subdomain = new_subdomain.split('.')[0]

        new_tenant = AgencyTenant.create!(
          # primary_pwb_user_uuid: pwb_user_uuid,
          subdomain: new_subdomain,
          domain: 'propertywebbuilder.com',
          is_claimed: false
        )
        render json: {
          status: :success,
          tenant: new_tenant.as_json(only: %i[id subdomain domain is_provisioned created_at])
        }, status: :created
      rescue ActiveRecord::RecordInvalid => e
        render json: {
          status: :error,
          message: e.message
        }, status: :unprocessable_entity
      end
    end
  end
end
