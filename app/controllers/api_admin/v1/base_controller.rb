module ApiAdmin
  module V1
    class BaseController < ApplicationController
      before_action :authenticate_admin_admin

      private

      def authenticate_admin_admin
        unless user_signed_in?
          render json: { error: 'Authentication required' }, status: :unauthorized
          return
        end

        return if current_user&.is_admin_admin?

        render json: { error: 'Unauthorized' }, status: :forbidden
        nil
      end

      # def authenticate_request
      #   # You'll need to implement your actual authentication logic here
      #   # For example, checking for a valid JWT token in the Authorization header
      #   return if valid_auth_token?

      #   render json: { error: 'Unauthorized' }, status: :unauthorized
      # end

      # def valid_auth_token?
      #   # Implement your token validation logic here
      #   # For now, this is a placeholder that always returns false
      #   false
      # end
    end
  end
end
