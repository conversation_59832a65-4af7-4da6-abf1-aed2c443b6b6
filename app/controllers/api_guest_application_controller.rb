class ApiGuestApplicationController < ActionController::Base
  respond_to :json
  # protect_from_forgery with: :exception

  before_action :get_htoc_access_code
  # after_action :track_action

  def get_htoc_access_code
    @htoc_access_code = request.headers['X-User-Access-Code']
    # when I put a debugger here I often get a nil value for htoc_access_code
    # Suspect might me for preflight calls
    # Or perhaps the SSR call....

    incoming_subdomain = request.subdomain.presence || 'default'
    @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)

    # # !@scoot.safe_dossier_ids.include?(@realty_dossier.id)
    # unless @scoot.has_access_to_dossier?(@realty_dossier)
    #   render json: { error: 'Access error' }, status: :unauthorized
    #   return
    # end

    # return if @scoot.access_token == @htoc_access_code
    return if @scoot&.has_access_to_tasks?(@htoc_access_code)

    render json: { error: 'Token error' }, status: :unauthorized
  end
  # def set_meta
  #   @meta = {
  #     at: DateTime.now.to_s,
  #     loc: '/' + request.host
  #   }
  # end
end
