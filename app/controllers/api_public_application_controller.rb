# frozen_string_literal: true

class ApiPublicApplicationController < ActionController::Base
  # set_current_tenant_by_subdomain_or_domain(:agency, :subdomain, :domain)
  respond_to :json
  protect_from_forgery with: :exception

  before_action :set_locale, :set_meta, :set_client_base_url, :ensure_tenant
  after_action :track_action

  def set_meta
    @meta = {
      at: DateTime.now.to_s,
      loc: '/' + request.host
    }
  end

  def ensure_tenant
    return if ActsAsTenant.current_tenant

    render_error_json 'Site not provisioned'
  end

  def set_client_base_url
    url_protocol = request.protocol
    if ENV['RAILS_ENV'] == 'production'
      # Nove 2021 - not sure how to set http protocol to be passed through caddy proxy
      # so forcing https in production
      url_protocol = 'https://'
    end
    @client_base_url = "#{url_protocol}#{request.host_with_port}"
  end

  def set_locale
    # locale = Website.unique_instance.default_client_locale_to_use
    # if params[:locale] && (I18n.locale_available? params[:locale])
    #   # passed in params override user's default
    #   locale = params[:locale]
    # end
    locale = params[:locale] || 'en'
    I18n.locale = locale.to_sym
  end

  def render_success_json(success_message)
    render json: {
      success: true,
      success_message:
    }, status: :ok, head: :no_content
  end

  def render_error_json(error_messages)
    # TODO: - log error to logger....
    # flash.now[:error] = 'Cannot send message.'
    render json: {
      success: false,
      error_messages:
    }, status: 500, head: :no_content
  end

  def handle_error_json(excepn = nil)
    error_messages = [I18n.t('server.forms.error')]
    if excepn
      error_messages.push(excepn)
      logger.debug ([excepn.message] + excepn.backtrace).join($/)
      logger.error excepn.message
    end
    render json: {
      success: false,
      error_messages:
    }, status: 500, head: :no_content
  end

  private

  def track_action
    unless current_visit
      ahoy.track "Initializing visit to #{controller_name}##{action_name}" # , props
      # sometimes current_visit isn't set - above seems to fix that
    end
    return unless current_visit

    safely do
      return if current_visit.browser == 'Wget'

      props = {
        is_public_api: true,
        i18n_locale: I18n.locale,
        verb: request.method,
        url: request.url,
        params: request.filtered_parameters
      }
      # if ((controller_name == "listings") && (action_name == "view"))
      # listing_ref = request.filtered_parameters["listing_ref"] || "unknown"
      # site_visitor_token = current_visit.visitor_token
      # RealtyAssetView.create(
      #   external_reference: listing_ref,
      #   details: props,
      #   locale: I18n.locale,
      #   site_visitor_token: site_visitor_token,
      # )
      # elsif ((controller_name == "search_simple") && (action_name != "results_from_params_fuzzy"))
      if (controller_name == 'search_simple') && (action_name == 'results_only')
        # ....
        site_visitor_token = current_visit.visitor_token
        new_query = RealtySearchQuery.create(
          details: props,
          locale: I18n.locale,
          site_visitor_token:
        )
        # TODO: - add property_type below:
        # worth including :page_no  below?
        %i[property_type city features price_min price_max bedrooms_min bedrooms_max bathrooms_min
           bathrooms_max].each do |param_name|
          query_val = request.filtered_parameters[param_name]
          new_query[param_name] = query_val unless query_val == 'default'
        end
        new_query.operation = request.filtered_parameters[:op]
        new_query.save!
      elsif controller_name != 'client'
        ahoy.track "Called #{controller_name}##{action_name}", props
        #  request.filtered_parameters
      end
    end
  end

  # def get_default_search_params(params, search_fields)
end
