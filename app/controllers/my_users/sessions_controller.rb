class MyUsers::SessionsController < ApplicationController
  # Render without layout for both actions
  layout false

  def new
    @user = Pwb::User.new
  end

  def create
    @user = Pwb::User.find_by(email: params[:email])
    if @user&.valid_password?(params[:password])
      sign_in(:user, @user)
      redirect_to root_path, notice: 'Signed in successfully.'
    else
      flash.now[:alert] = 'Invalid email or password.'
      render :new, layout: false
    end
  end
end 