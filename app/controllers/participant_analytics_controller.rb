class ParticipantAnalyticsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_date_range
  before_action :set_analytics_service

  def index
    @overview_metrics = @analytics_service.overview_metrics
    @top_participants = @analytics_service.top_participants(10)
  end

  def overview
    render json: @analytics_service.overview_metrics
  end

  def participants_over_time
    render json: {
      chart_data: @analytics_service.participants_over_time_data,
      chart_type: 'line',
      title: 'New Participants Over Time'
    }
  end

  def visit_distribution
    render json: {
      chart_data: @analytics_service.visit_distribution_data,
      chart_type: 'pie',
      title: 'Visit Distribution'
    }
  end

  def engagement_scores
    render json: {
      chart_data: @analytics_service.engagement_score_distribution,
      chart_type: 'pie',
      title: 'Engagement Score Distribution'
    }
  end

  def behavior_categories
    render json: {
      chart_data: @analytics_service.behavior_categories_data,
      chart_type: 'pie',
      title: 'Behavior Categories'
    }
  end

  def device_types
    render json: {
      chart_data: @analytics_service.device_type_distribution,
      chart_type: 'pie',
      title: 'Device Type Distribution'
    }
  end

  def traffic_sources
    render json: {
      chart_data: @analytics_service.traffic_sources_data,
      chart_type: 'pie',
      title: 'Traffic Sources'
    }
  end

  def geographic_distribution
    render json: {
      chart_data: @analytics_service.geographic_distribution,
      chart_type: 'bar',
      title: 'Geographic Distribution (Top 10 Countries)'
    }
  end

  def visit_frequency
    render json: {
      chart_data: @analytics_service.visit_frequency_analysis,
      chart_type: 'bar',
      title: 'Visit Frequency Analysis'
    }
  end

  def session_duration
    render json: {
      chart_data: @analytics_service.session_duration_distribution,
      chart_type: 'bar',
      title: 'Session Duration Distribution'
    }
  end

  def cohort_analysis
    period = params[:period]&.to_sym || :week
    render json: {
      chart_data: @analytics_service.cohort_analysis(period),
      chart_type: 'table',
      title: 'Cohort Analysis'
    }
  end

  def top_participants
    limit = params[:limit]&.to_i || 10
    render json: @analytics_service.top_participants(limit)
  end

  # Dashboard views
  def dashboard
    @overview_metrics = @analytics_service.overview_metrics
    @chart_configs = dashboard_chart_configs
  end

  def engagement_dashboard
    @engagement_metrics = {
      overview: @analytics_service.overview_metrics,
      top_participants: @analytics_service.top_participants(20)
    }
    @chart_configs = engagement_chart_configs
  end

  def traffic_dashboard
    @traffic_metrics = {
      sources: @analytics_service.traffic_sources_data,
      geographic: @analytics_service.geographic_distribution,
      devices: @analytics_service.device_type_distribution
    }
    @chart_configs = traffic_chart_configs
  end

  def behavior_dashboard
    @behavior_metrics = {
      categories: @analytics_service.behavior_categories_data,
      visit_distribution: @analytics_service.visit_distribution_data,
      session_duration: @analytics_service.session_duration_distribution
    }
    @chart_configs = behavior_chart_configs
  end

  private

  def set_date_range
    @start_date = params[:start_date]&.to_date || 30.days.ago.to_date
    @end_date = params[:end_date]&.to_date || Date.current
    @date_range = @start_date..@end_date
  end

  def set_analytics_service
    @analytics_service = ParticipantAnalyticsService.new(date_range: @date_range)
  end

  def dashboard_chart_configs
    [
      {
        id: 'participants-over-time',
        title: 'New Participants Over Time',
        type: 'line',
        endpoint: participant_analytics_participants_over_time_path(format: :json),
        height: '400px'
      },
      {
        id: 'visit-distribution',
        title: 'Visit Distribution',
        type: 'pie',
        endpoint: participant_analytics_visit_distribution_path(format: :json),
        height: '400px'
      },
      {
        id: 'engagement-scores',
        title: 'Engagement Score Distribution',
        type: 'pie',
        endpoint: participant_analytics_engagement_scores_path(format: :json),
        height: '400px'
      },
      {
        id: 'behavior-categories',
        title: 'Behavior Categories',
        type: 'pie',
        endpoint: participant_analytics_behavior_categories_path(format: :json),
        height: '400px'
      }
    ]
  end

  def engagement_chart_configs
    [
      {
        id: 'engagement-scores',
        title: 'Engagement Score Distribution',
        type: 'pie',
        endpoint: participant_analytics_engagement_scores_path(format: :json),
        height: '400px'
      },
      {
        id: 'visit-frequency',
        title: 'Visit Frequency Analysis',
        type: 'bar',
        endpoint: participant_analytics_visit_frequency_path(format: :json),
        height: '400px'
      },
      {
        id: 'session-duration',
        title: 'Session Duration Distribution',
        type: 'bar',
        endpoint: participant_analytics_session_duration_path(format: :json),
        height: '400px'
      }
    ]
  end

  def traffic_chart_configs
    [
      {
        id: 'traffic-sources',
        title: 'Traffic Sources',
        type: 'pie',
        endpoint: participant_analytics_traffic_sources_path(format: :json),
        height: '400px'
      },
      {
        id: 'geographic-distribution',
        title: 'Geographic Distribution',
        type: 'bar',
        endpoint: participant_analytics_geographic_distribution_path(format: :json),
        height: '400px'
      },
      {
        id: 'device-types',
        title: 'Device Types',
        type: 'pie',
        endpoint: participant_analytics_device_types_path(format: :json),
        height: '400px'
      }
    ]
  end

  def behavior_chart_configs
    [
      {
        id: 'behavior-categories',
        title: 'Behavior Categories',
        type: 'pie',
        endpoint: participant_analytics_behavior_categories_path(format: :json),
        height: '400px'
      },
      {
        id: 'visit-distribution',
        title: 'Visit Distribution',
        type: 'pie',
        endpoint: participant_analytics_visit_distribution_path(format: :json),
        height: '400px'
      },
      {
        id: 'session-duration',
        title: 'Session Duration Distribution',
        type: 'bar',
        endpoint: participant_analytics_session_duration_path(format: :json),
        height: '400px'
      }
    ]
  end
end
