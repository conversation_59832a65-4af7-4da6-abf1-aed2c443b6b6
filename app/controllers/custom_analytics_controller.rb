class CustomAnalyticsController < ApplicationController
  def data
    visits_data = Ahoy::Visit.group_by_day(:started_at).count
    events_data = Ahoy::Event.group_by_day(:time).count

    top_participants = Participant.order(average_session_duration: :desc).limit(20).as_json(only: [
      :visitor_token, :average_session_duration, :total_visits, :total_page_views
    ], methods: [:behavior_category])

    render json: {
      activity_over_time: { visits: visits_data, events: events_data },
      top_participants_by_duration: top_participants
    }
  end
end
