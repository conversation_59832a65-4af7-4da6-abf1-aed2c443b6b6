module Api
  module Peek
    class RelevantPlacesController < Pwb::ApplicationApiController
      # before_action :authenticate_user!
      def index
        @relevant_places = RelevantPlace.all
        render json: @relevant_places.map(&:as_detailed_json)
        # render 'api/v1/relevant_places/index'
      end

      def show
        @relevant_place = RelevantPlace.find_by!(uuid: params[:id])
        render 'api/v1/relevant_places/show'
      end
    end
  end
end
