module Api
  module V1
    class GenericPropertyPhotosController < Pwb::ApplicationApiController
      def index
        @generic_property_photos = GenericPropertyPhoto.all
        render 'api/v1/generic_property_photos/index'
      end

      def show
        @generic_property_photo = GenericPropertyPhoto.find_by!(uuid: params[:id])
        render 'api/v1/generic_property_photos/show'
      end
    end
  end
end
