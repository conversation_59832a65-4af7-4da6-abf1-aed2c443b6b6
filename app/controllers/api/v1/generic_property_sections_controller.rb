module Api
  module V1
    class GenericPropertySectionsController < Pwb::ApplicationApiController
      def index
        @generic_property_sections = GenericPropertySection.all
        render 'api/v1/generic_property_sections/index'
      end

      def show
        @generic_property_section = GenericPropertySection.find_by!(uuid: params[:id])
        render 'api/v1/generic_property_sections/show'
      end
    end
  end
end
