module Api
  module V1
    class GenericPropertiesController < Pwb::ApplicationApiController
      # before_action :authenticate_user!
      def index
        @generic_properties = GenericProperty.all
        render 'api/v1/generic_properties/index'
      end

      def show
        @generic_property = GenericProperty.find_by!(uuid: params[:id])
        render 'api/v1/generic_properties/show'
      end
    end
  end
end
