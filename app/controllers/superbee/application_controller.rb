# All Administrate controllers inherit from this
# `Administrate::ApplicationController`, making it the ideal place to put
# authentication logic or other before_actions.
#
# If you want to add pagination or other controller-level concerns,
# you're free to overwrite the RESTful controller actions.
module Superbee
  class ApplicationController < Administrate::ApplicationController
    before_action :authenticate_admin_admin

    # Override this to sort resources by created_at in descending order
    def scoped_resource
      resource_class.order(created_at: :desc)
    end

    private

    def authenticate_admin_admin
      # TODO: Add authentication logic here.
      if user_signed_in?
        nil if current_user.is_admin_admin?
      else
        render json: { error: 'Unauthorized' }, status: :unauthorized
        # redirect_to new_user_session_path, alert: 'You need to sign in to access this page.'
      end
    end
    # Override this value to specify the number of elements to display at a time
    # on index pages. Defaults to 20.
    # def records_per_page
    #   params[:per_page] || 20
    # end
  end
end
