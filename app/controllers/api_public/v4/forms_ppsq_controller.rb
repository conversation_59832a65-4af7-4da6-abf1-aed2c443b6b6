class ApiPublic::V4::FormsPpsqController < ApplicationController
  skip_before_action :verify_authenticity_token

  respond_to :json

  def follow_up_form
    old_comm = Communication.find_by_uuid(params[:communication_uuid])
    comm_form = params[:customerRequestForm] || {} # .to_unsafe_hash
    if old_comm
      mgmt_prefix = 'https://medo.propertywebbuilder.com/superwiser/communications'
      msg_url = "#{mgmt_prefix}/#{old_comm.id}"
      ntfy_msg = "New comm re ppsq game creation: #{msg_url}"
      Utils::NtfyUtils.new.send_public_ntfy(ntfy_msg)
      Utils::NtfyUtils.new.send_mgmt_email_note(ntfy_msg)
      old_comm.update({
                        extra_comm_details: comm_form
                      })
      render json: { success: true, comm_uuid: old_comm.uuid }, status: :ok
    else
      render json: { error: 'Communication not found' }, status: :not_found
    end
  end

  def process_form
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    comm_form = params[:customerRequestForm] || {} # .to_unsafe_hash
    form_id = params[:form_id]

    new_comm = if form_id == 'start_ppsq_a'
                 Communications::StartPpsqComm.create_from_form(
                   comm_form
                 )
               else
                 Communication.create!(
                   {
                     comm_form_name: form_id,
                     #  comm_type: 'subdomain_access_request',
                     #  subdomain_uuid: subdomain_site_guest.subdomain_uuid,
                     comm_text: ''
                     #  origin_guest_uuid: subdomain_site_guest.site_guest.uuid,
                     #  package_code: params[:package_code]
                   }
                 )
               end

    new_comm.update!({
                       request_referrer: request.referrer
                     })
    render json: { success: true, comm_uuid: new_comm.uuid }, status: :ok
    nil
  end

  def price_game_followup
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    feedback = params[:feedback] || {}
    # form_id = params[:form_id]
    game_session_id = params[:feedback][:game_session_id]

    new_comm = Communications::GameFeedbackComm.create_from_form(feedback)
    game_session = GameSession.find_by(session_guest_title: game_session_id)
    new_comm.update!(
      package_code: game_session_id,
      primary_assoc_uuid: game_session.uuid,
      secondary_assoc_type: game_session.site_visitor_token,
      session_guest_name: game_session.session_guest_name
    )

    new_comm.update!(
      request_referrer: request.referrer
    )
    render json: { success: true, comm_uuid: new_comm.uuid }, status: :ok
    nil
  end

  def game_property_feedback
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    feedback = params[:property_feedback] || {}
    game_session_id = params[:property_feedback][:game_session_id]
    guessed_price_uuid = params[:property_feedback][:guessed_price_uuid]

    new_comm = Communications::GamePropertyFeedbackComm.create_from_form(feedback)
    game_session = GameSession.find_by(session_guest_title: game_session_id)
    guessed_price = GuessedPrice.find_by(uuid: guessed_price_uuid)
    new_comm.update!(
      package_code: game_session_id,
      # primary_assoc_uuid: game_session.uuid,
      # primary_assoc_uuid is playing role guessed_price_uuid would have doneuu
      primary_assoc_uuid: guessed_price.uuid,
      secondary_assoc_type: game_session.site_visitor_token,
      session_guest_name: game_session.session_guest_name
    )

    new_comm.update!(
      request_referrer: request.referrer
    )
    render json: { success: true, comm_uuid: new_comm.uuid }, status: :ok
    nil
  end
end
