# frozen_string_literal: true

class ApiPublic::V4::FormsController < ApiPublicApplicationController
  skip_before_action :verify_authenticity_token
  # after_action :track_prospect, except: [:refresh_csrf]
  # before_action :forms_setup, except: [:refresh_csrf]
  # TODO - refactor so that even if email sending fails, prospect is created
  # maybe with ensure keyword
  # def refresh_csrf
  #   # endpoint guaranteed not to be cached
  #   # solely for obtaining a current csrf token
  #   # for the client
  #   response.headers["X-CSRF-Token"] = form_authenticity_token.to_s
  #   response.headers["X-CSRF-Param"] = "authenticity_token"
  #   render json: {
  #     success: true,
  #   }
  # end

  # def custom
  # Mar 2020 - don't think this is in use anymore (was for glenwood and h2m)
  #   @error_messages = []
  #   I18n.locale = params["contact"]["locale"] || I18n.default_locale
  #   # have a hidden field in form to pass in above
  #   # @enquiry = Message.new(params[:contact])
  #   @current_agency ||= Agency.unique_instance
  #   contact_details = params[:contact]
  #   title = contact_details[:subject]
  #   client_form = ClientForm.find params[:config_id]
  #   mailer_config = client_form.mailer_config
  #   # client_forms_container = FrontEndConfig::ClientForms.find_by_name(Apartment::Tenant.current) || {}
  #   # form_config_name = params["config_name"]
  #   # form_config = client_forms_container[form_config_name.to_sym]
  #   # delivery_email = form_config["delivery_email"].presence || "<EMAIL>"
  #   delivery_email = @current_agency.email_for_general_contact_form.presence || "<EMAIL>"
  #   create_form_message_and_contact contact_details, title, delivery_email
  #   GeneralMailer.mail_from_config(@contact, @enquiry, mailer_config).deliver_now
  #   success_message = client_form.success_text
  #   render json: {
  #     success: true,
  #     success_message: success_message,
  #   }
  # rescue StandardError => e
  #   gen_error_msg = client_form.error_text
  #   error_messages = [gen_error_msg, e]
  #   render json: {
  #     success: false,
  #     error_messages: error_messages,
  #   }
  # end

  def request_general_info
    I18n.locale = params["contact"]["locale"] || I18n.default_locale
    # have a hidden field in form to pass in above
    # if I didn't I could end up with the wrong locale
    mailer = MailBuilder.process_general_enquiry(params, request)
    mailer.deliver
    success_message = I18n.t "server.forms.success"
    render_success_json success_message
  rescue StandardError => e
    error_messages = [I18n.t("server.forms.error"), e]
    render_error_json error_messages
  end

  def request_property_info
    contact = Procedures::FormsProcedures.set_contact(params["contact"])
    current_agency ||= Agency.unique_instance
    enquiry = Procedures::FormsProcedures.set_enquiry(params["contact"], contact, current_agency, request)
    # I18n.locale = params["contact"]["locale"] || I18n.default_locale
    if params[:listings_model_name] == "rental_listings"
      listing = RentalListing.find_by_uuid(params[:listing_uuid])
    end
    if params[:listings_model_name] == "sale_listings"
      listing = SaleListing.find_by_uuid(params[:listing_uuid])
    end
    if listing
      mailer = MailBuilder.process_property_enquiry(contact, enquiry, listing)
      mailer.deliver_now
      # Procedures::FormsProcedures.update_enquiry_from_mailer(enquiry, mailer)
      Procedures::FormsProcedures.track_prospect(enquiry, contact, current_visit)
      success_message = I18n.t "server.forms.success"
      render_success_json success_message
    end
  rescue StandardError => e
    error_messages = [I18n.t("server.forms.error"), e]
    render_error_json error_messages
  end

  # def request_blog_info_zoho
  # contact = Procedures::FormsProcedures.set_contact(params["contact"])
  # enquiry = Procedures::FormsProcedures.set_enquiry(params["contact"], contact)
  #   mailer = MailBuilder.process_blog_enquiry_zoho(contact, enquiry)
  #   mailer.deliver_now
  # Procedures::FormsProcedures.update_enquiry_from_mailer(enquiry, mailer)
  # Procedures::FormsProcedures.track_prospect(enquiry, contact, current_visit)
  #   render_success_json @success_message
  # rescue StandardError => e
  #   return handle_error_json e
  # end

  # def request_general_info_zoho
  #   contact = Procedures::FormsProcedures.set_contact(params["contact"])
  #   enquiry = Procedures::FormsProcedures.set_enquiry(params["contact"], contact)
  #   mailer = Zoho::EnquiryMailer.general_enquiry_targeting_agency(contact, enquiry)
  #   #  MailBuilder.process_general_enquiry_zoho(params, contact, enquiry)
  #   mailer.deliver_now
  #   Procedures::FormsProcedures.update_enquiry_from_mailer(enquiry, mailer)
  #   Procedures::FormsProcedures.track_prospect(enquiry, contact, current_visit)
  #   render_success_json @success_message
  # rescue StandardError => e
  #   return handle_error_json e
  # end

  # def request_property_info_zoho
  #   contact = Procedures::FormsProcedures.set_contact(params["contact"])
  #   # Apr 2020 - had to update below to ensure it includes url
  #   enquiry = Procedures::FormsProcedures.set_enquiry(params["contact"], contact, request)
  #   mailer = MailBuilder.process_property_enquiry_zoho(params, contact, enquiry)
  #   mailer.deliver_now
  #   Procedures::FormsProcedures.update_enquiry_from_mailer(enquiry, mailer)
  #   Procedures::FormsProcedures.track_prospect(enquiry, contact, current_visit)
  #   render_success_json I18n.t "server.forms.success"
  # rescue StandardError => e
  #   return handle_error_json e
  # end

  # def request_favs_info_zoho
  #   # Added 23 July 2019
  #   contact = Procedures::FormsProcedures.set_contact(params["contact"])
  #   enquiry = Procedures::FormsProcedures.set_enquiry(params["contact"], contact)
  #   mailer = MailBuilder.process_favs_enquiry_zoho(params, contact, enquiry)
  #   mailer.deliver_now
  #   Procedures::FormsProcedures.update_enquiry_from_mailer(enquiry, mailer)
  #   # Would be nice to be able to record delivery success but not sure how to
  #   Procedures::FormsProcedures.track_prospect(enquiry, contact, current_visit)
  #   render_success_json @success_message
  # rescue StandardError => e
  #   return handle_error_json e
  # end

  # def request_callback_zoho
  #   contact = Procedures::FormsProcedures.set_contact(params["contact"])
  #   enquiry = Procedures::FormsProcedures.set_enquiry(params["contact"], contact)
  #   # mailer = MailBuilder.process_request_callback_zoho(params, contact, enquiry)
  #   title = "Request a callback"
  #   mailer = Zoho::EnquiryMailer.request_callback(contact, title)
  #   mailer.deliver_now
  #   Procedures::FormsProcedures.update_enquiry_from_mailer(enquiry, mailer)
  #   Procedures::FormsProcedures.track_prospect(enquiry, contact, current_visit)
  #   render_success_json @success_message
  # rescue StandardError => e
  #   return handle_error_json e
  # end

  # def setup_alert_zoho
  #   # I18n.locale = params["alertData"]["locale"] || I18n.default_locale
  #   if params["guuid"].present?
  #     current_guest = Guest.find_by_uuid(params["guuid"])
  #   else
  #     current_guest = Guest.create
  #     current_guest.reload
  #   end
  #   query_identifying_path = SearchAlertsHandler.id_path_from_params(params["alertData"]["query"])
  #   if current_guest.search_alerts.find_by_identifying_path(query_identifying_path).present?
  #     new_alert = current_guest.search_alerts.find_by_identifying_path(query_identifying_path)
  #   else
  #     new_alert = SearchAlertsHandler.create_alert_from_search(params, query_identifying_path)
  #   end
  #   new_alert.site_visitor_token = current_visit.visitor_token
  #   new_alert.guest_uuid = current_guest.uuid
  #   new_alert.save!

  #   contact = Procedures::FormsProcedures.set_contact(params["contact"])
  #   enquiry = Procedures::FormsProcedures.set_enquiry(params["contact"], contact)
  #   mailer = MailBuilder.process_setup_alert_zoho(params, contact)
  #   mailer.deliver_now
  #   Procedures::FormsProcedures.update_enquiry_from_mailer(enquiry, mailer)
  #   Procedures::FormsProcedures.track_prospect(enquiry, contact, current_visit)

  #   # mailer = MailBuilder.process_setup_alert_zoho(params, request)
  #   # mailer.deliver_now unless ENV["RAILS_ENV"] == "development"
  #   success_message = "Your alert has been setup"
  #   render json: {
  #     success: true,
  #     success_message: success_message,
  #     guuid: current_guest.uuid,
  #   }, status: :ok, head: :no_content
  # rescue StandardError => e
  #   return handle_error_json e
  # end

  private

  # def forms_setup
  #   I18n.locale = params["contact"]["locale"] || I18n.default_locale
  #   # have a hidden field in form to pass in above
  #   # if I didn't I could end up with the wrong locale
  #   @success_message = I18n.t "server.forms.success"
  # end
end
