class ApiPublic::V4::FormsHpgController < ApplicationController
  skip_before_action :verify_authenticity_token

  respond_to :json

  def create_game
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    
    # Create Communication record for price guess game creation
    new_comm = Communications::PriceGuessGameComm.create_from_form(params)
    
    new_comm.update!({
                       request_referrer: request.referrer
                     })

    # Send notification
    mgmt_prefix = 'https://medo.propertywebbuilder.com/superwiser/communications'
    msg_url = "#{mgmt_prefix}/#{new_comm.id}"
    ntfy_msg = "New price guess game creation request: #{msg_url}"
    Utils::NtfyUtils.new.send_public_ntfy(ntfy_msg)
    
    render json: { success: true, comm_uuid: new_comm.uuid }, status: :ok
    nil
  end

  # def game_property_feedback
  #   ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
  #   feedback = params[:property_feedback] || {}
  #   game_session_id = params[:property_feedback][:game_session_id]
  #   guessed_price_uuid = params[:property_feedback][:guessed_price_uuid]

  #   new_comm = Communications::GamePropertyFeedbackComm.create_from_form(feedback)
  #   game_session = GameSession.find_by(session_guest_title: game_session_id)
  #   guessed_price = GuessedPrice.find_by(uuid: guessed_price_uuid)
  #   new_comm.update!(
  #     package_code: game_session_id,
  #     # primary_assoc_uuid: game_session.uuid,
  #     # primary_assoc_uuid is playing role guessed_price_uuid would have doneuu
  #     primary_assoc_uuid: guessed_price.uuid,
  #     secondary_assoc_type: game_session.site_visitor_token,
  #     session_guest_name: game_session.session_guest_name
  #   )

  #   new_comm.update!(
  #     request_referrer: request.referrer
  #   )
  #   render json: { success: true, comm_uuid: new_comm.uuid }, status: :ok
  #   nil
  # end
end
