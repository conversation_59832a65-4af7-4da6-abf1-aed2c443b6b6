module ApiPublic
  module V4
    class SoldTransactionsController < ApplicationController
      def predictions_for_outcode
        sold_transaction_prices = SoldTransactionEpc.confirmed_st_epcs
        render json: {
          sold_transaction_prices: sold_transaction_prices.as_json(
            # include: {
            #   sold_transaction: {
            #     only: %i[
            #       id sold_date
            #     ],
            #     methods: %i[
            #       postal_code latitude longitude
            #       formatted_sold_price short_formatted_sold_price
            #       st_predicted_price st_predicted_price_off_by
            #       st_predicted_price_b st_predicted_price_off_by_b
            #       st_predicted_price_c st_predicted_price_off_by_c
            #     ]
            #   }
            # },
            only: %i[
              id total_floor_area number_heated_rooms
              number_habitable_rooms construction_age_band current_energy_rating
              potential_energy_rating current_energy_efficiency potential_energy_efficiency
              tenure
            ],
            methods: %i[
              formatted_sold_price
              st_predicted_price_a st_predicted_price_off_by_a
              avm_features
              total_floor_area number_heated_rooms
              number_habitable_rooms construction_age_band current_energy_rating
              potential_energy_rating current_energy_efficiency potential_energy_efficiency
              tenure
            ]
          )
        }
      end

      def full_for_outcode
        confirmed_st_epcs = SoldTransactionEpc.confirmed_st_epcs
        render json: {
          confirmed_st_epcs: confirmed_st_epcs.as_json(
            include: {
              sold_transaction: {
                include: {
                  listing_photos: {
                    methods: [:image_details],
                    only: %i[uuid sort_order]
                  }
                },
                methods: %i[
                  st_predicted_price st_predicted_price_off_by
                  postal_code latitude longitude
                  formatted_sold_price short_formatted_sold_price
                ],
                only: %i[
                  id uuid new_home sold_date sold_transaction_reference
                  sold_transaction_title
                ]
              },
              epc_detail: {
                # except: %i[uuid]
                only: %i[total_floor_area inspection_date]
              }
            },
            only: %i[
              association_certainty epc_address st_address st_epc_uprn
              st_epc_outcode st_epc_postcode st_epc_latitude st_epc_longitude
              id uuid
            ]
          )
        }
      end

      def index_synthetic
        @generic_properties_with_transactions = GenericProperty.all.includes(:sold_transactions)

        render json: @generic_properties_with_transactions.as_json(
          include: {
            sold_transactions: {
              include: {
                listing_photos: {
                  methods: [:image_details],
                  only: %i[uuid sort_order]
                }
              },
              methods: [:formatted_sold_price],
              only: %i[uuid new_home sold_date sold_transaction_reference sold_transaction_title]
            }
          },
          only: %i[
            id generic_property_reference generic_property_title no_of_bedrooms prop_type_key street_name postal_code city
          ]
        )
      end

      def index_real_for_cluster
        @geo_cluster = GeoCluster.last # GeoCluster.find_by!(uuid: params[:geo_cluster_id])
        # @realty_assets_with_transactions = RealtyAsset.includes(:sold_transactions).all
        @realty_assets_with_transactions = RealtyAsset.where(
          has_sold_transactions: true
        ).includes(:sold_transactions)

        render json: @geo_cluster.as_json(
          include: {
            postcode_areas: { only: %i[uuid postal_code] },
            sold_transactions: {
              include: {
                listing_photos: {
                  methods: [:image_details],
                  only: %i[uuid sort_order]
                }
              },
              methods: [:formatted_sold_price],
              only: %i[uuid new_home sold_date sold_transaction_reference sold_transaction_title]
            }
          }
          # only: %i[reference prop_type_key count_bedrooms title street_address city postal_code]
        )
      end

      def index_real
        # @realty_assets_with_transactions = RealtyAsset.includes(:sold_transactions).all
        @realty_assets_with_transactions = RealtyAsset.where(
          has_sold_transactions: true
        ).includes(:sold_transactions)

        render json: @realty_assets_with_transactions.as_json(
          include: {
            sold_transactions: {
              include: {
                listing_photos: {
                  methods: [:image_details],
                  only: %i[uuid sort_order]
                }
              },
              methods: [:formatted_sold_price],
              only: %i[uuid new_home sold_date sold_transaction_reference sold_transaction_title]
            }
          },
          only: %i[reference prop_type_key count_bedrooms title street_address city postal_code]
        )
      end

      # def index
      #   @sold_transactions = SoldTransaction.includes(:listing_photos, :realty_asset).all
      #   render json: @sold_transactions.as_json(
      #     include: {
      #       listing_photos: {
      #         methods: [:image_details],
      #         only: %i[uuid sort_order]
      #       },
      #       realty_asset: {
      #         only: %i[reference count_bedrooms title street_address city postal_code]
      #       }
      #     },
      #     methods: [:formatted_sold_price],
      #     only: %i[uuid new_home sold_date sold_transaction_reference sold_transaction_title]
      #   )
      # end

      def show
        @sold_transaction = SoldTransaction.includes(:listing_photos, :realty_asset)
                                           .find_by!(uuid: params[:id])
        render json: @sold_transaction.as_json(
          include: {
            listing_photos: {
              methods: [:image_details],
              only: %i[uuid sort_order]
            },
            realty_asset: {
              only: %i[reference title street_address city
                       postal_code count_bedrooms count_bathrooms]
            }
          },
          methods: [:formatted_sold_price],
          only: %i[uuid sold_transaction_reference sold_transaction_title
                   sold_transaction_description]
        )
      end
    end
  end
end
