# frozen_string_literal: true

class ApiPublic::V4::Dossiers::PriceEstimatesController < ApplicationController
  skip_before_action :verify_authenticity_token
  respond_to :json

  before_action :load_dossier
  before_action :load_price_estimate, only: %i[show update destroy]

  # GET /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates
  def index
    @price_estimates = @realty_dossier.price_estimates.kept.order('created_at DESC')

    render json: {
      price_estimates: @price_estimates.as_json(
        only: %w[uuid estimated_price_cents estimate_currency estimate_title
                 estimate_text estimator_name is_ai_estimate is_for_sale_listing
                 is_for_rental_listing percentage_above_or_below created_at updated_at],
        methods: %w[formatted_estimated_price]
      )
    }
  end

  # GET /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid
  def show
    render json: {
      price_estimate: @price_estimate.as_json(
        only: %w[uuid estimated_price_cents price_at_time_of_estimate_cents
                 estimate_currency estimate_title estimate_text estimate_vicinity
                 estimate_postal_code estimate_latitude_center estimate_longitude_center
                 estimator_name is_ai_estimate is_for_sale_listing is_for_rental_listing
                 is_protected percentage_above_or_below count_sold_transactions_shown
                 estimate_details created_at updated_at],
        methods: %w[formatted_estimated_price formatted_price_at_time_of_estimate]
      )
    }
  end

  # POST /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates
  def create
    @price_estimate = @realty_dossier.price_estimates.build(price_estimate_params)
    @price_estimate.agency_tenant_uuid = @realty_dossier.agency_tenant_uuid

    if @price_estimate.save
      render json: {
        price_estimate: @price_estimate.as_json(
          only: %w[uuid estimated_price_cents estimate_currency estimate_title
                   estimate_text estimator_name is_ai_estimate created_at],
          methods: %w[formatted_estimated_price]
        ),
        message: 'Price estimate created successfully'
      }, status: :created
    else
      render json: {
        errors: @price_estimate.errors.full_messages,
        message: 'Failed to create price estimate'
      }, status: :unprocessable_entity
    end
  end

  # PUT/PATCH /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid
  def update
    if @price_estimate.update(price_estimate_params)
      render json: {
        price_estimate: @price_estimate.as_json(
          only: %w[uuid estimated_price_cents estimate_currency estimate_title
                   estimate_text estimator_name is_ai_estimate updated_at],
          methods: %w[formatted_estimated_price]
        ),
        message: 'Price estimate updated successfully'
      }
    else
      render json: {
        errors: @price_estimate.errors.full_messages,
        message: 'Failed to update price estimate'
      }, status: :unprocessable_entity
    end
  end

  # DELETE /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid
  def destroy
    if @price_estimate.discard
      render json: {
        message: 'Price estimate deleted successfully'
      }
    else
      render json: {
        errors: @price_estimate.errors.full_messages,
        message: 'Failed to delete price estimate'
      }, status: :unprocessable_entity
    end
  end

  private

  def load_dossier
    @realty_dossier = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])

    unless @realty_dossier
      render json: { error: 'Dossier not found' }, status: :not_found
      return false
    end

    true
  end

  def load_price_estimate
    @price_estimate = @realty_dossier.price_estimates.kept.find_by_uuid(params[:uuid])

    unless @price_estimate
      render json: { error: 'Price estimate not found' }, status: :not_found
      return false
    end

    true
  end

  def price_estimate_params
    params.require(:price_estimate).permit(
      :estimated_price_cents, :price_at_time_of_estimate_cents, :estimate_currency,
      :estimate_title, :estimate_text, :estimate_vicinity, :estimate_postal_code,
      :estimate_latitude_center, :estimate_longitude_center, :estimator_name,
      :is_ai_estimate, :is_for_sale_listing, :is_for_rental_listing, :is_protected,
      :percentage_above_or_below, :count_sold_transactions_shown, :listing_uuid,
      :user_uuid, :scoot_uuid, estimate_details: {}
    )
  end
end
