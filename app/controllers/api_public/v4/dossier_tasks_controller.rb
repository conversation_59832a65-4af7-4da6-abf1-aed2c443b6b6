module ApiPublic::V4
  class DossierTasksController < ApplicationController
    skip_before_action :verify_authenticity_token
    respond_to :json

    before_action :set_dossier
    before_action :set_task, only: %i[show update destroy]

    # GET /dossiers/:realty_dossier_uuid/tasks
    def index
      @tasks = @dossier.dossier_tasks.kept.order(:due_date)
      render 'api_public/v4/dossier_tasks/index'
    end

    # GET /dossiers/:realty_dossier_uuid/tasks/:uuid
    def show
      render 'api_public/v4/dossier_tasks/show'
    end

    # POST /dossiers/:realty_dossier_uuid/tasks
    def create
      @task = @dossier.dossier_tasks.new(task_params)
      @task.title = params[:text]
      @task.agency_tenant_uuid = AgencyTenant.unique_tenant.uuid # ActsAsTenant.current_tenant&.uuid
      @task.comparison_id = params['comparison_id']
      @task.is_primary = params['is_primary']
      @task.pictures = params['pictures']
      if @task.save!
        render 'api_public/v4/dossier_tasks/show', status: :created
      else
        render json: { errors: @task.errors.full_messages }, status: :unprocessable_entity
      end
    end

    # PUT/PATCH /dossiers/:realty_dossier_uuid/tasks/:uuid
    def update
      if @task.update(task_params)
        render 'api_public/v4/dossier_tasks/show'
      else
        render json: { errors: @task.errors.full_messages }, status: :unprocessable_entity
      end
    end

    # DELETE /dossiers/:realty_dossier_uuid/tasks/:uuid
    def destroy
      @task.discard
      head :no_content
    end

    private

    def set_dossier
      @dossier = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])
      render json: { error: 'Dossier not found' }, status: :not_found unless @dossier
    end

    def set_task
      @task = @dossier.dossier_tasks.find_by_uuid(params[:uuid])
      render json: { error: 'Task not found' }, status: :not_found unless @task
    end

    def task_params
      params.require(:dossier_task).permit(:title, :description, :due_date, :completed, :comparison_id, :is_primary, pictures: %i[url name uuid])
    end
  end
end
