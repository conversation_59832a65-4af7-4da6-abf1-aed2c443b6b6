# frozen_string_literal: true

require 'nokogiri'

class ApiPublic::V4::Spp::SppController < ApiPublicApplicationController
  include RealtyScrapers::SharedScraperHelpers

  # before_action :set_page_dashboard, only: [:page_preview_html, :page_edit_html]

  # Jan 2022 - TODO: remove below and perhaps even require auth for spp creation
  skip_before_action :verify_authenticity_token, only: :create_from_html

  # def custom_page
  #   # Dec 2021 - only serves to allow me to demo spp page
  #   # from quasar client
  #   @content_locale = I18n.locale
  #   @current_spp_listing = RentalListing.last
  #   @current_spp_page = @current_spp_listing.find_or_create_primary_rendering_page
  #   return render "api_public/v4/spp/show_spp.json"
  # end

  def show
    listings_grouping = params[:listings_grouping] || 'for-sale'

    @current_spp_listing = if listings_grouping === 'for-sale'
                             SaleListing.find_by_uuid(params[:listing_uuid])
                           else
                             RentalListing.find_by_uuid(params[:listing_uuid])
                           end
    if @current_spp_listing
      # return the custom rendering page too:
      @current_spp_page = @current_spp_listing.find_or_create_primary_rendering_page
      # find_or_create_primary_rendering_page above goes through to PageCreator
      # and calls create_as_spp on there which is where the page sections are added
      render 'api_public/v4/spp/show_spp.json' # json_template_name
    else
      render json: {
        success: false,
        error_messages: 'Listing not found'
      }, status: 404, head: :no_content
    end
  end

  def create_from_html
    import_url = params[:import_url]

    unless import_url.present?
      # TODO: - validate url
      return render json: { error: 'Please provide a url' }, status: 500
    end

    raw_import_html = params[:raw_import_html]
    unless raw_import_html.present?
      # TODO: - validate url
      return render json: { error: 'Please provide html' }, status: 500
    end

    # html_validation_errors = Nokogiri::HTML.parse(raw_import_html).validate
    # if html_validation_errors.length > 0
    #   return render json: { error: "Invalid html" }, status: 500
    # end

    begin
      noko_doc = Nokogiri.HTML(raw_import_html)
    rescue Exception => e
      # puts "Couldn't read \"#{raw_import_html}\": #{e}"
      return render json: { error: 'Corrupt html' }, status: 500
    end

    return render json: { error: 'Invalid html' }, status: 500 if noko_doc.title.blank?

    api_wrapper = ::ApiWrappers::ScraperWrapper.new
    scraper_result = api_wrapper.get_single_listing_from_html_object(import_url, raw_import_html, 'sales')
    if scraper_result && scraper_result[:success]
      current_external_model = External::GenericListingWithAsset.new(scraper_result, 'sales')
    else
      error_message = scraper_result[:error_message] || 'Unable to parse html'
      return render json: { error: error_message }, status: 500
    end
    # retriever = ListingsRetrievers::ScraperRetriever.new(import_url)
    # current_external_model = retriever.retrieve_listing_with_asset_from_html(raw_import_html)
    extModUtil = ExternalModelUtils.new(current_external_model)
    @current_spp_listing = extModUtil.as_saved_spp

    @current_spp_page = @current_spp_listing.find_or_create_primary_rendering_page
    render 'api_public/v4/spp/show_spp.json'
  end

  def create_from_url
    import_url = params[:import_url]

    return render json: { error: 'Please provide a url' }, status: 500 unless import_url.present?

    include_trailing_slash = true
    remove_query = true
    import_uri_result = get_uri_from_url(import_url, remove_query, include_trailing_slash)
    return render json: { error: 'Please provide a valid url' }, status: 500 unless import_uri_result[:success]

    import_uri = import_uri_result[:uri]
    if ['www.immobilienscout24.de', 'immobilienscout24.de',
        'www.zillow.com', 'www.redfin.com', 'zillow.com', 'redfin.com'].include? import_uri.host
      # TODO: - more robust way of figuring out if source html required
      return render json: { error: 'Source html required' }, status: 418
    end

    # "https://www.rightmove.co.uk/properties/115881926"
    # important - as import_url is not a required field, if I run
    # query below with value of nil I will get back the first listing
    # @current_spp_listing = SaleListing.find_by_import_url(import_url.delete_suffix("/"))
    # if @current_spp_listing.blank?
    # end
    # Jan 2022 - for spps I will always create a new one even if listing
    # has been imported before..
    # TODO - think of a way of making use of previously imported listings..
    # retriever = ListingsRetrievers::ScraperRetriever.new(import_url)
    # current_external_model = retriever.retrieve_listing_with_asset
    api_wrapper = ::ApiWrappers::ScraperWrapper.new
    scraper_result = api_wrapper.get_single_listing_from_url(import_url, 'sales')
    if scraper_result && scraper_result[:success]
      current_external_model = External::GenericListingWithAsset.new(scraper_result, 'sales')
    else
      error_message = scraper_result[:error_message] || 'Source html required for this listing'
      return render json: { error: error_message }, status: 418
    end
    if current_external_model.present? && current_external_model.title == 'Title not found'
      # TODO: - have more robust way of deciding if scraping has failed...
      return render json: { error: 'Please enter source html for this listing' }, status: 418
    end

    extModUtil = ExternalModelUtils.new(current_external_model)
    @current_spp_listing = extModUtil.as_saved_spp

    @current_spp_page = @current_spp_listing.find_or_create_primary_rendering_page
    render 'api_public/v4/spp/show_spp.json'
  end
end
