module ApiPublic
  module V4
    class ChartsDataController < ApplicationController
      # def old_charts_data
      #   # @sold_transactions = SoldTransactionEpc.includes(:sold_transaction).all

      #   # Cache data for 1 hour (adjust the duration as needed)
      #   @sold_transactions = Rails.cache.fetch(
      #     'sold_transactions', expires_in: 1.hour
      #   ) do
      #     Rails.logger.info 'Cache miss: Generating chart data...'
      #     SoldTransactionEpc.includes(:sold_transaction).all
      #   end
      #   @chart_data = []
      #   # Rails.cache.delete('sorted_floor_area_chart_data')
      #   if params[:data_source_name] == 'sold_data_scatter_charts'
      #     @chart_data = Rails.cache.fetch(
      #       'sorted_floor_area_chart_data', expires_in: 1.hour
      #     ) do
      #       floor_area_chart_data = @sold_transactions.map do |transaction|
      #         [
      #           transaction.total_floor_area,
      #           transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f,
      #           'label'
      #         ]
      #       end
      #       floor_area_chart_data.sort_by { |data| data[0].to_f }
      #     end
      #   end

      #   if params[:data_source_name] == 'sold_data_by_habitable_rooms'
      #     @chart_data = Rails.cache.fetch(
      #       'sold_data_by_habitable_rooms', expires_in: 1.minute
      #     ) do
      #       rooms_chart_data = @sold_transactions.map do |transaction|
      #         [
      #           transaction.number_habitable_rooms,
      #           transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f
      #         ]
      #       end
      #       rooms_chart_data.sort_by { |data| data[0].to_f }
      #     end
      #   end

      #   chart_setup = {}
      #   if params[:data_source_name] == 'sale_v_predicted_price'
      #     raw_series_data = sale_v_predicted_price_data(
      #       sold_transactions: @sold_transactions.limit(10)
      #     )
      #     chart_setup = build_time_series_data(
      #       series_data(raw_series_data, name: 'Actual Vs. Predicted Prices'),
      #       chart_options(
      #         chart_type: 'scatter',
      #         x_label: 'Predicted Price',
      #         y_label: 'Actual Price',
      #         x_min: 50_000,
      #         y_min: 50_000,
      #         title: 'Actual House Prices Vs. Predicted Prices'
      #       )
      #     )
      #   end

      #   render json: {
      #     chart_setup:,
      #     chart_data: @chart_data,
      #     chart_data_name: params[:data_source_name]
      #   }
      # end

      # def time_series_data
      #   chart_setup = build_time_series_data(
      #     series_data(price_data_over_years), chart_options
      #   )
      #   render json: {
      #     chart_setup:
      #   }
      # end

      def charts_data
        sold_transaction_epcs = fetch_sold_transactions
        chart_service = Charts::ChartsDataService.new(
          sold_transaction_epcs: sold_transaction_epcs
        )

        # @chart_data = chart_service.get_chart_data(params[:data_source_name])
        chart_setup = {}

        chart_setup = chart_service.get_sale_v_predicted_price_data if params[:data_source_name] == 'sale_v_predicted_price'
        chart_setup = chart_service.get_price_by_floor_area_data if params[:data_source_name] == 'price_by_floor_area'

        render json: {
          chart_setup:,
          # chart_data: @chart_data,
          chart_data_name: params[:data_source_name]
        }
      end

      def charts_data_for_cluster
        postcode_area_cluster = GeoCluster.find_by_uuid(
          params[:postcode_cluster_uuid]
        )
        # GeoCluster.last.includes(postcode_geo_clusters: { postcode_areas: { sold_transactions: :sold_transaction_epcs } })
        # debugger
        sold_transaction_epcs = postcode_area_cluster.sold_transaction_epcs # fetch_sold_transaction_epcs
        chart_service = Charts::ChartsDataService.new(
          sold_transaction_epcs: sold_transaction_epcs
        )

        # @chart_data = chart_service.get_chart_data(params[:data_source_name])
        chart_setup = {}

        max_items_to_return = 50
        if params[:data_source_name] == 'sale_v_predicted_price'
          chart_setup = chart_service.get_sale_v_predicted_price_data(
            max_items_to_return
          )
        end
        if params[:data_source_name] == 'price_by_floor_area'
          chart_setup = chart_service.get_price_by_floor_area_data(
            max_items_to_return
          )
        end

        render json: {
          chart_setup:,
          # chart_data: @chart_data,
          chart_data_name: params[:data_source_name]
        }
      end

      private

      def fetch_sold_transactions
        # Rails.cache.delete('sold_transactions')
        Rails.cache.fetch('sold_transactions', expires_in: 1.hour) do
          Rails.logger.info 'Cache miss: Generating chart data...'
          SoldTransactionEpc.includes(:sold_transaction).all
        end
      end

      # def build_time_series_data(series, options)
      #   {
      #     series:,
      #     options:
      #   }
      # end

      # def series_data(data_points, name: 'Price')
      #   [{
      #     name:,
      #     data: data_points
      #   }]
      # end

      # def marker_options
      #   {
      #     size: 10,
      #     # colors: 'undefined',
      #     strokeColors: '#fff',
      #     strokeWidth: 2,
      #     strokeOpacity: 0.9,
      #     strokeDashArray: 0,
      #     fillOpacity: 1,
      #     discrete: [],
      #     shape: 'circle',
      #     offsetX: 0,
      #     offsetY: 0,
      #     # onClick: undefined,
      #     # onDblClick: undefined,
      #     showNullDataPoints: true,
      #     hover: {
      #       # size: undefined,
      #       sizeOffset: 3
      #     }
      #   }
      # end

      # def chart_options(
      #   chart_type: 'line',
      #   title: 'Average House Prices Over Years',
      #   y_label: 'Price (USD)', x_label: 'Year',
      #   y_min: 'undefined', x_min: 'undefined'
      # )
      #   xaxis = { title: { text: x_label }, tickAmount: nil }
      #   yaxis = { title: { text: y_label } }

      #   xaxis[:min] = x_min if x_min != 'undefined'
      #   yaxis[:min] = y_min if y_min != 'undefined'

      #   {
      #     markers: marker_options,
      #     chart: {
      #       type: chart_type
      #       # selection: {
      #       #   enabled: true,
      #       #   type: 'x',
      #       #   fill: {
      #       #     color: '#24292e',
      #       #     opacity: 0.1
      #       #   },
      #       #   stroke: {
      #       #     width: 1,
      #       #     dashArray: 3,
      #       #     color: '#24292e',
      #       #     opacity: 0.4
      #       #   },
      #       #   xaxis: {
      #       #     min: 'undefined',
      #       #     max: 'undefined'
      #       #   },
      #       #   yaxis: {
      #       #     min: 'undefined',
      #       #     max: 'undefined'
      #       #   }
      #       # }
      #     },
      #     # xaxis: { type: 'category' },
      #     xaxis:,
      #     yaxis:,
      #     title: { text: title },
      #     "plotOptions": {
      #       "scatter": {
      #         "marker": {
      #           "radius": 3 # Adjust the size of the data points here
      #         }
      #       }
      #     }
      #   }
      # end

      # # these are the different data points for the time series
      # def price_data_over_years(years = %w[2015 2016 2017 2018 2019 2020])
      #   years.map.with_index do |year, i|
      #     { x: year, y: 250_000 + (i * 20_000) }
      #   end
      # end

      # def sale_v_predicted_price_data(
      #   sold_transactions: nil
      # )
      #   sold_transactions.map do |transaction|
      #     sold_price = transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_i
      #     predicted_price = transaction.st_predicted_price_a.gsub(/[^\d.]/, '').to_i
      #     fill_color = 'orange'
      #     fill_color = 'blue' if predicted_price > sold_price
      #     predicted_price_difference = predicted_price - sold_price
      #     datapoint_size = 22
      #     {
      #       size: datapoint_size,
      #       marker: {
      #         size: 6,
      #         fillColor: '#FFA500', # Highlight color for the annotation marker
      #         strokeColor: '#000000',
      #         strokeWidth: 2
      #       },
      #       dataItemLabel: transaction.sold_transaction.st_long_address,
      #       label: {
      #         borderColor: '#FFA500',
      #         borderWidth: 1,
      #         text: 'High Predicted Price', # Annotation label
      #         style: {
      #           fontSize: '12px',
      #           fontWeight: 'bold',
      #           background: '#FFFACD',
      #           color: '#000'
      #         }
      #       },
      #       x: predicted_price,
      #       y: sold_price,
      #       fillColor: fill_color,
      #       predictedPriceDifference: predicted_price_difference,
      #       predictedPriceA: transaction.st_predicted_price_a,
      #       soldPrice: transaction.sold_transaction.formatted_sold_price,
      #       # label: 'label',
      #       SoldTransactionEpcId: transaction.id,
      #       SoldTransactionId: transaction.sold_transaction.id
      #     }
      #   end

      #   # price_data.sort_by { |data| data[0].to_f }
      # end
    end
  end
end
