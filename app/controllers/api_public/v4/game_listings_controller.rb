class ApiPublic::V4::GameListingsController < ApplicationController
  skip_before_action :verify_authenticity_token
  respond_to :json

  def show_for_sale
    @sale_listing = SaleListing.find_by_uuid(params[:sale_listing_uuid])
    if @sale_listing.nil?
      render json: { error: 'Listing not found' }, status: :not_found
    else
      render 'api_public/v4/game_listings/show_for_sale'
    end
  end

  # 13 july 2025 - above was a bit dumb as it did not allow me to retrieve
  # realty_game_listing data around the sale_listing

  def show_realty_game_listing
    @realty_game_listing = RealtyGameListing.find_by_uuid(params[:realty_game_listing_uuid])
    @sale_listing = @realty_game_listing&.sale_listing
    #  SaleListing.find_by_uuid(params[:sale_listing_uuid])
    if @sale_listing.nil?
      render json: { error: 'rgl not found' }, status: :not_found
    else
      @realty_game_listing.update_store_attributes_from_listing! if @realty_game_listing.gl_title_atr.blank? && @realty_game_listing.gl_image_url_atr.blank?
      render 'api_public/v4/game_listings/show_realty_game_listing'
    end
    # file_path = Rails.root.join('app/views/', 'api_public/v4/purchase_evaluations/show.json')
    # render json: File.read(file_path), status: :ok
  end
end
