module ApiPublic
  module V4
    class PostcodeClustersController < ApplicationController
      respond_to :json

      # below so I can preview sample data to feed to an LLM
      def llm_view
        @postcode_area_cluster = GeoCluster.find_by_uuid(
          params[:postcode_cluster_uuid]
        )
        render '/api_public/v4/postcode_clusters/llm_view' # This will render the index.json.jbuilder template
      end

      def show
        @postcode_area_cluster = GeoCluster.find_by_uuid(
          params[:postcode_cluster_uuid]
        )
        render '/api_public/v4/postcode_clusters/show' # This will render the index.json.jbuilder template
      end

      def index
        @postcode_area_clusters = GeoCluster.where('UPPER(cluster_outcode) = ?', params[:outcode_id].upcase)
                                            .order(created_at: :desc)
        render '/api_public/v4/postcode_clusters/index' # This will render the index.json.jbuilder template
      end
    end
  end
end
