# frozen_string_literal: true

class ApiPublic::V4::ComponentDataController < ApiPublicApplicationController
  def show
    # # Will need to figure out some way of returning
    # page_slug = "regular_sales_search_page"
    # @page = Page.find_by_localized_slug "en", page_slug
    # # @page = Page.find_by_localized_slug(locale, page_slug)
    # @content_to_show = []
    # search_widget_section = @page.page_sections.find_by_config_slug(:searchWidgetSection) # target_page.page_sections.first

    # if search_widget_section
    #   @search_widget = search_widget_section.search_widget
    # end
    @search_widget = SearchWidget.find_by(search_type: 'regular_rentals')
    # @search_widget = SearchWidget.find_by(search_type: "regular_sales")
    render 'show.json' # json_template_name
  end
end
