class ApiPublic::V4::DossiersController < ApplicationController
  skip_before_action :verify_authenticity_token
  respond_to :json

  # def price_guess
  #   incoming_subdomain = request.subdomain.presence || 'default'
  #   @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)
  #   @realty_dossier = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])

  #   @sale_listing = @realty_dossier&.primary_sale_listing
  #   #  SaleListing.find_by_uuid(params[:sale_listing_uuid])
  #   @close_uprn_details_json = {}
  #   if @sale_listing&.realty_asset&.close_uprn_details_json
  #     # TODO: - don't return close_uprn_details_json if uprn exists and is accurate
  #     @close_uprn_details_json = @sale_listing.realty_asset.close_uprn_details_json
  #   end
  #   if @sale_listing.nil?
  #     render json: { error: 'Listing not found' }, status: :not_found
  #   else
  #     render 'api_public/v4/realty_dossiers/price_guess'
  #   end
  # end

  # # TODO: - remove below:
  # def show_comparison
  #   #  I think I created this to get some data I could pass to the LLM:
  #   @realty_dossier = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])
  #   # @dossier_asset_to_compare = @realty_dossier&.secondary_dossier_assets&.find_by_uuid(
  #   #   params[:secondary_asset_uuid]
  #   # )
  #   @dossier_asset_to_compare = DossierAsset.find_by_uuid(
  #     params[:secondary_asset_uuid]
  #   )
  #   #  if above asset does not belong to realty_dossier
  #   # I might go ahead and create a realty_dossier_asset that belongs to it

  #   @sale_listing = @realty_dossier&.primary_sale_listing
  #   #  SaleListing.find_by_uuid(params[:sale_listing_uuid])
  #   @close_uprn_details_json = {}
  #   if @sale_listing&.realty_asset&.close_uprn_details_json
  #     # TODO: - don't return close_uprn_details_json if uprn exists and is accurate
  #     @close_uprn_details_json = @sale_listing.realty_asset.close_uprn_details_json
  #   end
  #   if @sale_listing.nil?
  #     render json: { error: 'Listing not found' }, status: :not_found
  #   else
  #     render 'api_public/v4/realty_dossiers/show_comparison'
  #   end
  # end

  def show
    incoming_subdomain = request.subdomain.presence || 'default'
    @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)
    @realty_dossier = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])

    # I am fine with below not getting applied to subdomains
    # that have not been assigned a scoot.  It does mean though
    # that if someone finds an unassigned subdomain and thinks to
    # construct some urls that match this, below check will be bypassed
    if @scoot.present?
      # !@scoot.safe_dossier_ids.include?(@realty_dossier.id)
      unless @scoot.has_access_to_dossier?(@realty_dossier)
        render json: { error: 'Access error' }, status: :unauthorized
        return
      end

      htoc_access_code = request.headers['X-User-Access-Code']
      # when I put a debugger here I often get a nil value for htoc_access_code
      # Suspect might me for preflight calls
      # Or perhaps the SSR call....
      unless @scoot.access_token == htoc_access_code
        render json: { error: 'Token error' }, status: :unauthorized
        return
      end
    end

    @sale_listing = @realty_dossier&.primary_sale_listing
    #  SaleListing.find_by_uuid(params[:sale_listing_uuid])
    @close_uprn_details_json = {}
    if @sale_listing&.realty_asset&.close_uprn_details_json
      # TODO: - don't return close_uprn_details_json if uprn exists and is accurate
      @close_uprn_details_json = @sale_listing.realty_asset.close_uprn_details_json
    end
    if @sale_listing.nil?
      render json: { error: 'Listing not found' }, status: :not_found
    else
      render 'api_public/v4/realty_dossiers/show'
    end
    # file_path = Rails.root.join('app/views/', 'api_public/v4/purchase_evaluations/show.json')
    # render json: File.read(file_path), status: :ok
  end

  def list_for_superwiser
    # TODO: - secure with auth
    @realty_dossiers = RealtyDossier.order('id desc')
    # little experiment below
    # @realty_dossiers_without_primary_listing = []
    # @realty_dossiers_with_primary_listing = []
    # @realty_dossiers.each do |realty_dossier|
    #   if realty_dossier.primary_sale_listing.nil?
    #     @realty_dossiers_without_primary_listing << realty_dossier
    #   else
    #     @realty_dossiers_with_primary_listing << realty_dossier
    #   end
    # end
    render 'api_public/v4/realty_dossiers/list_for_superwiser'
  end

  def show_screenshot_images
    return unless load_listing

    process_otm_photos(@target_sale_listing.listing_photos)
    # Support batching of up to limit photos via query params
    offset = params[:offset].to_i
    limit = params[:limit].present? ? params[:limit].to_i : @target_sale_listing.listing_photos.kept.count
    # @acceptable_images = @target_sale_listing.listing_photos.kept.offset(offset).limit(limit)
    acceptable_image_ids = @target_sale_listing.ordered_batch_photo_ids(offset: offset, limit: limit)
    @acceptable_images = RealtyAssetPhoto.where(id: acceptable_image_ids)
    # @acceptable_images = @target_sale_listing.listing_photos.kept.select(&:flag_is_parsed_by_llm)
    # .kept.offset(offset).limit(limit)
    render 'api_public/v4/realty_dossiers/show_image'
  end

  def show_wrong_screenshot_images
    return unless load_listing

    # process_otm_photos(@target_sale_listing.listing_photos)
    @acceptable_images = @target_sale_listing.plan_b_images
    # @target_sale_listing.listing_photos.each do |listing_photo|
    #   @acceptable_images << listing_photo if listing_photo.photo_title == 'wrong'
    # end

    render 'api_public/v4/realty_dossiers/show_image'
  end

  def show_discarded_screenshot_images
    return unless load_listing

    process_otm_photos(@target_sale_listing.listing_photos)
    @acceptable_images = @target_sale_listing.listing_photos.discarded

    render 'api_public/v4/realty_dossiers/show_image'

    #     # Would  be nice to be able to render a image directly, but /wkhtmltoimage
    #     # seems old and no longer trustworthy
    #     #
    #     # # Generate HTML content
    #     # html_content = render_to_string(
    #     #   template: 'api_public/v4/realty_dossiers/show_image',
    #     #   layout: false,
    #     #   locals: {
    #     #     realty_dossier: @realty_dossier,
    #     #     target_sale_listing: @target_sale_listing,
    #     #     acceptable_images: @acceptable_images,
    #     #     edit_mode: @edit_mode,
    #     #     selected_images: @selected_images
    #     #   }
    #     # )

    #     # # Configure IMGKit
    #     # kit = IMGKit.new(html_content, quality: 100, width: 1200)

    #     # # Add styles
    #     # kit.stylesheets << Rails.root.join('app', 'assets', 'stylesheets', 'application.css')

    #     # # Generate image
    #     # img = kit.to_img(:png)

    #     # # Send image as response
    #     # send_data img, type: 'image/png', disposition: 'inline'
  end

  private

  def load_listing
    @realty_dossier = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])
    listing_uuid = params[:listing_uuid]
    @target_sale_listing = nil

    if listing_uuid == @realty_dossier.primary_sale_listing.uuid
      @target_sale_listing = @realty_dossier.primary_sale_listing
    else
      @realty_dossier.secondary_dossier_assets.each do |asset|
        if asset.default_sale_listing.uuid == listing_uuid
          @target_sale_listing = asset.default_sale_listing
          break
        end
      end
    end

    unless @target_sale_listing
      render json: { error: 'Listing not found' }, status: :not_found
      return false
    end

    true
  end
  # def filter_acceptable_images(listing)
  #   listing.listing_photos.kept.reject { |pic| pic.image_details['url'].include?('480x320') }
  # end

  def discard_suspect_otm_photos(photo_object)
    return unless photo_object&.full_image_url&.include?('image-0-480x320.jpg')

    photo_object.discard
  end

  def process_otm_photos(listing_photos)
    # processed_photos = primary_sale_listing.listing_photos.kept.select(&:flag_is_parsed_by_llm)
    listing_photos.each_with_index do |listing_photo, index|
      discard_suspect_otm_photos(listing_photo) # if portal.present? && portal == 'onthemarket'
      listing_photo.sort_order = index
      listing_photo.save! # Assuming you want to persist the change to the database
    end
  end
end
