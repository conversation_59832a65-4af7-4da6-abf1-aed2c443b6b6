# frozen_string_literal: true

class ApiPublic::V4::PagesController < ApiPublicApplicationController
  def single_page
    default_page_slug = "home_page"
    page_slug = params[:page_slug] || default_page_slug
    @page = Page.find_by_localized_slug(locale, page_slug)
    @content_to_show = []

    if @page.present?
      current_edit_path
      set_feel
      # container_page = "show"
      view_to_render = @page.view_to_render || "/pages/show"
      if ["contact-us", "favourites-index"].include? page_slug
        # May extract this to be based on a page config
        # container_page = "favourites-container"
        # Below needed because vuetify needs to be hoisted in
        @adv_scripts_required = true
      end
      # handle_meta @page
      # render "/pwb_premium#{view_to_render}"
      @current_website = ActsAsTenant.current_tenant.websites.last # Website.last
      # @client_base_url = "#{request.protocol}#{request.host_with_port}"
      # @client_base_url is now set in base controller
      get_sections_as_html(@client_base_url)
      @content_locale = params[:locale] || "en"
      return render "show.json" # json_template_name
    else
      return render json: { error: "Page not found" }, status: 404
    end

    # @page = Page.find_by_uuid(params[:uuid]) || Page.last
  end

  private

  def get_sections_as_html(client_base_url)
    @page_sections_with_html = []
    @page.page_sections_for_public.each do |page_section|
      # html_string = render_component_to_string PageSectionComponent.new(
      #   current_user: @current_user,
      #   page_section: page_section,
      # )
      # # in order to be able to use above, had to specify formats as [:html] in the component
      # # .html.erb file
      html_string = render_to_string :partial => page_section.view_partial_or_default,
                                     :formats => [:html],
                                     :locals => {
                                       client_base_url: client_base_url,
                                       page_section: page_section,
                                       container_classes: "my-2",
                                       sect_cont_classes: "",
                                     }
      @page_sections_with_html.push({ section: page_section, html: html_string })
    end
  end

  def handle_meta(page)
    canonical_url = request.url
    url_base = "#{request.protocol + request.host_with_port}"
    meta_tags = page.retrieve_meta_tags url_base, canonical_url, @current_website.company_display_name, @current_website.main_logo_url
    meta_tags.each do |mtag|
      set_meta_tags mtag
    end
  end

  def set_feel
    @feel = {
      "animations" => {},
      "fonts" => {},
      "shade" => {},
      "sizes" => {},
      "background" => {},
      "accent" => {},
      "structure" => {},
      "corners" => {},
      "shadows" => {},
    # radius, shadow
    }
  end

  def current_edit_path
    @admin_nav_data = {}
    if @current_user && @page.present?
      content_locale = params[:locale] || I18n.locale
      # TODO - figure out a way to get below from user
      admin_ui_locale = I18n.locale
      @current_edit_path = url_for(edit_standard_page_path(@page.uuid, locale: admin_ui_locale, content_locale: content_locale))
      @current_full_screen_edit_path = url_for(full_screen_page_edit_path(@page.uuid, locale: admin_ui_locale, content_locale: content_locale))
      @admin_nav_data["current_edit_path"] = @current_edit_path
      @admin_nav_data["current_full_screen_edit_path"] = @current_full_screen_edit_path
    end
  end
end
