# class Api::V1::SaleListingsController < ApplicationApiController
# below originally based on above
class ApiPublic::V4::SaleListingsController < ApplicationController
  skip_before_action :verify_authenticity_token
  respond_to :json

  def show
    @sale_listing = SaleListing.find_by_uuid(params[:sale_listing_uuid])
    @close_uprn_details_json = {}
    if @sale_listing&.realty_asset&.close_uprn_details_json
      # TODO: - don't return close_uprn_details_json if uprn exists and is accurate
      @close_uprn_details_json = @sale_listing.realty_asset.close_uprn_details_json
    end
    if @sale_listing.nil?
      render json: { error: 'Listing not found' }, status: :not_found
    else
      render 'api_public/v4/sale_listings/show'
    end
    # file_path = Rails.root.join('app/views/', 'api_public/v4/purchase_evaluations/show.json')
    # render json: File.read(file_path), status: :ok
  end

  def list
    @sale_listings = SaleListing.kept.order('created_at desc')
    render 'api_public/v4/sale_listings/list'
  end
end
