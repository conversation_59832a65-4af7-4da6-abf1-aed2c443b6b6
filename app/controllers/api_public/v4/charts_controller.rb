module ApiPublic
  module V4
    class ChartsController < ApplicationController
      def sold_data_scatter_charts
        @sold_transactions = SoldTransactionEpc.includes(:sold_transaction).all
        @floor_area_chart_data = @sold_transactions.map do |transaction|
          [
            transaction.total_floor_area,
            transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f
          ]
        end

        @rooms_chart_data = @sold_transactions.map do |transaction|
          [
            transaction.number_habitable_rooms,
            transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f
          ]
        end

        @predicted_price_chart_data = @sold_transactions.map do |transaction|
          [
            transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f,
            transaction.st_predicted_price_a.gsub(/[^\d.]/, '').to_f
          ]
        end

        render 'api_public/v4/charts/sold_data_scatter_charts' # json_template_name
      end

      def sold_data_column_charts
        @sold_transactions = SoldTransactionEpc.includes(:sold_transaction).all

        @current_energy_rating_chart_data = @sold_transactions.group_by(&:current_energy_rating).map do |rating, transactions|
          {
            name: rating,
            data: transactions.map { |transaction| transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f }
          }
        end

        @potential_energy_rating_chart_data = @sold_transactions.group_by(&:potential_energy_rating).map do |rating, transactions|
          {
            name: rating,
            data: transactions.map { |transaction| transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f }
          }
        end

        @construction_age_band_chart_data = @sold_transactions.group_by(&:construction_age_band).map do |age_band, transactions|
          {
            name: age_band,
            data: transactions.map { |transaction| transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f }
          }
        end

        render 'api_public/v4/charts/test_view' # json_template_name
      end

      def test_view
        @sold_transactions = SoldTransactionEpc.includes(:sold_transaction).all

        @chart_data = @sold_transactions.map do |transaction|
          {
            name: "Transaction #{transaction.id}",
            data: {
              transaction.sold_transaction.sold_date => transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f
            }
          }
        end

        @floor_area_chart_data = @sold_transactions.map do |transaction|
          [
            transaction.total_floor_area,
            transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f
          ]
        end

        @rooms_chart_data = @sold_transactions.map do |transaction|
          [
            transaction.number_habitable_rooms,
            transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f
          ]
        end

        @current_energy_rating_chart_data = @sold_transactions.group_by(&:current_energy_rating).map do |rating, transactions|
          {
            name: rating,
            data: transactions.map { |transaction| transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f }
          }
        end

        @potential_energy_rating_chart_data = @sold_transactions.group_by(&:potential_energy_rating).map do |rating, transactions|
          {
            name: rating,
            data: transactions.map { |transaction| transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f }
          }
        end

        @construction_age_band_chart_data = @sold_transactions.group_by(&:construction_age_band).map do |age_band, transactions|
          {
            name: age_band,
            data: transactions.map { |transaction| transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f }
          }
        end

        @predicted_price_chart_data = @sold_transactions.map do |transaction|
          [
            transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f,
            transaction.st_predicted_price_a.gsub(/[^\d.]/, '').to_f
          ]
        end

        render 'api_public/v4/charts/test_view' # json_template_name
      end

      def sold_data_line_charts
        @sold_transactions = SoldTransactionEpc.includes(:sold_transaction).all

        @st_tenure_chart_data = @sold_transactions.group_by(
          &:st_tenure
        ).map do |tenure, transactions|
          [tenure, transactions.count]
        end.to_h

        @st_property_type_chart_data = @sold_transactions.group_by(
          &:st_property_type
        ).map do |property_type, transactions|
          [property_type, transactions.count]
        end.to_h

        @st_age_of_property_chart_data = @sold_transactions.group_by(
          &:st_age_of_property
        ).map do |age, transactions|
          [age, transactions.count]
        end.to_h

        @total_floor_area_chart_data = @sold_transactions.group_by(
          &:total_floor_area
        ).map do |floor_area, transactions|
          [floor_area, transactions.count]
        end.to_h

        @number_heated_rooms_chart_data = @sold_transactions.group_by(
          &:number_heated_rooms
        ).map do |heated_rooms, transactions|
          [heated_rooms, transactions.count]
        end.to_h

        @number_habitable_rooms_chart_data = @sold_transactions.group_by(
          &:number_habitable_rooms
        ).map do |habitable_rooms, transactions|
          [habitable_rooms, transactions.count]
        end.to_h

        @construction_age_band_chart_data = @sold_transactions.group_by(
          &:construction_age_band
        ).map do |age_band, transactions|
          [age_band, transactions.count]
        end.to_h

        @potential_energy_efficiency_chart_data = @sold_transactions.group_by(
          &:potential_energy_efficiency
        ).map do |efficiency, transactions|
          [efficiency, transactions.count]
        end.to_h

        @sold_price_chart_data = @sold_transactions.group_by(
          &:sold_price_cents
        ).map do |fsp, transactions|
          [fsp, transactions.count]
        end.to_h

        @current_energy_rating_chart_data = @sold_transactions.group_by(
          &:current_energy_rating
        ).map do |rating, transactions|
          [rating, transactions.count]
        end.to_h

        render 'api_public/v4/charts/sold_data_line_charts'
      end

      def sold_data_pie_charts
        @sold_transactions = SoldTransactionEpc.includes(:sold_transaction).all

        @postcode_chart_data = @sold_transactions.group(
          :st_epc_postcode
        ).count

        @current_energy_rating_chart_data = @sold_transactions.group_by(
          &:current_energy_rating
        ).map do |rating, transactions|
          [rating, transactions.count]
        end.to_h

        render 'api_public/v4/charts/sold_data_pie_charts'
      end

      def sold_data_bar_charts
        @sold_transactions = SoldTransactionEpc.includes(:sold_transaction).all

        @construction_age_band_chart_data = @sold_transactions.group_by(&:construction_age_band).map do |age_band, transactions|
          [
            age_band,
            transactions.sum { |transaction| transaction.sold_transaction.formatted_sold_price.gsub(/[^\d.]/, '').to_f }
          ]
        end

        render 'api_public/v4/charts/sold_data_bar_charts'
      end
    end
  end
end
