module ApiPublic
  module V4
    class PostcodeAreasController < ApplicationController
      respond_to :json
      def index
        @postcode_areas = PostcodeArea.all.includes(:sold_transactions)
        # render json: @postcode_areas.to_json(include: :sold_transactions)

        # Using Jbuilder to structure the JSON response for better control and efficiency
        render '/api_public/v4/postcode_areas/index' # This will render the index.json.jbuilder template
        # render json: @postcode_areas.as_json
      end
    end
  end
end
