module ApiPublic
  module V4
    class ChartsDataExamplesController < ApplicationController
      # Radar Chart Data
      def radar_chart_data
        data = {
          series: [
            {
              name: 'New York',
              data: [300_000, 250_000, 500, 1000] # Average Price, Median Price, Price/SqFt, Tax Rate
            },
            {
              name: 'San Francisco',
              data: [500_000, 450_000, 750, 1200] # Same metrics
            },
            {
              name: 'Chicago',
              data: [250_000, 200_000, 400, 800]
            }
          ],
          options: {
            chart: {
              height: 350,
              type: 'radar',
              dropShadow: {
                enabled: true,
                blur: 1,
                left: 1,
                top: 1
              }
            },
            title: {
              text: 'House Price Comparison Across Cities'
            },
            stroke: {
              width: 2
            },
            fill: {
              opacity: 0.1
            },
            markers: {
              size: 4
            },
            xaxis: {
              categories: ['Average Price', 'Median Price', 'Price/SqFt', 'Tax Rate']
            },
            yaxis: {
              tickAmount: 7,
              labels: {
                formatter: lambda { |val, i|
                  case i
                  when 0, 1 then val
                  when 2 then "$#{val}"
                  else "#{val}%"
                  end
                }
              }
            },
            legend: {
              position: 'top'
            }
          }
        }

        render json: {
          chart_setup: data
        }
      end

      # Box Plot Data
      def box_plot_data
        data = {
          series: [{
            type: 'boxPlot',
            data: [
              {
                x: 'Neighborhood A',
                y: [300_000, 320_000, 350_000, 380_000, 400_000] # min, q1, median, q3, max
              },
              {
                x: 'Neighborhood B',
                y: [250_000, 270_000, 300_000, 330_000, 350_000]
              },
              {
                x: 'Neighborhood C',
                y: [400_000, 420_000, 450_000, 480_000, 500_000]
              }
            ]
          }],
          options: {
            chart: {
              type: 'boxPlot',
              height: 350
            },
            title: {
              text: 'House Price Distribution by Neighborhood',
              align: 'left'
            },
            plotOptions: {
              boxPlot: {
                colors: {
                  upper: '#5C4742',
                  lower: '#A5978B'
                }
              }
            },
            xaxis: {
              type: 'category'
            },
            yaxis: {
              title: {
                text: 'Price (USD)'
              }
            },
            tooltip: {
              shared: false,
              intersect: true
            }
          }
        }

        render json: {
          chart_setup: data
        }
      end

      # Action for time series data
      def time_series_data
        data = {
          series: [{
            name: 'Average House Price',
            data: [
              { x: '2015', y: 250_000 },
              { x: '2016', y: 270_000 },
              { x: '2017', y: 285_000 },
              { x: '2018', y: 300_000 },
              { x: '2019', y: 320_000 },
              { x: '2020', y: 340_000 }
            ]
          }],
          options: {
            chart: {
              type: 'line'
            },
            xaxis: {
              type: 'category'
            },
            yaxis: {
              title: {
                text: 'Price (USD)'
              }
            },
            title: {
              text: 'Average House Prices Over Years'
            }
          }
        }

        render json: {
          chart_setup: data
        }
      end

      # Action for scatter plot data
      def scatter_plot_data
        data = {
          series: [{
            name: 'House Data',
            data: [
              { x: 1000, y: 200_000, label: 'House 1' },
              { x: 1500, y: 300_000, label: 'House 2' },
              { x: 2000, y: 450_000, label: 'House 3' },
              { x: 2500, y: 600_000, label: 'House 4' }
            ]
          }],
          options: {
            chart: {
              type: 'scatter',
              zoom: {
                enabled: true,
                type: 'xy'
              }
            },
            xaxis: {
              title: {
                text: 'Square Footage'
              }
            },
            yaxis: {
              title: {
                text: 'Price (USD)'
              }
            },
            title: {
              text: 'Price vs. Size of Houses'
            }
          }
        }

        render json: {
          chart_setup: data
        }
      end

      # Action for bar chart data
      def bar_chart_data
        data = {
          series: [{
            name: 'Average Price',
            data: [300_000, 500_000, 400_000, 600_000]
          }],
          options: {
            chart: {
              type: 'bar',
              height: 350
            },
            plotOptions: {
              bar: {
                horizontal: true
              }
            },
            xaxis: {
              categories: ['New York', 'San Francisco', 'Chicago', 'Miami'],
              title: {
                text: 'City'
              }
            },
            yaxis: {
              title: {
                text: 'Price (USD)'
              }
            },
            title: {
              text: 'Average House Price by City'
            }
          }
        }

        render json: {
          chart_setup: data
        }
      end

      # Action for heatmap data
      def heatmap_data
        data = {
          series: [
            {
              name: '1 Bedroom',
              data: [
                { x: '0-200k', y: 100 },
                { x: '200-400k', y: 50 },
                { x: '400-600k', y: 10 },
                { x: '600k+', y: 5 }
              ]
            },
            {
              name: '2 Bedrooms',
              data: [
                { x: '0-200k', y: 50 },
                { x: '200-400k', y: 200 },
                { x: '400-600k', y: 150 },
                { x: '600k+', y: 20 }
              ]
            }
          ],
          options: {
            chart: {
              type: 'heatmap',
              height: 350
            },
            dataLabels: {
              enabled: false
            },
            xaxis: {
              title: {
                text: 'Price Range'
              }
            },
            yaxis: {
              title: {
                text: 'Number of Bedrooms'
              }
            },
            title: {
              text: 'House Price Distribution by Bedrooms'
            }
          }
        }

        render json: {
          chart_setup: data
        }
      end
    end
  end
end
