# frozen_string_literal: true

# Dec 2019 - rational behind simple_search is to distinguish
# from searches that feed through to an api (like resales..)
# Might call those SearchPassThrough

class ApiPublic::V4::SearchSimpleController < ApiPublicApplicationController
  # self.page_cache_directory = -> { Rails.root.join("public", request.domain) }
  # caches_page :show

  def refresh_csrf_token
    # Nov 2020 - Might move to forms controller
    # Implemented to ensure token available if sign-out needed
    response.headers["X-CSRF-Token"] = form_authenticity_token.to_s
    response.headers["X-CSRF-Param"] = "authenticity_token"
    render json: {
             success: true,
           }
  end

  def results_only
    if PwbConfig::General::ListingsBackend == "resales"
      if params[:op] == "regular_rentals"
        query_instance = ListingsResales::QueryRentals.new(search_params: params)
      else
        query_instance = ListingsResales::QuerySales.new(search_params: params)
      end
    else
      if params[:op] == "regular_rentals"
        query_instance = ListingsDb::QueryRentals.new(search_params: params)
      else
        query_instance = ListingsDb::QuerySales.new(search_params: params)
      end
    end

    # cache_key = "JNAR_QUERY#{params.values.join}"
    # @results = Rails.cache.fetch(cache_key) do
    #   query_instance.get_results(include_messages: true)
    #   # for results that are cached always include messages..
    # end

    @results = query_instance.get_results(include_messages: true)
    # Apr 2021 TODO - figure out logic for when to display json
    # with or without address details
    if params[:op] == "regular_rentals"
      return render "results_rental_listings_with_lat_lng.json"
    else
      return render "results_sale_listings_with_lat_lng.json"
    end
  end

  private
end
