class ApiPublic::V4::ScootsController < ApplicationController
  # include ActionController::Caching::Actions

  skip_before_action :verify_authenticity_token
  respond_to :json

  # caches_action :show_games, cache_path: ->(controller) { "show_games/#{controller.params[:sbdmn_name]}" }, expires_in: HpgConfig::General::CACHE_EXPIRY_LOW
  def show_games
    incoming_subdomain = request.subdomain.presence || 'default'
    sbdmn_name = params[:sbdmn_name]
    if sbdmn_name.downcase != incoming_subdomain.downcase
      render json: { error: 'Sbd error' }, status: :unprocessable_entity
      return
    end
    # @scoot = Scoot.find_by(scoot_subdomain: sbdmn_name)
    # if @scoot.nil?
    #   render json: { error: 'Scoot not found' }, status: :not_found
    #   return
    # end
    # render json: { scoot: @scoot.as_json(
    #   only: %w[scoot_title],
    #   methods: %w[scoot_notice is_price_guess_enabled
    #               available_games_details
    #               supports_multiple_games
    #               is_price_guess_only is_price_guess_public]
    # ) }

    # start round_up games processing
    @round_up_realty_games = RealtyGame.where(
      game_default_locale: %w[holiday-destinations holiday-destinations-2]
    )
    # end round_up games processing

    cache_key = "show_games/#{sbdmn_name}"
    main_resp_json = Rails.cache.fetch(cache_key, expires_in: HpgConfig::General::CACHE_EXPIRY_LOW) do
      @scoot = Scoot.find_by(scoot_subdomain: sbdmn_name)
      if @scoot.nil?
        render json: { error: 'Scoot not found' }, status: :not_found
        return nil
      end
      # wonder if I can make use of render_to_string elsewhere
      render_to_string template: 'api_public/v4/scoots/show_games', formats: [:json]
    end

    return unless main_resp_json.present? && !performed?

    render json: main_resp_json
  end

  def show
    incoming_subdomain = request.subdomain.presence || 'default'
    sbdmn_name = params[:sbdmn_name]
    if sbdmn_name.downcase != incoming_subdomain.downcase
      render json: { error: 'Sbd error' }, status: :unprocessable_entity
      return
    end
    @scoot = Scoot.find_by(scoot_subdomain: sbdmn_name)
    if @scoot.nil?
      render json: { error: 'Scoot not found' }, status: :not_found
      return
    end
    render json: { scoot: @scoot.as_json(
      only: %w[scoot_title],
      methods: %w[scoot_notice is_price_guess_enabled
                  supports_multiple_games
                  is_price_guess_only is_price_guess_public]
    ) }
  end

  def check
    incoming_subdomain = request.subdomain.presence || 'default'
    sbdmn_name = params[:sbdmn_name]
    if sbdmn_name.downcase != incoming_subdomain.downcase
      render json: { error: 'Sbd error' }, status: :unprocessable_entity
      return
    end
    @scoot = Scoot.find_by(scoot_subdomain: sbdmn_name)
    if @scoot.nil?
      render json: { error: 'Scoot not found' }, status: :not_found
      return
    end
    if @scoot.access_token != params[:access_token]
      render json: { error: 'Access token error' } # , status: :unauthorized
      return
    end
    # @scoot = ScootDecorator.new(@scoot)
    # @scoot.scoot_dossier = ScootDossierDecorator.new(@scoot.scoot_dossier)
    realty_dossiers = @scoot&.scoot_dossiers.presence&.order('created_at desc')
    unless realty_dossiers.present?
      render json: {
        status: 'awaiting_dossier',
        message: 'Your dossier is not yet available. Please try again later.'
      }
      return
    end
    main_dossier = realty_dossiers.first
    mgmt_prefix = 'https://medo.propertywebbuilder.com/superwiser/scoots'
    msg_url = "#{@scoot.scoot_subdomain} #{mgmt_prefix}/#{@scoot.id}"
    ntfy_msg = "New token access to: #{msg_url}"
    Utils::NtfyUtils.new.send_public_ntfy(ntfy_msg)
    # Utils::NtfyUtils.new.send_mgmt_email_note(ntfy_msg)
    render json: {
      main_dossier: main_dossier.as_json(
        only: %w[uuid],
        methods: %w[]
      )
      # realty_dossiers: realty_dossiers.as_json(
      #   only: %w[],
      #   methods: %w[]
      # )
    }
    # render 'api_public/v4/realty_dossiers/list_for_superwiser'
  end
end
