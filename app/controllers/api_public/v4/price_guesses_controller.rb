# frozen_string_literal: true

class ApiPublic::V4::PriceGuessesController < ApplicationController
  skip_before_action :verify_authenticity_token
  respond_to :json

  before_action :load_scoot # :load_dossier
  before_action :load_price_estimate, only: %i[show update destroy]

  def game_result_calcs
    session_id = params[:game_session_id]
    # June 2025 - have to watch out for sql injection attack threat here..
    result = RealtyPunts::GameResultsCalculator.new(session_id).call

    if result.success?
      render json: result.data
    else
      render json: { error: result.error_message }, status: :not_found
    end
  end

  def game_results
    @price_estimates = @scoot.price_estimates.kept.order('created_at DESC')

    listings_for_price_guess = @scoot.listings_for_price_guess
    # session_date: "2024-01-15T10:30:00Z"
    render json: {
      comparisons: listings_for_price_guess.as_json(
        only: %w[uuid estimated_price_cents estimate_currency estimate_title
                 estimate_text estimator_name is_ai_estimate is_for_sale_listing
                 is_for_rental_listing percentage_above_or_below created_at updated_at],
        methods: %w[price_estimates_summary]
      )
      # dd: @price_estimates.as_json(
      #   only: %w[uuid estimated_price_cents listing_uuid estimate_currency estimate_title
      #            price_at_time_of_estimate_cents estimate_details
      #            estimate_text estimator_name is_ai_estimate is_for_sale_listing
      #            is_for_rental_listing percentage_above_or_below created_at updated_at],
      #   methods: %w[formatted_estimated_price]
      # )
    }
  end

  def game_session_results
    @price_estimates = @scoot.non_ai_price_estimates.where(
      game_session_id: params[:game_session_id]
    ).kept.order('created_at DESC')

    if @price_estimates.count > 0
      latest_time = @price_estimates.pluck(:created_at).max
      is_within_last_hour = latest_time >= 1.hour.ago if latest_time
      if is_within_last_hour
        mgmt_prefix = 'https://medo.propertywebbuilder.com/superwiser/price_estimates'
        msg_url = "#{@scoot.scoot_subdomain} #{mgmt_prefix}/#{@price_estimates.last.id}"
        ntfy_msg = "New price guess: #{msg_url}"
        Utils::NtfyUtils.new.send_public_ntfy(ntfy_msg, request)
      end
    end

    # session_date: "2024-01-15T10:30:00Z"
    render json: {
      estimates: @price_estimates.as_json(
        only: %w[uuid estimated_price_cents listing_uuid estimate_currency estimate_title
                 price_at_time_of_estimate_cents estimate_details
                 estimate_text estimator_name is_ai_estimate is_for_sale_listing
                 is_for_rental_listing percentage_above_or_below created_at updated_at],
        methods: %w[formatted_estimated_price]
      )
    }
  end

  # GET /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates
  def index
    @price_estimates = @scoot.price_estimates.kept.order('created_at DESC')

    render json: {
      price_estimates: @price_estimates.as_json(
        only: %w[uuid estimated_price_cents estimate_currency estimate_title
                 estimate_text estimator_name is_ai_estimate is_for_sale_listing
                 is_for_rental_listing percentage_above_or_below created_at updated_at],
        methods: %w[formatted_estimated_price]
      )
    }
  end

  # GET /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid
  def show
    render json: {
      price_estimate: @price_estimate.as_json(
        only: %w[uuid estimated_price_cents price_at_time_of_estimate_cents
                 estimate_currency estimate_title estimate_text estimate_vicinity
                 estimate_postal_code estimate_latitude_center estimate_longitude_center
                 estimator_name is_ai_estimate is_for_sale_listing is_for_rental_listing
                 is_protected percentage_above_or_below count_sold_transactions_shown
                 estimate_details created_at updated_at],
        methods: %w[formatted_estimated_price formatted_price_at_time_of_estimate]
      )
    }
  end

  # POST /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates
  def create
    @price_estimate = @scoot.price_estimates.build(price_estimate_params)
    @price_estimate.agency_tenant_uuid = @scoot.agency_tenant_uuid

    # Create or find GameSession instance
    game_session = find_or_create_game_session

    if game_session
      # Set the game_session_id to the GameSession UUID
      @price_estimate.game_session_id = game_session.uuid

      # Also store in estimate_details for backward compatibility
      @price_estimate.estimate_details ||= {}
      @price_estimate.estimate_details['game_session_id'] = game_session.uuid
      @price_estimate.estimate_details['session_guest_name'] = game_session.session_guest_name
    end

    if @price_estimate.save
      render json: {
        price_estimate: @price_estimate.as_json(
          only: %w[uuid game_session_id estimated_price_cents estimate_currency estimate_title
                   estimate_text estimator_name is_ai_estimate created_at],
          methods: %w[formatted_estimated_price]
        ),
        game_session: game_session&.as_json(
          only: %w[uuid session_guest_name session_guest_title created_at]
        ),
        message: 'Price estimate created successfully'
      }, status: :created
    else
      render json: {
        errors: @price_estimate.errors.full_messages,
        message: 'Failed to create price estimate'
      }, status: :unprocessable_entity
    end
  end

  # PUT/PATCH /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid
  def update
    if @price_estimate.update(price_estimate_params)
      render json: {
        price_estimate: @price_estimate.as_json(
          only: %w[uuid game_session_id estimated_price_cents estimate_currency estimate_title
                   estimate_text estimator_name is_ai_estimate updated_at],
          methods: %w[formatted_estimated_price]
        ),
        message: 'Price estimate updated successfully'
      }
    else
      render json: {
        errors: @price_estimate.errors.full_messages,
        message: 'Failed to update price estimate'
      }, status: :unprocessable_entity
    end
  end

  # DELETE /api_public/v4/dossiers/:realty_dossier_uuid/price_estimates/:uuid
  def destroy
    if @price_estimate.discard
      render json: {
        message: 'Price estimate deleted successfully'
      }
    else
      render json: {
        errors: @price_estimate.errors.full_messages,
        message: 'Failed to delete price estimate'
      }, status: :unprocessable_entity
    end
  end

  def inputs_for_price_guess
    incoming_subdomain = request.subdomain.presence || 'default'
    @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)
    @guessed_price_validation = {
      max_percentage_above: 900,
      min_percentage_below: 90,
      messages: {
        too_high: 'Guess is way way too high',
        too_low: 'Guess is way too low',
        # too_high: 'Guess is more than 200% too high',
        # too_low: 'Guess is less than 90% of actual price',
        positive_number: 'Please enter a positive number'
      }
    }
    @game_communities_details = game_communities_details(request.host)
    # @realty_dossier = @scoot.scoot_dossiers.first
    render 'api_public/v4/price_guesses/price_guess_inputs'
    # render 'api_public/v4/realty_dossiers/price_guess'
  end

  private

  def game_communities_details(request_host)
    hosts = ['nuneaton.propertysquares.com', 'brum.propertysquares.com', 'brum-houses.propertysquares.com']
    relevant_games = hosts - [request_host]

    reddit_community = {
      url: 'https://www.reddit.com/r/propertysquares/',
      text: 'Join the discussion on reddit!'
    }

    reddit_community[:url] = 'https://www.reddit.com/r/nuneaton/' if request_host == 'nuneaton.propertysquares.com'

    {
      show: true,
      redditCommunity: reddit_community,
      relevantGames: relevant_games
    }
  end

  def load_scoot
    # @scoot = Scoot.find_by_uuid(params[:realty_scoot_uuid])
    incoming_subdomain = request.subdomain.presence || 'default'
    @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)

    unless @scoot
      render json: { error: 'scoot not found' }, status: :not_found
      return false
    end

    true
  end
  # def load_dossier
  #   @scoot = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])

  #   unless @realty_dossier
  #     render json: { error: 'Dossier not found' }, status: :not_found
  #     return false
  #   end

  #   true
  # end

  def load_price_estimate
    @price_estimate = @scoot.price_estimates.kept.find_by_uuid(params[:uuid])

    unless @price_estimate
      render json: { error: 'Price estimate not found' }, status: :not_found
      return false
    end

    true
  end

  # Find or create a GameSession instance based on the provided parameters
  def find_or_create_game_session
    session_id = params[:game_session_id] || params.dig(:price_estimate, :game_session_id)
    guest_name = params[:session_guest_name] || params.dig(:price_estimate, :session_guest_name) || 'Anonymous Player'
    guest_title = params[:session_guest_title] || params.dig(:price_estimate, :session_guest_title)

    # If session_id is provided, try to find existing session
    if session_id.present?
      existing_session = GameSession.find_by(uuid: session_id)
      return existing_session if existing_session
    end

    # Create new GameSession
    GameSession.create!(
      uuid: session_id.presence || SecureRandom.uuid,
      agency_tenant_uuid: @scoot.agency_tenant_uuid,
      main_scoot_uuid: @scoot.uuid,
      session_guest_name: guest_name,
      session_guest_title: guest_title,
      game_session_details: {
        created_via: 'price_guess_api',
        user_agent: request.user_agent,
        ip_address: request.remote_ip,
        created_at: Time.current
      }
    )
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error("Failed to create GameSession: #{e.message}")
    nil
  end

  def price_estimate_params
    params.require(:price_estimate).permit(
      :estimated_price_cents, :game_session_id, :price_at_time_of_estimate_cents, :estimate_currency,
      :estimate_title, :estimate_text, :estimate_vicinity, :estimate_postal_code,
      :estimate_latitude_center, :estimate_longitude_center, :estimator_name,
      :is_ai_estimate, :is_for_sale_listing, :is_for_rental_listing, :is_protected,
      :percentage_above_or_below, :count_sold_transactions_shown, :listing_uuid,
      :user_uuid, :scoot_uuid, :session_guest_name, :session_guest_title, estimate_details: {}
    )
  end
end
