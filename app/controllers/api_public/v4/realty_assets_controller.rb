module ApiPublic
  module V4
    class RealtyAssetsController < ApplicationController
      def detailed
        @realty_assets_with_transactions = RealtyAsset.all
        render json: {
          confirmed_st_epcs: @realty_assets_with_transactions.as_json(
            include: {
              epc_details: {
                only: %i[total_floor_area inspection_date]
                # transaction_type tenure
                #  address uprn
                #  number_heated_rooms number_habitable_rooms
                #  floor_height construction_age_band
                #  environmental_impact_current environmental_impact_potential energy_consumption_current energy_consumption_potential
                #  built_form last_sold_transaction_uuid
              },
              sold_transactions: {
                include: {
                  listing_photos: {
                    methods: [:image_details],
                    only: %i[uuid sort_order]
                  }
                },
                methods: %i[formatted_sold_price short_formatted_sold_price],
                only: %i[uuid new_home sold_date sold_transaction_reference
                         sold_transaction_title]
              }
            },
            only: %i[reference prop_type_key count_bedrooms title
                     latitude longitude street_address city postal_code]
          )
        }
      end
    end
  end
end
