# frozen_string_literal: true

class ApiPublic::V4::FavouritesController < ApplicationController
  def create_or_update
    @favourites_collection = FavouritesCollection.find_by(uuid: params['favsGuid']) if params['favsGuid'].present?

    @favourites_collection ||= FavouritesCollection.create
    geo_search = Geocoder.search(request.remote_ip).first
    geo_data = {}
    geo_data = geo_search.data if geo_search&.data
    extra_details = {
      remote_ip: request.remote_ip,
      geo_data: geo_data
    }
    if current_visit
      site_visitor = SiteVisitor.find_or_create_for_visit(current_visit)
      # SiteVisitorContact.create(
      #   site_visitor_token: site_visitor_token,
      #   contact_uuid: contact_model.uuid,
      #   email: contact_model.primary_email,
      # )
      site_visitor.update_prospect_visitor_stats

      site_visitor_token = current_visit.visitor_token
      # site_visitor.present? ? current_visit.site_visitor.uuid : nil
      @favourites_collection.site_visitor_token = site_visitor_token
    end
    @favourites_collection.extra_details = extra_details
    if params['todo'] == 'archive'
      @favourites_collection.archived_properties.push params['propDetails']
    else
      @favourites_collection.current_properties.push params['propDetails']
    end
    @favourites_collection.save!
    @favourites_collection.reload
    # above needed for uuid to be generated
    render json: {
      success: true,
      favs_guid: @favourites_collection.uuid
    }
  end
end
