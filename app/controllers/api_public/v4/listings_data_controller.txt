# frozen_string_literal: true

class ApiPublic::V4::ListingsDataController < ApplicationController
  # def show_listing
  #   if params[:listings_grouping] == "for-rent"
  #     @current_listing_item = RentalListing.find_by_localized_slug(I18n.locale, params[:listing_slug])
  #   else
  #     @current_listing_item = SaleListing.find_by_localized_slug(I18n.locale, params[:listing_slug])
  #   end
  #   # @current_listing_item = RentalListing.last
  #   # byebug
  #   return render "show_listing.json" # json_template_name
  # end
end
