class HousePricesController < ApplicationController
  def index
    # Add any data preparation logic here
    render layout: 'house_prices'
  end

  def epc_display
    # @property = Property.find(params[:id])
    @epc_data = {
      current_rating: params[:current_rating].present? ? params[:current_rating].to_i : 77,
      potential_rating: params[:potential_rating].present? ? params[:potential_rating].to_i : 88,
      address: '123 Fake Street',
      valid_until: '2025-01-01'
      #  @property.epc_valid_until
    }
    render 'epc_display', layout: 'house_prices'
  end
end
