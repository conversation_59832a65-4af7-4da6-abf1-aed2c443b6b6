# app/controllers/firebase_auth_controller.rb
require 'net/http'
require 'uri'

class FirebaseAuthController < ApplicationController
  skip_before_action :verify_authenticity_token

  # Below was mainly generated from the following gpt prompt:
  #   For fire base authentication I have provided the following link as my backend to handle actions in my email template.
  # https://example.com/acctmgmt/__/auth/action?mode=<action>&oobCode=<code>

  # How would I implement a controller on my rails server to handle this?

  #  ENV['firebase_api_key'] # Your Firebase API Key

  def action
    firebase_api_key = ENV['firebase_api_key']
    mode = params[:mode] # The action type (e.g., 'verifyEmail', 'resetPassword')
    oob_code = params[:oobCode] # The out-of-band verification code
    passed_in_api_key = params[:apiKey] # The Firebase API key
    firebase_api_key ||= passed_in_api_key

    case mode
    when 'verifyEmail'
      handle_email_verification(oob_code, firebase_api_key)
    when 'resetPassword'
      handle_password_reset(oob_code, firebase_api_key)
    else
      render json: { error: 'Invalid action' }, status: :bad_request
    end
  end

  private

  # Handle email verification via Firebase REST API
  def handle_email_verification(oob_code, firebase_api_key)
    response = firebase_verify_email(oob_code, firebase_api_key)
    if response['email']
      render json: { message: 'Email verified successfully' }, status: :ok
    else
      render json: { error: 'Invalid or expired verification code' }, status: :unprocessable_entity
    end
  end

  # Handle password reset verification via Firebase REST API
  def handle_password_reset(oob_code, firebase_api_key)
    response = firebase_verify_password_reset_code(oob_code, firebase_api_key)
    if response['email']
      render json: { message: 'Password reset code verified successfully' }, status: :ok
    else
      render json: { error: 'Invalid or expired reset code' }, status: :unprocessable_entity
    end
  end

  # Helper method to verify email using Firebase's REST API
  def firebase_verify_email(oob_code, firebase_api_key)
    uri = URI("https://identitytoolkit.googleapis.com/v1/accounts:update?key=#{firebase_api_key}")
    request_body = {
      oobCode: oob_code
    }.to_json
    make_post_request(uri, request_body)
  end

  # Helper method to verify password reset code using Firebase's REST API
  def firebase_verify_password_reset_code(oob_code, firebase_api_key)
    uri = URI("https://identitytoolkit.googleapis.com/v1/accounts:resetPassword?key=#{firebase_api_key}")
    request_body = {
      oobCode: oob_code
    }.to_json
    make_post_request(uri, request_body)
  end

  # Method to make POST requests
  def make_post_request(uri, request_body)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    request = Net::HTTP::Post.new(uri.request_uri, 'Content-Type' => 'application/json')
    request.body = request_body

    response = http.request(request)
    JSON.parse(response.body)
  rescue StandardError => e
    Rails.logger.error "Error during Firebase API request: #{e.message}"
    {}
  end
end
