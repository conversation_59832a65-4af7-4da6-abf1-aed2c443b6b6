#!/usr/bin/env ruby

# Example script demonstrating how to use the new pre-scraped content functionality
# for creating realty games.

require_relative '../config/environment'

# Create sample input data
sample_input = {
  'api_prefix' => 'http://localhost:3000/api_mgmt/v4',
  'scoot_subdomain' => 'demo-subdomain',
  'vendor_name' => 'buenavista',
  
  'property_urls' => [
    'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/12345',
    'https://www.onthemarket.com/details/67890/'
  ],
  'realty_game_slug' => 'demo-game'
}

# Write sample input to file
input_file = 'tmp/demo_realty_game_input.json'
FileUtils.mkdir_p(File.dirname(input_file))
File.write(input_file, JSON.pretty_generate(sample_input))

puts "📝 Created sample input file: #{input_file}"
puts "📄 Input content:"
puts JSON.pretty_generate(sample_input)
puts "\n"

# Example 1: Using the original method (for comparison)
puts "🔄 Example 1: Original method (would require remote scraping)"
puts "creator = RealtyPunts::RealtyPuntCreator.new"
puts "realty_game_id = creator.create_realty_game('#{input_file}')"
puts "# This method scrapes content on the remote server\n\n"

# Example 2: Using the new pre-scraped method
puts "🚀 Example 2: New pre-scraped method (scrapes locally)"
puts "creator = RealtyPunts::RealtyPuntCreator.new"
puts "realty_game_id = creator.create_realty_game_with_pre_scraped_content('#{input_file}')"
puts "# This method scrapes content locally and sends it to the server\n\n"

# Example 3: Manual API usage
puts "📡 Example 3: Manual API usage"
puts <<~RUBY
  # Step 1: Create scrape item locally
  creator = RealtyPunts::RealtyPuntCreator.new
  url = 'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/12345'
  portal = creator.send(:determine_retrieval_portal_from_url, url)
  scrape_item = creator.send(:create_scrape_item_locally, url, portal)
  scrape_item_data = creator.send(:serialize_scrape_item, scrape_item)

  # Step 2: Send to API
  response = HTTParty.post(
    'http://localhost:3000/api_mgmt/v4/realty_games_mgmt/init_game_with_pre_scraped_listing',
    body: {
      vendor_name: 'buenavista',
      scoot_subdomain: 'demo-subdomain',
      retrieval_portal: portal,
      retrieval_end_point: url,
      realty_game_slug: 'demo-game',
      scrape_item_data: scrape_item_data
    }.to_json,
    headers: {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json'
    }
  )

  if response.success?
    game_id = JSON.parse(response.body)['realty_game_id']
    puts "✅ Created realty game with ID: \#{game_id}"
  else
    puts "❌ Failed to create game: \#{response.body}"
  end
RUBY

puts "\n"

# Example 4: Error handling
puts "⚠️  Example 4: Error handling"
puts <<~RUBY
  begin
    creator = RealtyPunts::RealtyPuntCreator.new
    realty_game_id = creator.create_realty_game_with_pre_scraped_content('#{input_file}')
    puts "✅ Successfully created realty game: \#{realty_game_id}"
  rescue StandardError => e
    puts "❌ Error creating realty game: \#{e.message}"
    puts "🔍 Backtrace: \#{e.backtrace.first(3).join('\\n')}"
  end
RUBY

puts "\n"

# Example 5: Comparing performance
puts "⏱️  Example 5: Performance comparison"
puts <<~RUBY
  require 'benchmark'

  input_file = '#{input_file}'
  creator = RealtyPunts::RealtyPuntCreator.new

  # Note: These are conceptual examples - actual execution would require
  # proper setup and may take significant time due to scraping operations

  Benchmark.bm(25) do |x|
    x.report("Original method:") do
      # creator.create_realty_game(input_file)
      puts "Would scrape remotely"
    end

    x.report("Pre-scraped method:") do
      # creator.create_realty_game_with_pre_scraped_content(input_file)
      puts "Scrapes locally, sends data"
    end
  end
RUBY

puts "\n"

# Example 6: Portal-specific configurations
puts "🔧 Example 6: Portal-specific configurations"
puts <<~RUBY
  # Different portals use different scraping strategies:

  # Buenavista (JSON API)
  buenavista_config = {
    scrape_class: 'ScrapeItemFromBuenavista',
    connector: 'ScraperConnectors::Json',
    method: :find_or_create_for_h2c_buenavista,
    include_trailing_slash: false
  }

  # OnTheMarket (HTML scraping)
  otm_config = {
    scrape_class: 'ScrapeItemFromOtm',
    connector: 'ScraperConnectors::Regular',
    method: :find_or_create_for_h2c_onthemarket,
    include_trailing_slash: true
  }

  # Zoopla (JavaScript scraping)
  zoopla_config = {
    scrape_class: 'ScrapeItem',
    connector: 'ScraperConnectors::LocalPlaywright',
    method: :find_or_create_for_h2c,
    include_trailing_slash: false
  }
RUBY

puts "\n"

puts "🎯 Summary:"
puts "- Use create_realty_game_with_pre_scraped_content() for better performance"
puts "- Local scraping reduces server load and improves error handling"
puts "- Same input file format as original method"
puts "- New API endpoints: init_game_with_pre_scraped_listing, add_pre_scraped_listing_to_game"
puts "- Supports all existing portals: buenavista, onthemarket, zoopla, rightmove, purplebricks"

puts "\n📁 Sample input file created at: #{input_file}"
puts "🚀 Ready to test the new functionality!"
