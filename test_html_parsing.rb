#!/usr/bin/env ruby

require 'nokogiri'
require 'json'

html = File.read("manual_html/listing.html")
doc = Nokogiri::HTML(html)

puts "=== HTML ANALYSIS ==="
puts "HTML length: #{html.length}"
puts "Document title: #{doc.css('title').first&.text}"

# Look for ZPID in the URL pattern
zpid_match = html.match(/\/(\d{8,})_zpid\//)
puts "ZPID found: #{zpid_match[1] if zpid_match}"

# Look for JSON scripts
json_scripts = doc.css('script[type="application/json"]')
puts "JSON scripts found: #{json_scripts.length}"

json_scripts.each_with_index do |script, i|
  puts "Script #{i + 1} ID: #{script['id']}"
  content = script.text
  puts "Content length: #{content.length}"
  puts "Content preview: #{content[0..200]}..."
  
  begin
    parsed = JSON.parse(content)
    puts "Successfully parsed JSON"
    puts "Top-level keys: #{parsed.keys.join(', ')}" if parsed.is_a?(Hash)
  rescue JSON::ParserError => e
    puts "JSON parse error: #{e.message}"
  end
  puts "---"
end

# Look for __NEXT_DATA__
next_data = doc.at('script#__NEXT_DATA__')
puts "__NEXT_DATA__ script found: #{!!next_data}"

# Look for any script containing property data
scripts_with_property_data = doc.css('script').select do |script|
  text = script.text
  text.include?('zpid') || text.include?('property') || text.include?('homedetails')
end

puts "Scripts with property data: #{scripts_with_property_data.length}"

# Look for meta tags
og_title = doc.css('meta[property="og:title"]').first
puts "OG Title: #{og_title['content'] if og_title}"

og_description = doc.css('meta[property="og:description"]').first
puts "OG Description: #{og_description['content'][0..100] if og_description}..."

# Look for any text containing the address
address_patterns = [
  /7404.*Verna.*Bethany/i,
  /Myakka.*City/i,
  /FL.*34251/i
]

address_patterns.each do |pattern|
  match = html.match(pattern)
  puts "Address pattern #{pattern} found: #{!!match}"
  puts "Match: #{match[0]}" if match
end

puts "=== DONE ==="
