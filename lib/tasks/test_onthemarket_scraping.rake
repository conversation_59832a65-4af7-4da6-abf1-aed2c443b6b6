namespace :h2c do
  namespace :test do
    desc 'Test OnTheMarket scraping functionality with comprehensive checks'
    task test_onthemarket_scraping: :environment do
      puts "🧪 Testing OnTheMarket Scraping Components"
      puts "=" * 60
      
      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
      
      # Test URLs
      test_property_url = "https://www.onthemarket.com/details/16219282/"
      test_search_url = "https://www.onthemarket.com/for-sale/property/london/"
      
      success_count = 0
      total_tests = 8
      
      begin
        # Test 1: ScrapeItemFromOtm creation
        puts "\n1️⃣  Testing ScrapeItemFromOtm creation..."
        otm_scrape = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(test_property_url)
        
        if otm_scrape.persisted? && otm_scrape.scrape_is_onthemarket?
          puts "✅ ScrapeItemFromOtm created successfully"
          puts "   - ID: #{otm_scrape.id}"
          puts "   - URL: #{otm_scrape.scrapable_url}"
          puts "   - OnTheMarket flag: #{otm_scrape.scrape_is_onthemarket}"
          success_count += 1
        else
          puts "❌ Failed to create ScrapeItemFromOtm"
        end
        
        # Test 2: ScrapeItemFromOtmSearch creation
        puts "\n2️⃣  Testing ScrapeItemFromOtmSearch creation..."
        search_scrape = ScrapeItemFromOtmSearch.find_or_create_for_otm_search(test_search_url)
        
        if search_scrape.persisted? && search_scrape.is_realty_search_scrape?
          puts "✅ ScrapeItemFromOtmSearch created successfully"
          puts "   - ID: #{search_scrape.id}"
          puts "   - Search flag: #{search_scrape.is_realty_search_scrape}"
          puts "   - OnTheMarket flag: #{search_scrape.scrape_is_onthemarket}"
          success_count += 1
        else
          puts "❌ Failed to create ScrapeItemFromOtmSearch"
        end
        
        # Test 3: ScraperConnectors::Regular initialization
        puts "\n3️⃣  Testing ScraperConnectors::Regular..."
        begin
          connector = ScraperConnectors::Regular.new(otm_scrape)
          puts "✅ ScraperConnectors::Regular initialized successfully"
          puts "   - Class: #{connector.class.name}"
          puts "   - Scrape instance: #{connector.scrape_instance.class.name}"
          success_count += 1
        rescue => e
          puts "❌ Failed to initialize ScraperConnectors::Regular: #{e.message}"
        end
        
        # Test 4: Mock content processing
        puts "\n4️⃣  Testing content processing with mock data..."
        mock_html = create_mock_onthemarket_html
        otm_scrape.update!(
          full_content_before_js: mock_html,
          is_valid_scrape: true,
          full_content_before_js_length: mock_html.length
        )
        
        if otm_scrape.full_content_before_js.present? && otm_scrape.full_content_before_js.length > 1000
          puts "✅ Mock content set successfully"
          puts "   - Content length: #{otm_scrape.full_content_before_js.length}"
          success_count += 1
        else
          puts "❌ Failed to set mock content"
        end
        
        # Test 5: Property hash extraction
        puts "\n5️⃣  Testing property hash extraction..."
        begin
          property_hash = otm_scrape.property_hash_from_scrape_item
          
          if property_hash.is_a?(Hash) && property_hash.key?('asset_data') && property_hash.key?('listing_data')
            puts "✅ Property hash extracted successfully"
            puts "   - Keys: #{property_hash.keys}"
            puts "   - Asset bedrooms: #{property_hash['asset_data']['count_bedrooms']}"
            puts "   - Listing price: £#{property_hash['listing_data']['price_sale_current_cents'] / 100}"
            puts "   - Images count: #{property_hash['listing_image_urls']&.length || 0}"
            success_count += 1
          else
            puts "❌ Invalid property hash structure"
          end
        rescue => e
          puts "❌ Failed to extract property hash: #{e.message}"
        end
        
        # Test 6: Data mapping validation
        puts "\n6️⃣  Testing data mapping schemas..."
        begin
          asset_data = property_hash['asset_data']
          listing_data = property_hash['listing_data']
          
          # Validate required asset fields
          asset_required = %w[reference count_bedrooms count_bathrooms city description]
          asset_valid = asset_required.all? { |field| asset_data.key?(field) }
          
          # Validate required listing fields  
          listing_required = %w[reference price_sale_current_cents currency title]
          listing_valid = listing_required.all? { |field| listing_data.key?(field) }
          
          if asset_valid && listing_valid
            puts "✅ Data mapping schemas validated"
            puts "   - Asset schema: ✅ (#{asset_data.keys.length} fields)"
            puts "   - Listing schema: ✅ (#{listing_data.keys.length} fields)"
            success_count += 1
          else
            puts "❌ Data mapping schema validation failed"
            puts "   - Asset schema: #{asset_valid ? '✅' : '❌'}"
            puts "   - Listing schema: #{listing_valid ? '✅' : '❌'}"
          end
        rescue => e
          puts "❌ Data mapping validation error: #{e.message}"
        end
        
        # Test 7: Search results processing
        puts "\n7️⃣  Testing search results processing..."
        mock_search_results = create_mock_search_results
        search_scrape.update!(full_content_before_js: mock_search_results)
        
        begin
          listings = search_scrape.summary_sale_listings_from_scrape_item
          
          if listings.is_a?(Array) && listings.length > 0
            puts "✅ Search results processed successfully"
            puts "   - Listings found: #{listings.length}"
            puts "   - First listing reference: #{listings.first[:summary_listing_reference]}"
            puts "   - Price range: £#{listings.map { |l| l[:summary_listing_price] }.minmax.join(' - ')}"
            success_count += 1
          else
            puts "❌ Failed to process search results"
          end
        rescue => e
          puts "❌ Search processing error: #{e.message}"
        end
        
        # Test 8: Error handling
        puts "\n8️⃣  Testing error handling..."
        begin
          # Test with insufficient content
          short_content_scrape = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket("#{test_property_url}test/")
          short_content_scrape.update!(full_content_before_js: "short")
          
          error_caught = false
          begin
            short_content_scrape.property_hash_from_scrape_item
          rescue => e
            error_caught = true
            expected_error = e.message.include?('unavailabl') || e.message.include?('too short')
          end
          
          if error_caught && expected_error
            puts "✅ Error handling works correctly"
            puts "   - Caught expected error for insufficient content"
            success_count += 1
          else
            puts "❌ Error handling failed"
          end
        rescue => e
          puts "❌ Error handling test failed: #{e.message}"
        end
        
      rescue => e
        puts "\n💥 Fatal error during testing: #{e.message}"
        puts e.backtrace.first(5).join("\n")
      end
      
      # Summary
      puts "\n" + "=" * 60
      puts "🏁 TEST RESULTS SUMMARY"
      puts "=" * 60
      puts "Tests passed: #{success_count}/#{total_tests}"
      puts "Success rate: #{(success_count.to_f / total_tests * 100).round(1)}%"
      
      if success_count == total_tests
        puts "🎉 ALL TESTS PASSED! OnTheMarket scraping is working correctly."
      elsif success_count >= total_tests * 0.75
        puts "⚠️  Most tests passed. Minor issues may need attention."
      else
        puts "🚨 Multiple test failures detected. OnTheMarket scraping needs fixes."
      end
      
      puts "\n📊 Component Status:"
      puts "- Model creation: #{success_count >= 2 ? '✅' : '❌'}"
      puts "- Content processing: #{success_count >= 4 ? '✅' : '❌'}"
      puts "- Data extraction: #{success_count >= 6 ? '✅' : '❌'}"
      puts "- Error handling: #{success_count >= 7 ? '✅' : '❌'}"
      
      puts "\n" + "=" * 60
    end
    
    # Helper methods
    private
    
    def create_mock_onthemarket_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>Test Property - OnTheMarket</title>
          <link rel="canonical" href="https://www.onthemarket.com/details/16219282/" />
        </head>
        <body>
          <script id="__NEXT_DATA__">
          {
            "props": {
              "initialReduxState": {
                "property": {
                  "id": "16219282",
                  "propertyTitle": "Mock Test Property",
                  "description": "A beautiful test property for validation",
                  "bedrooms": 3,
                  "bathrooms": 2,
                  "priceRaw": 450000,
                  "displayAddress": "123 Test Street, Test Town",
                  "addressLocality": "Test Town",
                  "humanisedPropertyType": "Semi-detached house",
                  "images": [
                    {"largeUrl": "https://example.com/img1.jpg"},
                    {"url": "https://example.com/img2.jpg"}
                  ],
                  "features": [
                    {"id": "garden", "feature": "Garden"},
                    {"id": "parking", "feature": "Parking"}
                  ],
                  "location": {"lat": 51.5074, "lon": -0.1278},
                  "agent": {"ukCountry": "england"},
                  "documents": [],
                  "moreLikeThis": [],
                  "rooms": {
                    "descriptions": [
                      {"name": "living_room", "description": "Spacious living room"}
                    ]
                  }
                }
              }
            }
          }
          </script>
          <script id="dataLayerContainer">
            window.dataLayer.push({"postcode": "TT1 1TT", "property-id": "16219282"});
          </script>
          <div class="swiper-slide">
            <picture>
              <img src="https://example.com/test-image.jpg" alt="Test property" />
            </picture>
          </div>
          #{'<p>Additional content to meet length requirements.</p>' * 100}
        </body>
        </html>
      HTML
    end
    
    def create_mock_search_results
      {
        'properties' => [
          {
            'id' => '11111111',
            'property-title' => 'Mock Search Result 1',
            'price' => '£350,000',
            'bedrooms' => 2,
            'bathrooms' => 1,
            'display_address' => '456 Search Street, London',
            'postcode1' => 'SW1A',
            'location' => { 'lat' => 51.5074, 'lon' => -0.1278 },
            'images' => [{ 'default' => 'https://example.com/search1.jpg' }],
            'agent' => { 'name' => 'Mock Agent 1' }
          },
          {
            'id' => '22222222',
            'property-title' => 'Mock Search Result 2', 
            'price' => '£275,000',
            'bedrooms' => 1,
            'bathrooms' => 1,
            'display_address' => '789 Another Road, London',
            'postcode1' => 'E1 6AN',
            'location' => { 'lat' => 51.5155, 'lon' => -0.0922 },
            'images' => [{ 'default' => 'https://example.com/search2.jpg' }],
            'agent' => { 'name' => 'Mock Agent 2' }
          }
        ]
      }.to_json
    end
  end
end
