namespace :h2c do
  desc 'Start realty_game from url with input from file'
  task start_realty_game_from_url_generic: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    input_file = 'db/realty_punts/latest.json'
    service = RealtyPunts::RealtyPuntCreator.new
    result = service.create_realty_game(input_file)

    puts "game with id: #{result} created"
    # puts "result: #{result}"
    # # Read input from file
    # input_file = ENV['INPUT_FILE'] || 'db/realty_punts/latest.json'
    # unless File.exist?(input_file)
    #   puts "Input file not found: #{input_file}"
    #   exit 1
    # end

    # input_data = JSON.parse(File.read(input_file))
    # _api_prefix = input_data['api_prefix'] || 'https://be-medo.propertywebbuilder.com/api_mgmt/v4'
    # retrieval_portal = input_data['retrieval_portal'] || ''
    # property_refs = input_data['property_refs'] || []

    # if property_refs.empty?
    #   puts 'No property references found in input file'
    #   exit 1
    # end

    # # scoot_to_use = Scoot.find_by(scoot_subdomain: 'bvh')
    # scoot_to_use = Scoot.find_or_create_by(scoot_subdomain: 'costa-del-sol')
    # scoot_to_use.update!(
    #   supports_multiple_games: true,
    #   should_show_out_links: true
    #   # is_price_guess_enabled: true,
    #   # is_price_guess_public: true,
    #   # is_price_guess_only: true
    # )
    # realty_game_to_use = scoot_to_use.realty_games.find_or_create_by(
    #   realty_game_slug: 'regular-game'
    # )

    # realty_game_to_use.update!(
    #   game_title: 'Regular Game',
    #   game_description: 'A regular game to validate the RealtyGameListingCreator service',
    #   game_default_currency: 'GBP',
    #   game_default_country: 'UK',
    #   game_default_locale: 'en',
    #   game_start_at: 1.hour.ago,
    #   game_end_at: 1.week.from_now
    # )

    # # Process property references
    # property_refs.each_with_index do |extra_prop_ref, _index|
    #   if retrieval_portal == 'buenavista'
    #     retrieval_end_point = "https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/#{extra_prop_ref}"
    #   elsif retrieval_portal == 'onthemarket'
    #     retrieval_end_point = "https://www.onthemarket.com/details/#{extra_prop_ref}/"
    #   end

    #   realty_game_listing = realty_game_to_use.add_listing_from_url(retrieval_end_point, retrieval_portal)
    #   # asset = creator.create_asset_for_dossier(
    #   #   uuid,
    #   #   source_url,
    #   #   portal_name,
    #   #   is_primary: false
    #   # )

    #   puts "realty_game_listing created with details: #{realty_game_listing.realty_game_listing_details}"
    # end
  end
end
