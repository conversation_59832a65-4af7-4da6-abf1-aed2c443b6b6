namespace :h2c do
  desc 'fix_game_slugs'
  task fix_game_slugs: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    rg2 = RealtyGame.find(2)
    rg2_title = 'Specials Edition: Coventry House Prices'
    rg2_description = 'Discover Coventry, where history meets opportunity with a vibrant housing market! From charming medieval streets to trendy new developments, this city offers budget-friendly terraces, spacious family homes, and everything in between. Can you guess the true value of a Coventry property? Challenge yourself to find a steal or avoid an overpriced listing. Perfect for locals, prospective buyers, or anyone who loves a fun property puzzle!'
    rg2.update(
      game_bg_image_url: 'https://upload.wikimedia.org/wikipedia/commons/9/90/Hillman_House_Coventry.jpg',
      game_title: rg2_title,
      game_description: rg2_description,
      game_default_locale: rg2_title.parameterize
    )

    rg3 = RealtyGame.find(3)
    rg3_title = 'Steel City Edition: Sheffield House Prices'
    rg3_description = 'Check out Sheffield, a dynamic city where industrial roots and stunning green landscapes create a unique property scene! From lively urban lofts near the city centre to cozy homes bordering the Peak District, Sheffield’s housing market is full of surprises. Test your property pricing skills: can you identify a bargain or spot an overvalued gem? Whether you’re a Sheffielder, a future homeowner, or just curious, this game’s got you covered!'
    rg3.update(
      game_bg_image_url: 'https://upload.wikimedia.org/wikipedia/commons/3/31/Sheffield_City.jpg',
      game_title: rg3_title,
      game_description: rg3_description,
      game_default_locale: rg3_title.parameterize
    )

    hpg_scoot = Scoot.find_by(
      scoot_subdomain: 'hpg-scoot'
    )
    hpg_scoot.update(
      realty_game_ids: [2, 3]
    )
    # puts "result: #{result}"
  end
end
