# require 'logger'
# namespace :h2c do
#   desc 'Start realty_game from url with input from file, auto-detect portal, and comprehensive logging'
#   task start_realty_game_from_url_generic_v3: :environment do

namespace :h2c do
  desc 'Update visible_in_game based on associated sale listing visibility'
  task post_game_fixes_migration_changes: :environment do
    puts 'Starting visibility update for RealtyGameListings...'

    RealtyGameListing.for_sale.find_each do |listing|

      next unless listing.sale_listing

      # TODO - add something to ensure below is set each time a new
      # realty_game_listing is created..
      listing.update(
        ordered_photo_uuids: listing.sale_listing.listing_photos.pluck(:uuid),
        visible_photo_uuids: listing.sale_listing.listing_photos.pluck(:uuid)
      )

      new_visibility = listing.sale_listing.visible
      if listing.visible_in_game != new_visibility
        listing.update(visible_in_game: new_visibility)
        puts "Updated RealtyGameListing #{listing.uuid}: visible_in_game set to #{new_visibility}"
      end
    end

    puts 'Finished updating visibility for RealtyGameListings.'

    # This will fix counter caches after above
    RealtyGameListing.counter_culture_fix_counts

    RealtyGame.find_each do |r_g|
      r_g.update!(game_global_slug: r_g.game_default_locale)
    end
  end
end
