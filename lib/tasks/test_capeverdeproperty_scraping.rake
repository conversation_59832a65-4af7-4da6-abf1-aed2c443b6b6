namespace :h2c do
  namespace :test do
    desc 'Test Cape Verde Property scraping functionality with comprehensive checks'
    task test_capeverdeproperty_scraping: :environment do
      puts "🧪 Testing Cape Verde Property Scraping Components"
      puts "=" * 60
      
      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
      
      # Test URLs
      test_property_url = "https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms"
      test_search_url = "https://www.capeverdeproperty.co.uk/buy/property-for-sale/"
      
      success_count = 0
      total_tests = 8
      
      begin
        # Test 1: ScrapeItemFromCapeverdeproperty creation
        puts "\n1️⃣  Testing ScrapeItemFromCapeverdeproperty creation..."
        cvp_scrape = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(test_property_url)
        
        if cvp_scrape.persisted? && cvp_scrape.scrape_is_capeverdeproperty?
          puts "✅ ScrapeItemFromCapeverdeproperty created successfully"
          puts "   - ID: #{cvp_scrape.id}"
          puts "   - URL: #{cvp_scrape.scrapable_url}"
          puts "   - Cape Verde Property flag: #{cvp_scrape.scrape_is_capeverdeproperty}"
          success_count += 1
        else
          puts "❌ Failed to create ScrapeItemFromCapeverdeproperty"
        end
        
        # Test 2: ScrapeItemFromCapeverdepropertySearch creation
        puts "\n2️⃣  Testing ScrapeItemFromCapeverdepropertySearch creation..."
        search_scrape = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(test_search_url)
        
        if search_scrape.persisted? && search_scrape.is_realty_search_scrape?
          puts "✅ ScrapeItemFromCapeverdepropertySearch created successfully"
          puts "   - ID: #{search_scrape.id}"
          puts "   - Search flag: #{search_scrape.is_realty_search_scrape}"
          puts "   - Cape Verde Property flag: #{search_scrape.scrape_is_capeverdeproperty}"
          success_count += 1
        else
          puts "❌ Failed to create ScrapeItemFromCapeverdepropertySearch"
        end
        
        # Test 3: ScraperConnectors::Regular initialization
        puts "\n3️⃣  Testing ScraperConnectors::Regular..."
        begin
          connector = ScraperConnectors::Regular.new(cvp_scrape)
          puts "✅ ScraperConnectors::Regular initialized successfully"
          puts "   - Class: #{connector.class.name}"
          puts "   - Scrape instance: #{connector.scrape_instance.class.name}"
          success_count += 1
        rescue => e
          puts "❌ Failed to initialize ScraperConnectors::Regular: #{e.message}"
        end
        
        # Test 4: Mock content processing
        puts "\n4️⃣  Testing content processing with mock data..."
        mock_html = create_mock_capeverdeproperty_html
        cvp_scrape.update!(
          full_content_before_js: mock_html,
          full_content_before_js_length: mock_html.length,
          is_valid_scrape: true
        )
        
        begin
          property_data = cvp_scrape.property_hash_from_scrape_item
          puts "✅ Content processing successful"
          puts "   - Title: #{property_data['listing_data']['title']}"
          puts "   - Price: €#{property_data['listing_data']['price_sale_current_cents'] / 100}"
          puts "   - Bedrooms: #{property_data['asset_data']['count_bedrooms']}"
          puts "   - Images: #{property_data['listing_image_urls']&.length || 0}"
          success_count += 1
        rescue => e
          puts "❌ Content processing failed: #{e.message}"
        end
        
        # Test 5: RealtyParsers::ParseCapeverdepropertyListingsHtml
        puts "\n5️⃣  Testing HTML parser service..."
        begin
          parsed_data = RealtyParsers::ParseCapeverdepropertyListingsHtml.property_hash_from_html(mock_html)
          puts "✅ HTML parser service working correctly"
          puts "   - Title extracted: #{parsed_data['title']}"
          puts "   - Price extracted: €#{parsed_data['price_raw']}"
          puts "   - Features count: #{parsed_data['features']&.length || 0}"
          puts "   - Images count: #{parsed_data['image_count'] || 0}"
          success_count += 1
        rescue => e
          puts "❌ HTML parser failed: #{e.message}"
        end
        
        # Test 6: Pasarelas::CapeverdepropertyPasarela
        puts "\n6️⃣  Testing Pasarela (data transformation)..."
        begin
          pasarela_result = Pasarelas::CapeverdepropertyPasarela.extract_listing_and_asset_from_scrape_item(cvp_scrape)
          puts "✅ Pasarela transformation successful"
          puts "   - Listing data keys: #{pasarela_result[:listing_data].keys.count}"
          puts "   - Asset data keys: #{pasarela_result[:asset_data].keys.count}"
          puts "   - Images: #{pasarela_result[:images]&.length || 0}"
          puts "   - Raw data present: #{pasarela_result[:raw_data].present?}"
          success_count += 1
        rescue => e
          puts "❌ Pasarela transformation failed: #{e.message}"
        end
        
        # Test 7: Portal configuration
        puts "\n7️⃣  Testing portal configuration..."
        begin
          portal_config = RealtyScrapedItem.portal_config['capeverdeproperty']
          if portal_config.present?
            puts "✅ Portal configuration found"
            puts "   - Scrape class: #{portal_config[:scrape_class]}"
            puts "   - Connector: #{portal_config[:connector]}"
            puts "   - Method: #{portal_config[:method]}"
            puts "   - Pasarela: #{portal_config[:pasarela]}"
            success_count += 1
          else
            puts "❌ Portal configuration not found"
          end
        rescue => e
          puts "❌ Portal configuration test failed: #{e.message}"
        end
        
        # Test 8: Flag validation
        puts "\n8️⃣  Testing flag system..."
        begin
          test_item = RealtyScrapedItem.new(agency_tenant_uuid: AgencyTenant.unique_tenant.uuid)
          test_item.scrape_is_capeverdeproperty = true
          
          if test_item.scrape_is_capeverdeproperty?
            puts "✅ Flag system working correctly"
            puts "   - Flag set: #{test_item.scrape_is_capeverdeproperty}"
            puts "   - Flag value: #{test_item.realty_scraped_item_flags}"
            success_count += 1
          else
            puts "❌ Flag system not working"
          end
        rescue => e
          puts "❌ Flag system test failed: #{e.message}"
        end
        
        # Final results
        puts "\n" + "=" * 60
        puts "🎯 Test Results Summary"
        puts "✅ Passed: #{success_count}/#{total_tests} tests"
        puts "❌ Failed: #{total_tests - success_count}/#{total_tests} tests"
        
        if success_count == total_tests
          puts "🎉 All tests passed! Cape Verde Property scraping is ready."
        elsif success_count >= total_tests * 0.75
          puts "⚠️  Most tests passed. Minor issues may need attention."
        else
          puts "🚨 Multiple test failures. Review implementation."
        end
        
        # Cleanup
        puts "\n🧹 Cleaning up test data..."
        RealtyScrapedItem.where(scrapable_url: [test_property_url, test_search_url]).destroy_all
        puts "✅ Cleanup completed"
        
      rescue => e
        puts "💥 Critical error during testing: #{e.message}"
        puts e.backtrace.first(5)
      end
    end

    private

    def create_mock_capeverdeproperty_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>CA OCEANO 2 BEDROOM APARTMENT</title>
          <link rel="canonical" href="https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms">
        </head>
        <body>
          <h1>CA OCEANO 2 BEDROOM APARTMENT SANTA MARIA, SAL</h1>
          <h2>€125,000</h2>
          <h2>2 Bedroom Apartment For Sale</h2>
          <h3>Tenure: Freehold</h3>
          
          <p>Sea Views, 2 bed first floor apartment located by the Budda Beach Hotel in the east of Santa Maria. Fully Furnished. Great location.</p>
          
          <img src="https://maps.googleapis.com/maps/api/staticmap?center=16.5963490599297,-22.8951838191986&zoom=13&size=2000x1000" alt="Map">
          
          <div class="main-features">
            <ul>
              <li>1 bathroom(s)</li>
              <li>Condition: Excellent</li>
              <li>Furnished</li>
              <li>Sea view</li>
              <li>Distance from the sea: Walking</li>
              <li>Distance from an airport: 15 mins</li>
            </ul>
          </div>
          
          <img src="https://wdcdn.co/Media/webp/l/6e70a3d7-8920-48e8-8409-46e1b1606436.jpg" alt="Property Image">
          <img src="https://wdcdn.co/Media/webp/l/4ef69a11-404b-4148-910f-0234126192ab.jpg" alt="Property Image">
          
          <h3>OFFICE DETAILS</h3>
          <div>
            <a href="/offices/estate-agents/cape-verde">Number 3, Residence Isla do Fogo, Rua 1 De Junho, Santa Maria, Sal, Cape Verde</a>
            <a href="tel:002382422041">00238 242 2041</a>
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </div>
        </body>
        </html>
      HTML
    end
  end
end
