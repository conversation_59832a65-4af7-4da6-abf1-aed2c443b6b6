require 'logger'
require 'httparty'

namespace :h2c do
  # rake h2c:start_game_from_url_otm
  desc 'Start game from url'
  task start_game_from_url_otm: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    if VCR.turned_on?
      puts VCR.current_cassette.name if VCR.current_cassette
      cassette_library_dir ||= 'spec/cassettes/listing_scrapes'
      VCR.configure do |config|
        config.allow_http_connections_when_no_cassette = true
        config.cassette_library_dir = cassette_library_dir
        config.hook_into :webmock
        config.ignore_localhost = true
        config.debug_logger = File.open('vcr.log', 'w')
      end
    end

    all_refs = %w[14614992 17019883 17063716]
    # 17019883 - magyar 550k
    # 17063716 - axmin cl 425k

    # all_refs = %w[17098402 16510476 16486838 16851473]
    # 17098402 - bowen ct - 99k
    # 16510476 - c'gate 100k
    # 16486838 - warstone lane 199k
    # 16851473 - digbeth 175k

    # all_refs = %w[17033649 14554870 17106633]
    # 17106633 berrick salome 995k
    # 14554870 hinksey hill 1.5m
    # 17033649 melfort

    # api_prefix = 'http://david.lvh.me:3333/api_mgmt/v4'
    api_prefix = 'https://be-medo.propertywebbuilder.com/api_mgmt/v4'
    # dossier = nil
    dossier_id = nil
    all_refs.each_with_index do |extra_prop_ref, index|
      retrieval_end_point = "https://www.onthemarket.com/details/#{extra_prop_ref}/"

      if index.zero?
        # Create dossier via API
        response = HTTParty.post(
          "#{api_prefix}/dossiers_mgmt/dossier_from_url",
          body: {
            retrieval_end_point: retrieval_end_point,
            retrieval_portal: 'onthemarket'
          }.to_json,
          headers: {
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
            # Add any required authentication headers
          }
        )
        raise "Failed to create dossier: #{response.body}" unless response.success?

        dossier_data = JSON.parse(response.body)
        # dossier = RealtyDossier.find(dossier_data['realty_dossier']['id'])
        dossier_id = dossier_data['realty_dossier']['id']
      elsif dossier_id.present?
        # Add asset via API with VCR cassette
        # VCR.use_cassette("onthemarket_#{extra_prop_ref}_page_1") do
        response = HTTParty.put(
          "#{api_prefix}/dossiers_mgmt/add_asset_from_url", # Replace with actual API endpoint
          body: {
            dossier_id: dossier_id,
            retrieval_end_point: retrieval_end_point,
            retrieval_portal: 'onthemarket'
          }.to_json,
          headers: {
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
            # Add any required authentication headers
          }
        )

        raise "Failed to add asset: #{response.body}" unless response.success?

        asset_data = JSON.parse(response.body)
        puts asset_data
      else
        puts 'No dossier ID found, skipping asset addition'
      end
    end
  end
end
