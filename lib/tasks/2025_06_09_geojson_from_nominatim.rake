require 'httparty'
require 'json'

namespace :geojson do
  desc 'Fetch GeoJSON from nominatim'
  task from_nominatim: :environment do
    realty_asset = RealtyAsset.last
    location_name = "#{realty_asset.city}, #{realty_asset.country}"
    service = Geo::GeoJsonExtractor.new
    puts "fetching geojson for #{location_name}"
    result = service.fetch_geojson_from_nominatim(location_name) # ('CV11 6FA')
    realty_asset.asset_polygon = result
    realty_asset.save!
    puts result[:error] || JSON.pretty_generate(result)
  end
end
