require 'logger'

# Create a custom IO wrapper that adds timestamps
class TimestampedIO
  def initialize(io)
    @io = io
  end

  def write(message)
    timestamp = Time.now.strftime('%Y-%m-%d %H:%M:%S')
    @io.write("[#{timestamp}] #{message}")
  end

  def close
    @io.close
  end
end

namespace :h2c do
  # rake h2c:start_dossier_from_url_buenavista
  desc 'Start dossier from url'
  task start_dossier_from_url_buenavista: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    if VCR.turned_on?
      puts VCR.current_cassette.name if VCR.current_cassette
      #  For some reason, VCR gets turned on here.
      # Need to investigate further.
      # VCR.turn_off!
      # WebMock.allow_net_connect!
      # VCR.configuration.allow_http_connections_when_no_cassette? = true

      # Could disable it but decided I'll embrace it instead
      cassette_library_dir ||= 'spec/cassettes/listing_scrapes'
      VCR.configure do |config|
        config.allow_http_connections_when_no_cassette = true
        config.cassette_library_dir = cassette_library_dir
        config.hook_into :webmock
        config.ignore_localhost = true
        config.debug_logger = File.open('vcr.log', 'w')

        # # Create a Logger instance with a custom format
        # logger = Logger.new('vcr.log')
        # logger.formatter = proc do |severity, datetime, _progname, msg|
        #   "[#{datetime.strftime('%Y-%m-%d %H:%M:%S.%L')}] #{severity}: #{msg}\n"
        # end
        # config.debug_logger = logger

        # log_file = File.open('vcr.log', 'w')
        # config.debug_logger = TimestampedIO.new(log_file)
      end
    end

    property_reference_1 = 'R5018944'

    retrieval_end_point = "https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/#{property_reference_1}"
    # STEP 1
    # dossier = RealtyDossier.dossier_from_buenavista_url(retrieval_end_point)
    # above will in turn call PortalDossierCreator
    dossier = Creators::PortalDossierCreator.new.create_from_url(retrieval_end_point, 'buenavista')
    puts dossier
    # dossier = RealtyDossier.last

    # dossier_2 = RealtyDossier.find_by(dossier_starting_url: retrieval_end_point)
    # puts dossier_2.id
    # dossier_2.update_from_source_url(retrieval_end_point, 'buenavista')

    # # In the 1st pass it seems .primary_dossier_asset may be nil
    # # Might need to do a dossier.reload to get it..
    dossier.reload
    # # 9 April - below involves screenshot with playwright
    # # will make use of DossierInfoSourceFromCompositePhoto to create dossier_asset_parts
    # # STEP 2

    acp_result = dossier.primary_dossier_asset.analyse_composite_photo
    # # puts "Composite photo analysis result: #{acp_result}"

    # # analyse_composite_photo_plan_b
    # # is for when there are mistakes in the first
    # #  analyse_composite_photo
    # # Will have to figure out how to get it in the workflow...
    # acp_result = dossier.primary_dossier_asset.analyse_composite_photo_plan_b(
    #   force_plan_b_photos: true
    # )
    # puts "Composite photo analysis result: #{acp_result}"

    # STEP 3
    sections_result = dossier.primary_dossier_asset.classify_property_sections
    # above just calls classify_rooms
    puts "primary classify_property_sections result: #{sections_result}"

    property_reference_2 = 'R4943650'

    VCR.use_cassette("buenavista_#{property_reference_2}_page_1") do
      retrieval_end_point_2 = "https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/#{property_reference_2}"
      # asset_2 = dossier.add_asset_from_buenavista_url(retrieval_end_point_2)
      asset_2 = dossier.add_asset_from_portal_url(retrieval_end_point_2, 'buenavista')
      puts asset_2
    end

    dossier.secondary_dossier_assets.each do |sec_da|
      acp_result = sec_da.analyse_composite_photo
      puts "Composite photo analysis result: #{acp_result}"
      # below will create DossierAssetParts

      sections_result = sec_da.classify_property_sections
      puts "secondary classify_property_sections result: #{sections_result}"

      # once analyse_composite_photo has completed, we can run the below
      # It is after below that the DossierAssetsComparison is created
      # and the comparable becomes available on the site

      dc_res = dossier.create_detailed_comparison(sec_da.uuid)
      puts "Detailed comparison result: #{dc_res}"
    end

    # May 2025 - have to decide if I want to re-introduce below:

    # certificate_id = '**************-7795-6926'
    # scraper = RealtyScrapers::EpcScrapingService.new
    # retrieved_epc = scraper.scrape_and_save(certificate_id)

    # retrieved_epc[:epc_detail].realty_asset_uuid = dossier.primary_realty_asset.uuid
    # retrieved_epc[:epc_detail].save!

    # above means
    # dossier.primary_realty_asset.epc_details will include the new epc

    # TODO: have a method that when pased a URL will figure which of the
    # vendor specific dossier creators to use
    #  like     RealtyDossier.dossier_from_buenavista_url(retrieval_end_point)

    #     To create a dossier from start to finish after above we will need to::
    #
    # dossier.analyze_primary_photos(max_photos_to_analyze = 2)
    # 14 apr - analyze_primary_photos no longer the best...
    # should probably run a job to analyze the EPC before below
    # then
    # dossier.evaluate_with_photos_content
    # then
    # dossier.create_and_run_default_search
    # After above is run below will be set:
    # dossier.dossier_search_to_use
    # Should also have:
    # dossier.most_comparable_summary_listings
    # Which will be instances of:
    # http://localhost:3333/superwiser/summary_listings/37
    # As of 1 apr 2025 summary listings are not exposed to the FE
    # then
    # dossier.populate_dossier_listings_from_search
    # Above should create secondary_dossier_assets like so:
    # dossier.secondary_dossier_assets
  end
end
