# frozen_string_literal: true

namespace :test do
  desc 'Test GameSession integration with PriceEstimate and GameSessionResultsCalculator'
  task game_session_integration: :environment do
    puts "🎮 Testing GameSession Integration..."
    puts "=" * 60

    begin
      # Set up tenant context
      agency_tenant = AgencyTenant.first || create_test_agency_tenant
      ActsAsTenant.current_tenant = agency_tenant
      puts "✅ Using agency tenant: #{agency_tenant.subdomain}"

      # Create or find a Scoot
      scoot = create_test_scoot(agency_tenant)
      puts "✅ Created/found scoot: #{scoot.scoot_title}"

      # Create a RealtyGame
      realty_game = create_test_realty_game(agency_tenant, scoot)
      puts "✅ Created realty game: #{realty_game.game_title}"

      # Create some test listings
      sale_listings = create_test_sale_listings(agency_tenant, 3)
      puts "✅ Created #{sale_listings.count} sale listings"

      # Test 1: Create GameSession manually
      puts "\n🔍 Test 1: Creating GameSession manually..."
      game_session = GameSession.create!(
        uuid: SecureRandom.uuid,
        agency_tenant_uuid: agency_tenant.uuid,
        main_scoot_uuid: scoot.uuid,
        main_realty_game_uuid: realty_game.uuid,
        session_guest_name: 'Test Player',
        session_guest_title: 'Property Expert',
        game_session_details: {
          test_session: true,
          created_via: 'rake_task'
        }
      )
      puts "  ✅ Created GameSession: #{game_session.uuid}"

      # Test 2: Create PriceEstimates linked to GameSession
      puts "\n🔍 Test 2: Creating PriceEstimates linked to GameSession..."
      price_estimates = []
      
      sale_listings.each_with_index do |listing, index|
        estimate = PriceEstimate.create!(
          uuid: SecureRandom.uuid,
          agency_tenant_uuid: agency_tenant.uuid,
          scoot_uuid: scoot.uuid,
          listing_uuid: listing.uuid,
          game_session_id: game_session.uuid,
          estimated_price_cents: listing.price_sale_current_cents + rand(-50_000..50_000),
          price_at_time_of_estimate_cents: listing.price_sale_current_cents,
          estimate_currency: 'GBP',
          estimate_title: "Estimate for #{listing.title}",
          estimate_text: "Test estimate #{index + 1}",
          estimator_name: game_session.session_guest_name,
          is_for_sale_listing: true,
          estimate_details: {
            game_session_id: game_session.uuid,
            property_index: index,
            game_score: rand(60..100)
          }
        )
        price_estimates << estimate
      end
      puts "  ✅ Created #{price_estimates.count} price estimates"

      # Test 3: Test GameSession relationships
      puts "\n🔍 Test 3: Testing GameSession relationships..."
      puts "  - GameSession price_estimates count: #{game_session.price_estimates.count}"
      puts "  - GameSession total_estimates_count: #{game_session.total_estimates_count}"
      
      price_estimates.each do |estimate|
        session = estimate.game_session
        puts "  - PriceEstimate #{estimate.uuid[0..7]}... -> GameSession: #{session&.uuid&.[](0..7) || 'nil'}..."
      end

      # Test 4: Test GameSessionResultsCalculator
      puts "\n🔍 Test 4: Testing GameSessionResultsCalculator..."
      calculator = RealtyPunts::GameSessionResultsCalculator.new(game_session.uuid)
      result = calculator.call

      if result.success?
        puts "  ✅ Calculator succeeded!"
        puts "  - Total score: #{result.data[:player_results][:total_score]}"
        puts "  - Max possible score: #{result.data[:player_results][:max_possible_score]}"
        puts "  - Performance rating: #{result.data[:player_results][:performance_rating][:rating]}"
        puts "  - Session guest name: #{result.data[:player_results][:session_guest_name]}"
        puts "  - Game results count: #{result.data[:player_results][:game_results].count}"
        puts "  - Comparison summary count: #{result.data[:comparison_summary].count}"
        puts "  - Game session info: #{result.data[:game_session]['session_guest_name']}"
      else
        puts "  ❌ Calculator failed: #{result.error_message}"
      end

      # Test 5: Test backward compatibility
      puts "\n🔍 Test 5: Testing backward compatibility with estimate_details..."
      old_style_estimate = PriceEstimate.create!(
        uuid: SecureRandom.uuid,
        agency_tenant_uuid: agency_tenant.uuid,
        scoot_uuid: scoot.uuid,
        listing_uuid: sale_listings.first.uuid,
        estimated_price_cents: 500_000_00,
        estimate_currency: 'GBP',
        estimate_title: "Old Style Estimate",
        estimate_text: "Test backward compatibility",
        estimator_name: 'Legacy Player',
        is_for_sale_listing: true,
        estimate_details: {
          game_session_id: game_session.uuid,
          property_index: 99,
          game_score: 85
        }
      )
      
      # Test if the old style estimate can find the game session
      found_session = old_style_estimate.game_session
      puts "  ✅ Old style estimate found session: #{found_session&.session_guest_name || 'nil'}"
      
      # Test if the session can find the old style estimate
      session_estimates = game_session.price_estimates
      puts "  ✅ Session found #{session_estimates.count} estimates (including old style)"

      # Test 6: Test session summary
      puts "\n🔍 Test 6: Testing session summary..."
      summary = game_session.session_summary
      puts "  ✅ Session summary:"
      puts "    - UUID: #{summary[:uuid][0..7]}..."
      puts "    - Guest name: #{summary[:guest_name]}"
      puts "    - Estimates count: #{summary[:estimates_count]}"
      puts "    - Game title: #{summary[:game_title]}"

      puts "\n🎉 All GameSession integration tests completed successfully!"
      puts "=" * 60

    rescue StandardError => e
      puts "\n❌ Error occurred: #{e.message}"
      puts e.backtrace.first(5).join("\n")
      raise e
    ensure
      ActsAsTenant.current_tenant = nil
    end
  end

  private

  def create_test_agency_tenant
    AgencyTenant.create!(
      uuid: SecureRandom.uuid,
      subdomain: 'test-session-tenant',
      domain: 'example.com',
      is_provisioned: true
    )
  end

  def create_test_scoot(agency_tenant)
    Scoot.find_or_create_by(scoot_subdomain: 'test-session-scoot') do |scoot|
      scoot.uuid = SecureRandom.uuid
      scoot.agency_tenant_uuid = agency_tenant.uuid
      scoot.scoot_title = 'Test Session Scoot'
      scoot.access_token = SecureRandom.hex(16)
    end
  end

  def create_test_realty_game(agency_tenant, scoot)
    RealtyGame.create!(
      uuid: SecureRandom.uuid,
      agency_tenant_uuid: agency_tenant.uuid,
      scoot_uuid: scoot.uuid,
      game_title: 'Test Session Game',
      game_description: 'A test game to validate GameSession integration',
      game_default_currency: 'GBP',
      game_default_country: 'UK',
      game_default_locale: 'en',
      game_start_at: 1.hour.ago,
      game_end_at: 1.week.from_now
    )
  end

  def create_test_sale_listings(agency_tenant, count)
    listings = []
    count.times do |i|
      realty_asset = create_test_realty_asset(agency_tenant, i)
      listing = SaleListing.create!(
        uuid: SecureRandom.uuid,
        agency_tenant_uuid: agency_tenant.uuid,
        realty_asset_uuid: realty_asset.uuid,
        title: "Test Property #{i + 1}",
        price_sale_current_cents: (300_000 + (i * 50_000)) * 100,
        currency: 'GBP',
        visible: true
      )
      listings << listing
    end
    listings
  end

  def create_test_realty_asset(agency_tenant, index)
    RealtyAsset.create!(
      uuid: SecureRandom.uuid,
      agency_tenant_uuid: agency_tenant.uuid,
      street_address: "#{index + 1} Test Street",
      city: 'Test City',
      postal_code: "TE#{index + 1} 1ST",
      country: 'UK',
      count_bedrooms: 2 + (index % 3),
      count_bathrooms: 1 + (index % 2),
      constructed_area: 100 + (index * 20)
    )
  end
end
