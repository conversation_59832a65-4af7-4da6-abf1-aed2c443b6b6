# require 'uri'
# require 'net/http'
# require 'json'

namespace :h2c do
  # rake h2c:scrape_single_idealista_to_sale_listing
  desc 'Scrape Idealista to Sale Listing'
  task scrape_single_idealista_to_sale_listing: :environment do
    property_reference = '108332223' # Example property ID from the provided URL
    # You can change this to any other Idealista property ID
    
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    # Idealista URL format
    retrieval_end_point = "https://www.idealista.com/inmueble/#{property_reference}/"

    puts "Starting to scrape Idealista property: #{retrieval_end_point}"
    
    begin
      dossier = RealtyDossier.dossier_from_idealista_url(retrieval_end_point)
      
      if dossier
        puts "Successfully created dossier with ID: #{dossier.id}"
        puts "Dossier UUID: #{dossier.uuid}"
        puts "Primary sale listing: #{dossier.primary_sale_listing&.id}"
        puts "Title: #{dossier.dossier_display_title}"
      else
        puts "Failed to create dossier"
      end
    rescue StandardError => e
      puts "Error occurred: #{e.message}"
      puts e.backtrace.join("\n")
    end
  end

  # rake h2c:scrape_multiple_idealista_to_sale_listing
  desc 'Scrape Multiple Idealista Properties to Sale Listings'
  task scrape_multiple_idealista_to_sale_listing: :environment do
    # Array of Idealista property IDs to scrape
    property_references = [
      '108332223', # Example from provided URL
      # Add more property IDs here as needed
      # '108332224',
      # '108332225',
    ]
    
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    puts "Starting to scrape #{property_references.length} Idealista properties"
    
    successful_dossiers = []
    failed_properties = []
    
    property_references.each_with_index do |property_reference, index|
      retrieval_end_point = "https://www.idealista.com/inmueble/#{property_reference}/"
      
      puts "\n[#{index + 1}/#{property_references.length}] Processing: #{retrieval_end_point}"
      
      begin
        dossier = RealtyDossier.dossier_from_idealista_url(retrieval_end_point)
        
        if dossier
          successful_dossiers << {
            property_reference: property_reference,
            dossier_id: dossier.id,
            dossier_uuid: dossier.uuid,
            title: dossier.dossier_display_title
          }
          puts "✓ Successfully created dossier with ID: #{dossier.id}"
        else
          failed_properties << property_reference
          puts "✗ Failed to create dossier"
        end
      rescue StandardError => e
        failed_properties << property_reference
        puts "✗ Error occurred: #{e.message}"
      end
      
      # Add a small delay between requests to be respectful to the server
      sleep(2) if index < property_references.length - 1
    end
    
    puts "\n" + "="*50
    puts "SUMMARY"
    puts "="*50
    puts "Total properties processed: #{property_references.length}"
    puts "Successful dossiers created: #{successful_dossiers.length}"
    puts "Failed properties: #{failed_properties.length}"
    
    if successful_dossiers.any?
      puts "\nSuccessful dossiers:"
      successful_dossiers.each do |dossier_info|
        puts "- Property #{dossier_info[:property_reference]}: Dossier ID #{dossier_info[:dossier_id]} (#{dossier_info[:title]})"
      end
    end
    
    if failed_properties.any?
      puts "\nFailed properties:"
      failed_properties.each do |property_reference|
        puts "- #{property_reference}"
      end
    end
  end

  # rake h2c:test_idealista_scraping_with_mock_data
  desc 'Test Idealista Scraping Components with Mock Data'
  task test_idealista_scraping_with_mock_data: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    puts "Testing Idealista scraping components with mock data..."

    # Mock HTML content that represents what we would get from a successful Idealista scrape
    mock_idealista_html = <<~HTML
      <!DOCTYPE html>
      <html lang="es">
      <head>
        <title>Piso en venta en Calle Mayor, Madrid - Idealista</title>
      </head>
      <body>
        <h1>Piso en venta en Calle Mayor, Madrid</h1>
        <div class="price">350.000 €</div>
        <div class="description">
          Precioso piso en el centro de Madrid con 3 habitaciones y 2 baños.
          Totalmente reformado con cocina equipada y aire acondicionado.
        </div>
        <div class="features">
          <span>3 habitaciones</span>
          <span>2 baños</span>
          <span>120 m²</span>
          <span>Ascensor</span>
          <span>Aire acondicionado</span>
        </div>
        <div class="location">Calle Mayor, Madrid, 28013</div>
        <script type="application/json" id="property-data">
        {
          "property": {
            "id": "108332223",
            "title": "Piso en venta en Calle Mayor, Madrid",
            "price": {
              "amount": 350000,
              "currency": "EUR"
            },
            "description": "Precioso piso en el centro de Madrid con 3 habitaciones y 2 baños.",
            "bedrooms": 3,
            "bathrooms": 2,
            "area": 120,
            "location": {
              "address": "Calle Mayor, Madrid",
              "city": "Madrid",
              "postal_code": "28013",
              "latitude": 40.4168,
              "longitude": -3.7038
            },
            "features": ["Ascensor", "Aire acondicionado", "Reformado"],
            "images": [
              "https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image1.jpg",
              "https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image2.jpg"
            ],
            "property_type": "Apartment",
            "year_built": 1980,
            "energy": {
              "rating": "E",
              "performance": "150 kWh/m²"
            }
          }
        }
        </script>
        <img src="https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image1.jpg" alt="Property image 1">
        <img src="https://img.idealista.com/blur/WEB_LISTING/0/id.pro.es.image.master/image2.jpg" alt="Property image 2">
      </body>
      </html>
    HTML

    begin
      # Test 1: Create RealtyScrapedItem with mock data
      puts "\n1. Testing RealtyScrapedItem creation with mock data..."
      realty_scraped_item = RealtyScrapedItem.find_or_create_for_hpg('https://www.idealista.com/inmueble/108332223/')
      realty_scraped_item.update!(
        full_content_before_js: mock_idealista_html,
        scrape_is_idealista: true,
        scraped_content_column_name: 'full_content_before_js'
      )

      puts "✓ RealtyScrapedItem created with ID: #{realty_scraped_item.id}"
      puts "  - URL: #{realty_scraped_item.scrapable_url}"
      puts "  - Content length: #{realty_scraped_item.full_content_before_js&.length || 0}"

      # Test 2: Test pasarela processing
      puts "\n2. Testing IdealistaPasarela processing..."
      pasarela = Pasarelas::IdealistaPasarela.new(realty_scraped_item)
      pasarela.call

      realty_scraped_item.reload
      puts "✓ Pasarela processing completed"
      puts "  - Asset data present: #{realty_scraped_item.extracted_asset_data.present?}"
      puts "  - Listing data present: #{realty_scraped_item.extracted_listing_data.present?}"
      puts "  - Image URLs count: #{realty_scraped_item.extracted_image_urls&.length || 0}"

      if realty_scraped_item.extracted_asset_data.present?
        asset_data = realty_scraped_item.extracted_asset_data
        puts "  - Title: #{asset_data['title']}"
        puts "  - City: #{asset_data['city']}"
        puts "  - Bedrooms: #{asset_data['count_bedrooms']}"
        puts "  - Bathrooms: #{asset_data['count_bathrooms']}"
        puts "  - Area: #{asset_data['constructed_area']}"
        puts "  - Property type: #{asset_data['prop_type']}"
        puts "  - Energy rating: #{asset_data['energy_rating']}"
      end

      if realty_scraped_item.extracted_listing_data.present?
        listing_data = realty_scraped_item.extracted_listing_data
        puts "  - Price (cents): #{listing_data['price_sale_current_cents']}"
        puts "  - Currency: #{listing_data['price_sale_current_currency']}"
        puts "  - Reference: #{listing_data['property_reference']}"
      end

      # Test 3: Test ScrapeItemFromIdealista property_hash_from_scrape_item
      puts "\n3. Testing ScrapeItemFromIdealista property extraction..."
      scrape_item = ScrapeItemFromIdealista.find_or_create_for_h2c_idealista('https://www.idealista.com/inmueble/108332223/')
      scrape_item.update!(full_content_before_js: mock_idealista_html)

      property_hash = scrape_item.property_hash_from_scrape_item
      if property_hash
        puts "✓ Property hash extracted successfully"
        puts "  - Title: #{property_hash['title']}"
        puts "  - Price: #{property_hash['price']}"
        puts "  - Bedrooms: #{property_hash['bedrooms']}"
        puts "  - Area: #{property_hash['area']}"
      else
        puts "✗ Failed to extract property hash"
      end

    rescue StandardError => e
      puts "✗ Error during testing: #{e.message}"
      puts e.backtrace.join("\n")
    end

    puts "\nTesting completed!"
  end

  # rake h2c:test_idealista_scraping
  desc 'Test Idealista Scraping Components (with real scraping - may encounter bot detection)'
  task test_idealista_scraping: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    
    property_reference = '108332223'
    retrieval_end_point = "https://www.idealista.com/inmueble/#{property_reference}/"
    
    puts "Testing Idealista scraping components..."
    puts "URL: #{retrieval_end_point}"
    
    begin
      # Test 1: Create scrape item
      puts "\n1. Testing ScrapeItemFromIdealista creation..."
      scrape_item = ScrapeItemFromIdealista.find_or_create_for_h2c_idealista(retrieval_end_point)
      puts "✓ Scrape item created with ID: #{scrape_item.id}"
      puts "  - URL: #{scrape_item.scrapable_url}"
      puts "  - Host: #{scrape_item.scrape_uri_host}"
      puts "  - Content length: #{scrape_item.full_content_before_js&.length || 0}"
      
      # Test 2: Test pasarela processing with RealtyScrapedItem
      if scrape_item.full_content_before_js.present?
        puts "\n2. Testing IdealistaPasarela processing..."

        # Create a RealtyScrapedItem for pasarela testing
        realty_scraped_item = RealtyScrapedItem.find_or_create_for_hpg(retrieval_end_point)
        realty_scraped_item.update!(
          full_content_before_js: scrape_item.full_content_before_js,
          scrape_is_idealista: true,
          scraped_content_column_name: 'full_content_before_js'
        )

        pasarela = Pasarelas::IdealistaPasarela.new(realty_scraped_item)
        pasarela.call

        realty_scraped_item.reload
        puts "✓ Pasarela processing completed"
        puts "  - Asset data present: #{realty_scraped_item.extracted_asset_data.present?}"
        puts "  - Listing data present: #{realty_scraped_item.extracted_listing_data.present?}"
        puts "  - Image URLs count: #{realty_scraped_item.extracted_image_urls&.length || 0}"

        if realty_scraped_item.extracted_asset_data.present?
          asset_data = realty_scraped_item.extracted_asset_data
          puts "  - Title: #{asset_data['title']}"
          puts "  - City: #{asset_data['city']}"
          puts "  - Bedrooms: #{asset_data['count_bedrooms']}"
          puts "  - Bathrooms: #{asset_data['count_bathrooms']}"
          puts "  - Area: #{asset_data['constructed_area']}"
        end

        if realty_scraped_item.extracted_listing_data.present?
          listing_data = realty_scraped_item.extracted_listing_data
          puts "  - Price (cents): #{listing_data['price_sale_current_cents']}"
          puts "  - Currency: #{listing_data['price_sale_current_currency']}"
          puts "  - Reference: #{listing_data['property_reference']}"
        end
      else
        puts "✗ No content scraped - cannot test pasarela"
      end
      
      # Test 3: Test full dossier creation
      puts "\n3. Testing full dossier creation..."
      dossier = RealtyDossier.dossier_from_idealista_url(retrieval_end_point)
      
      if dossier
        puts "✓ Dossier created successfully"
        puts "  - Dossier ID: #{dossier.id}"
        puts "  - UUID: #{dossier.uuid}"
        puts "  - Title: #{dossier.dossier_display_title}"
        puts "  - Primary asset: #{dossier.primary_realty_asset&.id}"
        puts "  - Primary listing: #{dossier.primary_sale_listing&.id}"
        
        if dossier.primary_sale_listing
          listing = dossier.primary_sale_listing
          puts "  - Listing title: #{listing.title}"
          puts "  - Price: #{listing.price_sale_current}"
          puts "  - Bedrooms: #{listing.count_bedrooms}"
          puts "  - Bathrooms: #{listing.count_bathrooms}"
        end
      else
        puts "✗ Failed to create dossier"
      end
      
    rescue StandardError => e
      puts "✗ Error during testing: #{e.message}"
      puts e.backtrace.join("\n")
    end
    
    puts "\nTesting completed!"
  end
end
