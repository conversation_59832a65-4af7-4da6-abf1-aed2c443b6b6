# frozen_string_literal: true

namespace :test do
  desc 'Test RealtyGame relationships by creating a complete game setup with listings and guesses'
  task realty_game_relationships: :environment do
    puts '🎮 Starting RealtyGame Relationships Test...'
    puts '=' * 60

    begin
      # Set up tenant context
      agency_tenant = AgencyTenant.first || create_test_agency_tenant
      ActsAsTenant.current_tenant = agency_tenant
      puts "✅ Using agency tenant: #{agency_tenant.subdomain}"

      # Create or find a Scoot
      scoot = create_test_scoot(agency_tenant)
      puts "✅ Created/found scoot: #{scoot.scoot_title}"

      # Create some test listings
      sale_listings = create_test_sale_listings(agency_tenant, 3)
      rental_listings = create_test_rental_listings(agency_tenant, 2)
      puts "✅ Created #{sale_listings.count} sale listings and #{rental_listings.count} rental listings"

      # Create a RealtyGame
      realty_game = create_test_realty_game(agency_tenant, scoot)
      puts "✅ Created realty game: #{realty_game.game_title}"

      # Create RealtyGameListings
      game_listings = create_test_game_listings(realty_game, sale_listings, rental_listings)
      puts "✅ Created #{game_listings.count} game listings"

      # Create GameSessions
      game_sessions = create_test_game_sessions(agency_tenant, realty_game, 2)
      puts "✅ Created #{game_sessions.count} game sessions"

      # Create GuessedPrices
      guessed_prices = create_test_guessed_prices(agency_tenant, realty_game, game_listings, game_sessions)
      puts "✅ Created #{guessed_prices.count} guessed prices"

      # Test all relationships
      puts "\n🔍 Testing Relationships..."
      puts '-' * 40

      test_realty_game_relationships(realty_game)
      test_game_listing_relationships(game_listings.first)
      test_game_session_relationships(game_sessions.first)
      test_guessed_price_relationships(guessed_prices.first)

      # Test counter caches
      puts "\n📊 Testing Counter Caches..."
      puts '-' * 40
      test_counter_caches(realty_game, game_listings.first, game_sessions.first)

      # Display summary
      puts "\n📋 Final Summary..."
      puts '-' * 40
      display_summary(realty_game)

      puts "\n🎉 All tests completed successfully!"
      puts '=' * 60
    rescue StandardError => e
      puts "\n❌ Error occurred: #{e.message}"
      puts e.backtrace.first(5).join("\n")
      raise e
    ensure
      ActsAsTenant.current_tenant = nil
    end
  end

  private

  def create_test_agency_tenant
    AgencyTenant.create!(
      uuid: SecureRandom.uuid,
      subdomain: 'test-game-tenant',
      domain: 'example.com',
      is_provisioned: true
    )
  end

  def create_test_scoot(agency_tenant)
    Scoot.find_or_create_by(scoot_subdomain: 'test-game-scoot') do |scoot|
      scoot.uuid = SecureRandom.uuid
      scoot.agency_tenant_uuid = agency_tenant.uuid
      scoot.scoot_title = 'Test Game Scoot'
      scoot.access_token = SecureRandom.hex(16)
    end
  end

  def create_test_sale_listings(agency_tenant, count)
    listings = []
    count.times do |i|
      realty_asset = create_test_realty_asset(agency_tenant, i)
      listing = SaleListing.create!(
        uuid: SecureRandom.uuid,
        agency_tenant_uuid: agency_tenant.uuid,
        realty_asset_uuid: realty_asset.uuid,
        title: "Test Sale Property #{i + 1}",
        price_sale_current_cents: (300_000 + (i * 50_000)) * 100, # £300k, £350k, £400k
        currency: 'GBP',
        visible: true
      )
      listings << listing
    end
    listings
  end

  def create_test_rental_listings(agency_tenant, count)
    listings = []
    count.times do |i|
      realty_asset = create_test_realty_asset(agency_tenant, i + 10)
      listing = RentalListing.create!(
        uuid: SecureRandom.uuid,
        agency_tenant_uuid: agency_tenant.uuid,
        realty_asset_uuid: realty_asset.uuid,
        title: "Test Rental Property #{i + 1}",
        price_rental_monthly_standard_season_cents: (2000 + (i * 500)) * 100, # £2000, £2500
        price_rental_monthly_standard_season_currency: 'GBP',
        visible: true
      )
      listings << listing
    end
    listings
  end

  def create_test_realty_asset(agency_tenant, index)
    RealtyAsset.create!(
      uuid: SecureRandom.uuid,
      agency_tenant_uuid: agency_tenant.uuid,
      street_address: "#{index + 1} Test Street",
      city: 'Test City',
      postal_code: "TE#{index + 1} 1ST",
      country: 'UK',
      count_bedrooms: 2 + (index % 3),
      count_bathrooms: 1 + (index % 2),
      constructed_area: 100 + (index * 20)
    )
  end

  def create_test_realty_game(agency_tenant, scoot)
    RealtyGame.create!(
      uuid: SecureRandom.uuid,
      agency_tenant_uuid: agency_tenant.uuid,
      scoot_uuid: scoot.uuid,
      game_title: 'Test Property Price Guessing Game',
      game_description: 'A test game to validate all relationships work correctly',
      game_default_currency: 'GBP',
      game_default_country: 'UK',
      game_default_locale: 'en',
      game_start_at: 1.hour.ago,
      game_end_at: 1.week.from_now
    )
  end

  def create_test_game_listings(realty_game, sale_listings, rental_listings)
    game_listings = []

    # Add sale listings to game
    sale_listings.each do |listing|
      game_listing = RealtyGameListing.create!(
        uuid: SecureRandom.uuid,
        realty_game_uuid: realty_game.uuid,
        listing_uuid: listing.uuid,
        realty_asset_uuid: listing.realty_asset_uuid,
        is_sale_listing: true,
        is_rental_listing: false,
        realty_game_listing_details: { added_at: Time.current, listing_type: 'sale' }
      )
      game_listings << game_listing
    end

    # Add rental listings to game
    rental_listings.each do |listing|
      game_listing = RealtyGameListing.create!(
        uuid: SecureRandom.uuid,
        realty_game_uuid: realty_game.uuid,
        listing_uuid: listing.uuid,
        realty_asset_uuid: listing.realty_asset_uuid,
        is_sale_listing: false,
        is_rental_listing: true,
        realty_game_listing_details: { added_at: Time.current, listing_type: 'rental' }
      )
      game_listings << game_listing
    end

    game_listings
  end

  def create_test_game_sessions(agency_tenant, realty_game, count)
    sessions = []
    count.times do |i|
      session = GameSession.create!(
        uuid: SecureRandom.uuid,
        agency_tenant_uuid: agency_tenant.uuid,
        main_realty_game_uuid: realty_game.uuid,
        session_guest_name: "Test Player #{i + 1}",
        session_guest_title: "Property Expert #{i + 1}",
        game_session_details: {
          player_type: i.even? ? 'human' : 'ai',
          experience_level: %w[beginner intermediate expert].sample
        }
      )
      sessions << session
    end
    sessions
  end

  def create_test_guessed_prices(agency_tenant, realty_game, game_listings, game_sessions)
    guessed_prices = []

    game_listings.each_with_index do |game_listing, listing_index|
      game_sessions.each_with_index do |session, _session_index|
        # Create 1-2 guesses per listing per session
        (1..2).each do |guess_num|
          base_price = if game_listing.is_sale_listing?
                         300_000 + (listing_index * 50_000) # Base sale prices
                       else
                         2_000 + (listing_index * 500) # Base rental prices (monthly)
                       end

          # Add some variation to the guesses
          variation = rand(-20..20) # ±20% variation
          guessed_price = (base_price * (1 + variation / 100.0)).to_i

          guess = GuessedPrice.create!(
            uuid: SecureRandom.uuid,
            agency_tenant_uuid: agency_tenant.uuid,
            realty_game_uuid: realty_game.uuid,
            realty_game_listing_uuid: game_listing.uuid,
            game_session_uuid: session.uuid,
            listing_uuid: game_listing.listing_uuid,
            guessed_price_in_cents: guessed_price * 100,
            price_at_time_of_estimate_cents: base_price * 100,
            percentage_above_or_below: variation,
            guessed_price_currency: 'GBP',
            estimate_title: "Guess #{guess_num} for #{begin
              game_listing.listing.title
            rescue StandardError
              'Unknown Property'
            end}",
            estimate_text: "Estimated by #{session.session_guest_name}",
            estimator_name: session.session_guest_name,
            is_ai_estimate: session.session_guest_name.include?('AI'),
            guessed_price_details: {
              guess_number: guess_num,
              confidence_level: rand(1..10),
              reasoning: 'Based on location and property features'
            }
          )
          guessed_prices << guess
        end
      end
    end

    guessed_prices
  end

  def test_realty_game_relationships(realty_game)
    puts 'Testing RealtyGame relationships...'

    # Test has_many relationships
    puts "  - realty_game_listings: #{realty_game.realty_game_listings.count}"
    puts "  - game_sessions: #{realty_game.game_sessions.count}"
    puts "  - guessed_prices: #{realty_game.guessed_prices.count}"

    # Test belongs_to relationships
    puts "  - agency_tenant: #{realty_game.agency_tenant&.subdomain || 'nil'}"
    puts "  - scoot: #{realty_game.scoot&.scoot_title || 'nil'}"

    # Test that relationships work
    raise 'RealtyGame should have game listings' if realty_game.realty_game_listings.empty?
    raise 'RealtyGame should have game sessions' if realty_game.game_sessions.empty?
    raise 'RealtyGame should have guessed prices' if realty_game.guessed_prices.empty?

    puts '  ✅ All RealtyGame relationships working'
  end

  def test_game_listing_relationships(game_listing)
    puts 'Testing RealtyGameListing relationships...'

    # Test has_many relationships
    puts "  - guessed_prices: #{game_listing.guessed_prices.count}"

    # Test belongs_to relationships
    puts "  - realty_game: #{game_listing.realty_game&.game_title || 'nil'}"
    puts "  - realty_asset: #{game_listing.realty_asset&.street_address || 'nil'}"

    # Test polymorphic listing relationship
    listing = game_listing.listing
    puts "  - listing: #{listing&.class&.name || 'nil'} - #{listing&.title || 'nil'}"

    # Test that relationships work
    raise 'GameListing should have guessed prices' if game_listing.guessed_prices.empty?
    raise 'GameListing should have a realty game' unless game_listing.realty_game
    raise 'GameListing should have a listing' unless listing

    puts '  ✅ All RealtyGameListing relationships working'
  end

  def test_game_session_relationships(game_session)
    puts 'Testing GameSession relationships...'

    # Test has_many relationships
    puts "  - guessed_prices: #{game_session.guessed_prices.count}"

    # Test belongs_to relationships
    puts "  - agency_tenant: #{game_session.agency_tenant&.subdomain || 'nil'}"
    puts "  - realty_game: #{game_session.realty_game&.game_title || 'nil'}"
    puts "  - scoot: #{game_session.scoot&.scoot_title || 'nil'}"

    # Test that relationships work
    raise 'GameSession should have guessed prices' if game_session.guessed_prices.empty?
    raise 'GameSession should have an agency tenant' unless game_session.agency_tenant

    puts '  ✅ All GameSession relationships working'
  end

  def test_guessed_price_relationships(guessed_price)
    puts 'Testing GuessedPrice relationships...'

    # Test belongs_to relationships
    puts "  - agency_tenant: #{guessed_price.agency_tenant&.subdomain || 'nil'}"
    puts "  - realty_game: #{guessed_price.realty_game&.game_title || 'nil'}"
    puts "  - realty_game_listing: #{guessed_price.realty_game_listing&.uuid || 'nil'}"
    puts "  - game_session: #{guessed_price.game_session&.session_guest_name || 'nil'}"

    # Test helper methods
    puts "  - formatted_guessed_price: #{guessed_price.formatted_guessed_price}"
    puts "  - estimate_type: #{guessed_price.estimate_type}"
    puts "  - accuracy_indicator: #{guessed_price.accuracy_indicator}"

    # Test that relationships work
    raise 'GuessedPrice should have an agency tenant' unless guessed_price.agency_tenant
    raise 'GuessedPrice should have a realty game' unless guessed_price.realty_game
    raise 'GuessedPrice should have a game listing' unless guessed_price.realty_game_listing
    raise 'GuessedPrice should have a game session' unless guessed_price.game_session

    puts '  ✅ All GuessedPrice relationships working'
  end

  def test_counter_caches(realty_game, game_listing, game_session)
    puts 'Testing counter caches...'

    # Test RealtyGame counters
    expected_guesses = realty_game.guessed_prices.count
    actual_guesses = realty_game.guessed_prices_count
    puts "  - RealtyGame guessed_prices_count: #{actual_guesses} (expected: #{expected_guesses})"

    # Test RealtyGameListing counters
    expected_listing_guesses = game_listing.guessed_prices.count
    actual_listing_guesses = game_listing.guessed_prices_count
    puts "  - RealtyGameListing guessed_prices_count: #{actual_listing_guesses} (expected: #{expected_listing_guesses})"

    # Test GameSession counters
    expected_session_guesses = game_session.guessed_prices.count
    actual_session_guesses = game_session.guessed_prices_count
    puts "  - GameSession guessed_prices_count: #{actual_session_guesses} (expected: #{expected_session_guesses})"

    puts '  ✅ All counter caches working'
  end

  def display_summary(realty_game)
    puts "Game: #{realty_game.game_title}"
    puts "Total Game Listings: #{realty_game.realty_game_listings.count}"
    puts "  - Sale Listings: #{realty_game.realty_game_listings.for_sale.count}"
    puts "  - Rental Listings: #{realty_game.realty_game_listings.for_rental.count}"
    puts "Total Game Sessions: #{realty_game.game_sessions.count}"
    puts "Total Guessed Prices: #{realty_game.guessed_prices.count}"
    puts "  - AI Estimates: #{realty_game.guessed_prices.ai_estimates.count}"
    puts "  - Human Estimates: #{realty_game.guessed_prices.count - realty_game.guessed_prices.ai_estimates.count}"

    # Show some sample data
    puts "\nSample Guessed Prices:"
    realty_game.guessed_prices.limit(3).each do |guess|
      puts "  - #{guess.formatted_guessed_price} by #{guess.estimator_name} (#{guess.estimate_type})"
    end
  end

  desc 'Clean up test data created by realty_game_relationships task'
  task cleanup_realty_game_test_data: :environment do
    puts '🧹 Cleaning up RealtyGame test data...'
    puts '=' * 50

    begin
      # Find test agency tenant
      agency_tenant = AgencyTenant.find_by(subdomain: 'test-game-tenant')

      if agency_tenant
        ActsAsTenant.current_tenant = agency_tenant

        # Clean up in reverse order of dependencies
        puts 'Cleaning up GuessedPrices...'
        GuessedPrice.where(agency_tenant_uuid: agency_tenant.uuid).destroy_all

        puts 'Cleaning up GameSessions...'
        GameSession.where(agency_tenant_uuid: agency_tenant.uuid).destroy_all

        puts 'Cleaning up RealtyGameListings...'
        RealtyGameListing.joins(:realty_game)
                         .where(realty_games: { agency_tenant_uuid: agency_tenant.uuid })
                         .destroy_all

        puts 'Cleaning up RealtyGames...'
        RealtyGame.where(agency_tenant_uuid: agency_tenant.uuid).destroy_all

        puts 'Cleaning up Listings...'
        SaleListing.where(agency_tenant_uuid: agency_tenant.uuid).destroy_all
        RentalListing.where(agency_tenant_uuid: agency_tenant.uuid).destroy_all

        puts 'Cleaning up RealtyAssets...'
        RealtyAsset.where(agency_tenant_uuid: agency_tenant.uuid).destroy_all

        puts 'Cleaning up Scoot...'
        Scoot.where(agency_tenant_uuid: agency_tenant.uuid).destroy_all

        puts 'Cleaning up AgencyTenant...'
        agency_tenant.destroy

        puts '✅ Test data cleanup completed successfully!'
      else
        puts 'ℹ️  No test data found to clean up.'
      end
    rescue StandardError => e
      puts "❌ Error during cleanup: #{e.message}"
      puts e.backtrace.first(3).join("\n")
    ensure
      ActsAsTenant.current_tenant = nil
    end
  end
end
