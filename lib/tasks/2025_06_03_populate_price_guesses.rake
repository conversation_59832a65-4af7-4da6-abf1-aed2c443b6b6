namespace :psq do
  # rake psq:populate_price_guesses
  desc 'Populate the database with 20 random price guesses within 40% of the real price'
  task populate_price_guesses: :environment do
    require 'securerandom'
    require 'faker'
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    ss = Scoot.find_by(scoot_subdomain: 'nuneaton')

    # Define possible values
    estimate_titles = ['Guess for Property A', 'Estimate for House B', 'Price Guess C', 'Market Estimate D']
    estimator_names = 10.times.map { Faker::Name.unique.name }

    puts 'Creating random price guesses...'

    estimator_names.each do |estimator_name|
      puts "Estimator name: #{estimator_name}"
      game_session_id = SecureRandom.uuid
      ss.listings_for_price_guess.each do |price_guess_listing|
        # Calculate price range within ±40% of price_sale_current_cents
        real_price_cents = price_guess_listing.price_sale_current_cents
        next unless real_price_cents&.positive? # Skip if price is nil or invalid

        min_price = (real_price_cents * 0.4).to_i # 40% of real price
        max_price = (real_price_cents * 1.4).to_i # 140% of real price

        estimated_price_cents = rand(min_price..max_price)
        price_estimate = PriceEstimate.new(
          # will use is_protected below to signify estimates
          # autogen by rake task
          is_protected: true,
          listing_uuid: price_guess_listing.uuid,
          game_session_id: game_session_id,
          estimated_price_cents: estimated_price_cents,
          price_at_time_of_estimate_cents: price_guess_listing.price_sale_current_cents,
          estimate_currency: price_guess_listing.currency,
          estimate_title: estimate_titles.sample,
          estimate_text: Faker::Lorem.sentence(word_count: 10),
          estimator_name: estimator_name,
          is_ai_estimate: false,
          estimate_details: {},
          updated_at: Time.now,
          created_at: Time.now
        )

        if price_estimate.save
          puts "Created PriceEstimate by #{estimator_name} with UUID: #{price_estimate.uuid}"
        else
          puts "Failed to create PriceEstimate by #{estimator_name}: #{price_estimate.errors.full_messages.join(', ')}"
        end
      end
    end

    puts 'Finished populating random price guesses.'
  end
end
