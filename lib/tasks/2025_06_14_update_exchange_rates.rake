require 'net/http'
require 'json'

namespace :currency do
  desc 'Update currency exchange rates from exchangerate-api.com'
  task update_rates: :environment do
    # Fetch latest rates from API
    uri = URI('https://api.exchangerate-api.com/v4/latest/GBP')
    response = Net::HTTP.get(uri)
    api_data = JSON.parse(response)

    # Load existing currency file
    currency_file = Rails.root.join('config', 'currencyRates.json')
    raise "Currency file not found at #{currency_file}" unless File.exist?(currency_file)

    current_data = JSON.parse(File.read(currency_file))

    # Get supported currencies
    supported_currencies = current_data['supportedCurrencies']

    # Update lastUpdated timestamp
    current_data['lastUpdated'] = Time.now.utc.strftime('%Y-%m-%dT%H:%M:%SZ')

    # Calculate exchange rates for each supported currency
    updated_rates = {}
    supported_currencies.each do |base|
      updated_rates[base] = {}
      supported_currencies.each do |target|
        next if base == target

        if base == 'GBP'
          # Direct rate from API
          updated_rates[base][target] = api_data['rates'][target].to_f.round(3)
        else
          # Calculate cross-rate through GBP
          # Rate = (Target/GBP) / (Base/GBP)
          rate = (api_data['rates'][target] / api_data['rates'][base]).round(3)
          updated_rates[base][target] = rate
        end
      end
    end

    # Update exchange rates in current data
    current_data['exchangeRates'] = updated_rates

    # Write updated data back to file
    File.write(currency_file, JSON.pretty_generate(current_data))
    puts "Currency rates successfully updated at #{Time.now.utc}"
  rescue StandardError => e
    puts "Error updating currency rates: #{e.message}"
  end
end
