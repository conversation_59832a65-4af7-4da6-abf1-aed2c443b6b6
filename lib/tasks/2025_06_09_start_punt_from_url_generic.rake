namespace :h2c do
  desc 'Start punt from url with input from file'
  task start_punt_from_url_generic: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    # # Configure VCR if enabled
    # if VCR.turned_on?
    #   puts VCR.current_cassette.name if VCR.current_cassette
    #   cassette_library_dir ||= 'spec/cassettes/listing_scrapes'
    #   VCR.configure do |config|
    #     config.allow_http_connections_when_no_cassette = true
    #     config.cassette_library_dir = cassette_library_dir
    #     config.hook_into :webmock
    #     config.ignore_localhost = true
    #     config.debug_logger = File.open('vcr.log', 'w')
    #   end
    # end

    # Read input from file
    input_file = ENV['INPUT_FILE'] || 'db/realty_punts/latest.json'
    unless File.exist?(input_file)
      puts "Input file not found: #{input_file}"
      exit 1
    end

    input_data = JSON.parse(File.read(input_file))
    api_prefix = input_data['api_prefix'] || 'https://be-medo.propertywebbuilder.com/api_mgmt/v4'
    retrieval_portal = input_data['retrieval_portal'] || ''
    property_refs = input_data['property_refs'] || []

    if property_refs.empty?
      puts 'No property references found in input file'
      exit 1
    end

    # property_reference_1 = 'R5018944'
    # STEP 1

    # Initialize service
    service = RealtyPunts::RealtyPuntCreator.new(api_prefix)

    # Process property references
    dossier_id = nil
    property_refs.each_with_index do |extra_prop_ref, index|
      if retrieval_portal == 'buenavista'
        retrieval_end_point = "https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/#{extra_prop_ref}"
      elsif retrieval_portal == 'onthemarket'
        retrieval_end_point = "https://www.onthemarket.com/details/#{extra_prop_ref}/"
      end
      if index.zero?
        dossier_id = service.create_dossier_from_url(retrieval_end_point, retrieval_portal)
      elsif dossier_id.present?
        # VCR.use_cassette("buenavista_#{extra_prop_ref}_page_1") do
        asset_data = service.add_asset_to_dossier(dossier_id, retrieval_end_point, retrieval_portal)
        puts asset_data
        # end
      else
        puts 'No dossier ID found, skipping asset addition'
      end
    end
  end
end
