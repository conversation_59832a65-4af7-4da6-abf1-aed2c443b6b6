namespace :test do
  desc 'Test GameSessionResultsCalculator with database persistence'
  task game_session_results_persistence: :environment do
    puts "\n🧪 Testing GameSessionResultsCalculator Database Persistence"
    puts "=" * 60

    begin
      # Find or create test data
      agency_tenant = AgencyTenant.first
      unless agency_tenant
        puts "❌ No AgencyTenant found. Please create one first."
        exit 1
      end

      scoot = agency_tenant.scoots.first
      unless scoot
        puts "❌ No Scoot found. Please create one first."
        exit 1
      end

      realty_game = scoot.realty_games.first
      unless realty_game
        puts "❌ No RealtyGame found. Please create one first."
        exit 1
      end

      # Test 1: Create GameSession with guessed prices
      puts "\n🔍 Test 1: Creating GameSession with GuessedPrices..."
      game_session = GameSession.create!(
        uuid: SecureRandom.uuid,
        agency_tenant_uuid: agency_tenant.uuid,
        main_scoot_uuid: scoot.uuid,
        main_realty_game_uuid: realty_game.uuid,
        session_guest_name: 'Test Player',
        session_guest_title: 'Property Expert',
        game_session_details: {
          test_session: true,
          created_via: 'rake_task'
        }
      )
      puts "  ✅ Created GameSession: #{game_session.uuid}"

      # Create some test guessed prices
      guessed_prices = []
      3.times do |i|
        guessed_price = game_session.guessed_prices.create!(
          uuid: SecureRandom.uuid,
          agency_tenant_uuid: agency_tenant.uuid,
          listing_uuid: SecureRandom.uuid,
          realty_game_uuid: realty_game.uuid,
          guessed_price_in_cents: (500_000 + rand(200_000)), # £5000-£7000
          guessed_price_currency: 'GBP',
          price_at_time_of_estimate_cents: (550_000 + rand(100_000)), # £5500-£6500
          estimate_title: "Test Property #{i + 1}",
          estimate_text: "Test estimate for property #{i + 1}",
          score_for_guess: rand(80) + 20, # 20-100 score
          guessed_price_details: {
            property_index: i,
            estimate_vicinity: "Test Area #{i + 1}"
          }
        )
        guessed_prices << guessed_price
      end
      puts "  ✅ Created #{guessed_prices.count} GuessedPrices"

      # Test 2: Check initial state
      puts "\n🔍 Test 2: Checking initial state..."
      puts "  - GameSession results_calculated?: #{game_session.results_calculated?}"
      puts "  - GameSession total_score: #{game_session.total_score || 'nil'}"
      puts "  - GameSession performance_summary: #{game_session.performance_summary || 'nil'}"

      # Test 3: Run GameSessionResultsCalculator
      puts "\n🔍 Test 3: Running GameSessionResultsCalculator..."
      calculator = RealtyPunts::GameSessionResultsCalculator.new(game_session.uuid)
      result = calculator.call

      if result.success?
        puts "  ✅ Calculator succeeded"
        puts "  - Player total score: #{result.data[:player_results][:total_score]}"
        puts "  - Max possible score: #{result.data[:player_results][:max_possible_score]}"
        puts "  - Performance rating: #{result.data[:player_results][:performance_rating][:rating]}"
      else
        puts "  ❌ Calculator failed: #{result.error_message}"
        exit 1
      end

      # Test 4: Check saved results
      puts "\n🔍 Test 4: Checking saved results..."
      game_session.reload
      puts "  - GameSession results_calculated?: #{game_session.results_calculated?}"
      puts "  - GameSession total_score: #{game_session.total_score}"
      puts "  - GameSession max_possible_score: #{game_session.max_possible_score}"
      puts "  - GameSession performance_percentage: #{game_session.performance_percentage}%"
      puts "  - GameSession performance_rating_text: #{game_session.performance_rating_text}"
      puts "  - GameSession results_calculated_at: #{game_session.results_calculated_at}"

      performance_summary = game_session.performance_summary
      if performance_summary
        puts "  - Performance summary available: ✅"
        puts "    - Rating: #{performance_summary[:rating][:text]}"
        puts "    - Icon: #{performance_summary[:rating][:icon]}"
        puts "    - Color: #{performance_summary[:rating][:color]}"
      else
        puts "  - Performance summary: ❌ Not available"
      end

      # Test 5: Test session summary
      puts "\n🔍 Test 5: Testing session summary..."
      summary = game_session.session_summary
      puts "  - Session summary includes results_calculated: #{summary[:results_calculated]}"
      puts "  - Session summary includes performance_summary: #{summary[:performance_summary].present?}"

      puts "\n✅ All tests completed successfully!"
      puts "🎉 GameSessionResultsCalculator database persistence is working!"

    rescue StandardError => e
      puts "\n❌ Test failed with error: #{e.message}"
      puts e.backtrace.first(5).join("\n")
      exit 1
    ensure
      # Cleanup
      if defined?(game_session) && game_session&.persisted?
        puts "\n🧹 Cleaning up test data..."
        game_session.destroy
        puts "  ✅ Cleaned up GameSession and related records"
      end
    end
  end
end
