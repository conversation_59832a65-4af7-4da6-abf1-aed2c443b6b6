namespace :zoopla_pasarela do
  desc "Test ZooplaPasarela service with existing Zoopla scrape items"
  task test: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    
    puts "Testing ZooplaPasarela service..."
    puts "=" * 50
    
    # Find Zoopla scrape items
    zoopla_items = ScrapeItem.where(scrape_uri_host: 'www.zoopla.co.uk')
    
    if zoopla_items.empty?
      puts "❌ No Zoopla scrape items found"
      puts "Please scrape some Zoopla URLs first"
      exit 1
    end
    
    puts "Found #{zoopla_items.count} Zoopla scrape item(s)"
    puts
    
    zoopla_items.each_with_index do |scrape_item, index|
      puts "Processing item #{index + 1}/#{zoopla_items.count}:"
      puts "URL: #{scrape_item.scrapable_url}"
      
      # Run the pasarela service
      pasarela = Pasarelas::ZooplaPasarela.new(scrape_item)
      
      begin
        pasarela.call
        scrape_item.reload
        
        extracted_data = scrape_item.extra_scrape_item_details
        
        if extracted_data && extracted_data['extracted_listing_data']
          listing_data = extracted_data['extracted_listing_data']
          asset_data = extracted_data['extracted_asset_data']
          image_urls = extracted_data['extracted_image_urls']
          
          puts "✅ Successfully extracted data:"
          puts "   Title: #{listing_data['title']}"
          puts "   Price: #{listing_data['price_sale_current_cents']} cents (#{listing_data['price_sale_current_currency']})"
          puts "   Bedrooms: #{asset_data['count_bedrooms']}"
          puts "   Bathrooms: #{asset_data['count_bathrooms']}"
          puts "   Property Type: #{asset_data['prop_type_key']}"
          puts "   Country: #{asset_data['country']}"
          puts "   City: #{asset_data['city']}"
          puts "   Images: #{image_urls&.length || 0} images"
          puts "   Reference: #{listing_data['reference']}"
        else
          puts "❌ No data extracted - check if the scrape item contains valid Zoopla data"
        end
        
      rescue => e
        puts "❌ Error processing item: #{e.message}"
        puts "   #{e.backtrace.first}"
      end
      
      puts
      puts "-" * 30
      puts
    end
    
    puts "ZooplaPasarela testing completed!"
  end
  
  desc "Test ZooplaPasarela service with a specific scrape item ID"
  task :test_item, [:scrape_item_id] => :environment do |t, args|
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    
    scrape_item_id = args[:scrape_item_id]
    
    if scrape_item_id.blank?
      puts "❌ Please provide a scrape item ID"
      puts "Usage: rake zoopla_pasarela:test_item[123]"
      exit 1
    end
    
    scrape_item = ScrapeItem.find_by(id: scrape_item_id)
    
    if scrape_item.nil?
      puts "❌ Scrape item with ID #{scrape_item_id} not found"
      exit 1
    end
    
    puts "Testing ZooplaPasarela with scrape item ID: #{scrape_item_id}"
    puts "URL: #{scrape_item.scrapable_url}"
    puts "Host: #{scrape_item.scrape_uri_host}"
    puts "=" * 50
    
    # Run the pasarela service
    pasarela = Pasarelas::ZooplaPasarela.new(scrape_item)
    
    begin
      pasarela.call
      scrape_item.reload
      
      extracted_data = scrape_item.extra_scrape_item_details
      
      if extracted_data && extracted_data['extracted_listing_data']
        listing_data = extracted_data['extracted_listing_data']
        asset_data = extracted_data['extracted_asset_data']
        image_urls = extracted_data['extracted_image_urls']
        
        puts "✅ Successfully extracted data:"
        puts
        puts "LISTING DATA:"
        puts "  Title: #{listing_data['title']}"
        puts "  Description: #{listing_data['description']&.truncate(100)}"
        puts "  Price: #{listing_data['price_sale_current_cents']} cents"
        puts "  Currency: #{listing_data['price_sale_current_currency']}"
        puts "  Reference: #{listing_data['reference']}"
        puts "  Published: #{listing_data['publish_from']}"
        puts "  Visible: #{listing_data['visible']}"
        puts "  Furnished: #{listing_data['furnished']}"
        puts
        puts "ASSET DATA:"
        puts "  Property Type: #{asset_data['prop_type_key']}"
        puts "  Bedrooms: #{asset_data['count_bedrooms']}"
        puts "  Bathrooms: #{asset_data['count_bathrooms']}"
        puts "  Floor Area: #{asset_data['constructed_area']} sq ft"
        puts "  Country: #{asset_data['country']}"
        puts "  City: #{asset_data['city']}"
        puts "  Address: #{asset_data['street_address']}"
        puts "  Postal Code: #{asset_data['postal_code']}"
        puts "  Coordinates: #{asset_data['latitude']}, #{asset_data['longitude']}"
        puts
        puts "IMAGES:"
        puts "  Count: #{image_urls&.length || 0}"
        if image_urls&.any?
          image_urls.first(3).each_with_index do |url, i|
            puts "  #{i + 1}. #{url}"
          end
          puts "  ... and #{image_urls.length - 3} more" if image_urls.length > 3
        end
        puts
        puts "FEATURES:"
        if asset_data['categories']&.any?
          asset_data['categories'].each do |category|
            puts "  - #{category['name']}"
          end
        end
        
      else
        puts "❌ No data extracted"
        puts "Raw extra_scrape_item_details: #{extracted_data.inspect}"
        
        # Debug information
        puts
        puts "DEBUG INFO:"
        puts "  Has full_content_before_js: #{scrape_item.full_content_before_js.present?}"
        puts "  Content length: #{scrape_item.full_content_before_js&.length || 0}"
        puts "  Has script_json: #{scrape_item.script_json.present?}"
        puts "  Script JSON type: #{scrape_item.script_json.class}"
      end
      
    rescue => e
      puts "❌ Error processing item: #{e.message}"
      puts "Backtrace:"
      e.backtrace.first(5).each { |line| puts "  #{line}" }
    end
  end
  
  desc "Show available Zoopla scrape items"
  task list: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    
    zoopla_items = ScrapeItem.where(scrape_uri_host: 'www.zoopla.co.uk')
                             .order(created_at: :desc)
    
    if zoopla_items.empty?
      puts "No Zoopla scrape items found"
      exit 0
    end
    
    puts "Available Zoopla scrape items:"
    puts "=" * 60
    
    zoopla_items.each do |item|
      puts "ID: #{item.id}"
      puts "URL: #{item.scrapable_url}"
      puts "Created: #{item.created_at}"
      puts "Has content: #{item.full_content_before_js.present?}"
      puts "Has script_json: #{item.script_json.present?}"
      puts "Already processed: #{item.extra_scrape_item_details.present?}"
      puts "-" * 40
    end
  end
end
