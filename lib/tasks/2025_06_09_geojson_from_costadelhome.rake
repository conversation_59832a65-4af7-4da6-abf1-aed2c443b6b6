require 'httparty'
require 'json'

namespace :geojson do
  desc 'Fetch map.js from costadelhome.com and extract GeoJSON from boundary points'
  task extract_from_map_js: :environment do
    # problem with below is finding the correct code:
    url = 'https://www.costadelhome.com/properties/560343/map.js'
    service = Geo::GeoJsonExtractor.new(url)
    result = service.call
    puts result[:error] || JSON.pretty_generate(result)
  end
end
