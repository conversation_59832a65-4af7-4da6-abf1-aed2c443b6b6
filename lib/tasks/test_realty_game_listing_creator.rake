# frozen_string_literal: true

namespace :test do
  desc 'Test the updated RealtyGameListingCreator service'
  task realty_game_listing_creator: :environment do
    puts "🎮 Testing RealtyGameListingCreator Service..."
    puts "=" * 60

    begin
      # Set up tenant context
      agency_tenant = AgencyTenant.first || create_test_agency_tenant
      ActsAsTenant.current_tenant = agency_tenant
      puts "✅ Using agency tenant: #{agency_tenant.subdomain}"

      # Create or find a Scoot
      scoot = create_test_scoot(agency_tenant)
      puts "✅ Created/found scoot: #{scoot.scoot_title}"

      # Create a RealtyGame
      realty_game = create_test_realty_game(agency_tenant, scoot)
      puts "✅ Created realty game: #{realty_game.game_title}"

      # Test URLs for different portals
      test_urls = [
        'https://www.zoopla.co.uk/for-sale/details/12345678/',
        'https://www.rightmove.co.uk/properties/123456789',
        'https://www.onthemarket.com/details/12345678/',
        'https://www.purplebricks.co.uk/property-for-sale/12345678',
        'https://www.buenavistahomes.eu/api_public/v3/en/resales/sales/12345'
      ]

      creator = Creators::RealtyGameListingCreator.new

      test_urls.each_with_index do |url, index|
        puts "\n🔍 Testing URL #{index + 1}: #{url}"
        
        begin
          # Test portal detection
          detected_portal = creator.send(:detect_portal_from_url, url)
          puts "  ✅ Detected portal: #{detected_portal}"
          
          # Note: We can't actually create listings without valid URLs and scraping setup
          # This would require real property URLs and working scraper infrastructure
          puts "  ℹ️  Would create RealtyGameListing for game: #{realty_game.uuid}"
          puts "  ℹ️  Portal: #{detected_portal}, URL: #{url}"
          
        rescue Creators::RealtyGameListingCreator::UnknownPortalError => e
          puts "  ❌ Portal detection failed: #{e.message}"
        rescue StandardError => e
          puts "  ❌ Error: #{e.message}"
        end
      end

      # Test with invalid URLs
      puts "\n🚫 Testing invalid URLs..."
      invalid_urls = [
        'https://www.example.com/property/123',
        'not-a-url',
        'https://unknown-portal.com/listing/123'
      ]

      invalid_urls.each do |url|
        puts "\n🔍 Testing invalid URL: #{url}"
        begin
          creator.send(:detect_portal_from_url, url)
          puts "  ❌ Should have failed but didn't"
        rescue Creators::RealtyGameListingCreator::UnknownPortalError => e
          puts "  ✅ Correctly rejected: #{e.message}"
        rescue Creators::RealtyGameListingCreator::InvalidUrlError => e
          puts "  ✅ Correctly rejected invalid URL: #{e.message}"
        rescue StandardError => e
          puts "  ✅ Correctly rejected: #{e.message}"
        end
      end

      # Test validation methods
      puts "\n🔍 Testing validation methods..."
      
      # Test valid UUID
      begin
        creator.send(:validate_inputs, realty_game.uuid, 'https://www.zoopla.co.uk/test', 'zoopla')
        puts "  ✅ Valid inputs accepted"
      rescue StandardError => e
        puts "  ❌ Valid inputs rejected: #{e.message}"
      end

      # Test invalid UUID
      begin
        creator.send(:validate_inputs, 'invalid-uuid', 'https://www.zoopla.co.uk/test', 'zoopla')
        puts "  ❌ Invalid UUID should have been rejected"
      rescue ArgumentError => e
        puts "  ✅ Invalid UUID correctly rejected: #{e.message}"
      end

      # Test invalid portal
      begin
        creator.send(:validate_inputs, realty_game.uuid, 'https://www.zoopla.co.uk/test', 'unknown')
        puts "  ❌ Invalid portal should have been rejected"
      rescue Creators::RealtyGameListingCreator::UnknownPortalError => e
        puts "  ✅ Invalid portal correctly rejected: #{e.message}"
      end

      puts "\n📋 Service API Summary:"
      puts "-" * 40
      puts "Main method: create_game_listing_from_url(realty_game_uuid, url, portal = nil)"
      puts "  - realty_game_uuid: UUID of the RealtyGame to add listing to"
      puts "  - url: Property listing URL from supported portal"
      puts "  - portal: Optional portal name (auto-detected if nil)"
      puts ""
      puts "Supported portals: #{Creators::RealtyGameListingCreator::PORTAL_CONFIG.keys.join(', ')}"
      puts ""
      puts "Example usage:"
      puts "  creator = Creators::RealtyGameListingCreator.new"
      puts "  game_listing = creator.create_game_listing_from_url("
      puts "    '#{realty_game.uuid}',"
      puts "    'https://www.zoopla.co.uk/for-sale/details/12345678/'"
      puts "  )"

      puts "\n🎉 RealtyGameListingCreator service test completed successfully!"
      puts "=" * 60

    rescue StandardError => e
      puts "\n❌ Error occurred: #{e.message}"
      puts e.backtrace.first(5).join("\n")
      raise e
    ensure
      ActsAsTenant.current_tenant = nil
    end
  end

  private

  def create_test_agency_tenant
    AgencyTenant.create!(
      uuid: SecureRandom.uuid,
      subdomain: 'test-creator-tenant',
      domain: 'example.com',
      is_provisioned: true
    )
  end

  def create_test_scoot(agency_tenant)
    Scoot.find_or_create_by(scoot_subdomain: 'test-creator-scoot') do |scoot|
      scoot.uuid = SecureRandom.uuid
      scoot.agency_tenant_uuid = agency_tenant.uuid
      scoot.scoot_title = 'Test Creator Scoot'
      scoot.access_token = SecureRandom.hex(16)
    end
  end

  def create_test_realty_game(agency_tenant, scoot)
    RealtyGame.create!(
      uuid: SecureRandom.uuid,
      agency_tenant_uuid: agency_tenant.uuid,
      scoot_uuid: scoot.uuid,
      game_title: 'Test Game Listing Creator Game',
      game_description: 'A test game to validate the RealtyGameListingCreator service',
      game_default_currency: 'GBP',
      game_default_country: 'UK',
      game_default_locale: 'en',
      game_start_at: 1.hour.ago,
      game_end_at: 1.week.from_now
    )
  end
end
