require 'logger'
namespace :h2c do
  desc 'Start realty_game from url with input from file, auto-detect portal, and comprehensive logging'
  task start_realty_game_from_url_generic_v2: :environment do
    logger = Logger.new('log/realty_game_task.log')
    logger.level = Logger::DEBUG

    begin
      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
      logger.info "Set current tenant: #{ActsAsTenant.current_tenant&.id}"

      input_file = 'db/realty_punts/latest.json'
      logger.info "Reading input file: #{input_file}"

      unless File.exist?(input_file)
        logger.error "Input file not found: #{input_file}"
        puts "Input file not found: #{input_file}"
        exit 1
      end

      input_data = JSON.parse(File.read(input_file))
      property_urls = input_data['property_urls'] || []
      if property_urls.empty?
        logger.error 'No property URLs found in input file'
        puts 'No property URLs found in input file'
        exit 1
      end
      first_url = property_urls.first
      logger.info "First property URL: #{first_url}"

      # Use the same portal detection logic as the service
      def detect_portal_from_url(url)
        case url
        when /buenavistahomes\.eu/
          'buenavista'
        when /onthemarket\.com/
          'onthemarket'
        when /zoopla\.co\.uk/
          'zoopla'
        when /rightmove\.co\.uk/
          'rightmove'
        when /purplebricks\.co\.uk/
          'purplebricks'
        else
          'unknown'
        end
      end
      portal = detect_portal_from_url(first_url)
      logger.info "Detected portal: #{portal}"

      service = RealtyPunts::RealtyPuntCreator.new(nil, logger)
      logger.info "Initialized RealtyPuntCreator for portal: #{portal}"

      result = if input_data['pre_scrape']
                 logger.info 'Using pre-scraped content'
                 service.create_realty_game_with_pre_scraped_content(input_file)
               else
                 logger.info 'Scraping content'
                 service.create_realty_game(input_file)
               end

      logger.info "Game with id: #{result} created for portal: #{portal}"
      puts "Game with id: #{result} created for portal: #{portal}"
    rescue => e
      logger.error "Error: #{e.message}"
      logger.error e.backtrace.join("\n")
      puts "An error occurred. Check log/realty_game_task.log for details."
      raise
    end
  end
end
