# require 'uri'
# require 'net/http'
# require 'json'

namespace :h2c do
  # rake h2c:scrape_single_zillow_to_sale_listing
  desc 'Scrape Zillow to Sale Listing'
  task scrape_single_zillow_to_sale_listing: :environment do
    property_zpid = '33066770' # Example property ZPID from the provided URL
    # You can change this to any other Zillow property ZPID
    
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    # Zillow URL format
    retrieval_end_point = "https://www.zillow.com/homedetails/90-Prospect-Dr-Chappaqua-NY-10514/#{property_zpid}_zpid/"

    puts "Starting to scrape Zillow property: #{retrieval_end_point}"
    
    begin
      dossier = RealtyDossier.dossier_from_zillow_url(retrieval_end_point)
      
      if dossier
        puts "Successfully created dossier with ID: #{dossier.id}"
        puts "Dossier UUID: #{dossier.uuid}"
        puts "Primary sale listing: #{dossier.primary_sale_listing&.id}"
        puts "Title: #{dossier.dossier_display_title}"
      else
        puts "Failed to create dossier"
      end
    rescue StandardError => e
      puts "Error occurred: #{e.message}"
      puts e.backtrace.join("\n")
    end
  end

  # rake h2c:scrape_multiple_zillow_to_sale_listing
  desc 'Scrape Multiple Zillow Properties to Sale Listings'
  task scrape_multiple_zillow_to_sale_listing: :environment do
    # Array of Zillow property ZPIDs to scrape
    property_zpids = [
      '33066770', # Example from provided URL
      # Add more property ZPIDs here as needed
      # '12345678',
      # '87654321',
    ]
    
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    puts "Starting to scrape #{property_zpids.length} Zillow properties"
    
    successful_dossiers = []
    failed_properties = []
    
    property_zpids.each_with_index do |property_zpid, index|
      # Note: You'll need to construct the full URL with the actual address
      # This is a simplified example - in practice you'd need the full address
      retrieval_end_point = "https://www.zillow.com/homedetails/property-address/#{property_zpid}_zpid/"
      
      puts "\n[#{index + 1}/#{property_zpids.length}] Processing: #{retrieval_end_point}"
      
      begin
        dossier = RealtyDossier.dossier_from_zillow_url(retrieval_end_point)
        
        if dossier
          successful_dossiers << {
            property_zpid: property_zpid,
            dossier_id: dossier.id,
            dossier_uuid: dossier.uuid,
            title: dossier.dossier_display_title
          }
          puts "✓ Successfully created dossier with ID: #{dossier.id}"
        else
          failed_properties << property_zpid
          puts "✗ Failed to create dossier"
        end
      rescue StandardError => e
        failed_properties << property_zpid
        puts "✗ Error occurred: #{e.message}"
      end
      
      # Add a small delay between requests to be respectful to the server
      sleep(2) if index < property_zpids.length - 1
    end
    
    puts "\n" + "="*50
    puts "SUMMARY"
    puts "="*50
    puts "Total properties processed: #{property_zpids.length}"
    puts "Successful dossiers created: #{successful_dossiers.length}"
    puts "Failed properties: #{failed_properties.length}"
    
    if successful_dossiers.any?
      puts "\nSuccessful dossiers:"
      successful_dossiers.each do |dossier_info|
        puts "- Property #{dossier_info[:property_zpid]}: Dossier ID #{dossier_info[:dossier_id]} (#{dossier_info[:title]})"
      end
    end
    
    if failed_properties.any?
      puts "\nFailed properties:"
      failed_properties.each do |property_zpid|
        puts "- #{property_zpid}"
      end
    end
  end

  # rake h2c:test_zillow_scraping_with_mock_data
  desc 'Test Zillow Scraping Components with Mock Data'
  task test_zillow_scraping_with_mock_data: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    
    puts "Testing Zillow scraping components with mock data..."
    
    # Mock HTML content that represents what we would get from a successful Zillow scrape
    mock_zillow_html = <<~HTML
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <title>90 Prospect Drive, Chappaqua, NY 10514 | MLS #873236 | Zillow</title>
      </head>
      <body>
        <h1>90 Prospect Drive, Chappaqua, NY 10514</h1>
        <div data-testid="price">$1,599,000</div>
        <div data-testid="description">
          Welcome to 90 Prospect Drive, a timeless Dutch Colonial nestled on over an acre of picturesque, 
          park-like land in the heart of Chappaqua's walkable village center. This sun-filled 4-bedroom, 
          2.5-bath home blends storybook charm with modern luxury.
        </div>
        <div class="features">
          <span>4 bedrooms</span>
          <span>3 bathrooms</span>
          <span>3,087 sqft</span>
          <span>1.17 Acres Lot</span>
          <span>Built in 1936</span>
          <span>Single Family Residence</span>
        </div>
        <script id="__NEXT_DATA__" type="application/json">
        {
          "props": {
            "pageProps": {
              "property": {
                "zpid": "33066770",
                "streetAddress": "90 Prospect Drive, Chappaqua, NY 10514",
                "price": {
                  "value": 1599000,
                  "currency": "USD"
                },
                "description": "Welcome to 90 Prospect Drive, a timeless Dutch Colonial nestled on over an acre of picturesque, park-like land in the heart of Chappaqua's walkable village center.",
                "bedrooms": 4,
                "bathrooms": 3,
                "livingArea": 3087,
                "lotSize": 1.17,
                "yearBuilt": 1936,
                "homeType": "SingleFamily",
                "address": {
                  "streetAddress": "90 Prospect Drive",
                  "city": "Chappaqua",
                  "state": "NY",
                  "zipcode": "10514",
                  "latitude": 41.1595,
                  "longitude": -73.7648
                },
                "photos": [
                  "https://photos.zillowstatic.com/fp/b3275c56180cc84001bf5c317e3814be-cc_ft_960.jpg",
                  "https://photos.zillowstatic.com/fp/0236b5da0d2dc0d6fee259a81f6931fd-cc_ft_576.jpg"
                ],
                "features": ["Hardwood floors", "Fireplace", "Central air", "Garage"],
                "parkingSpaces": 2
              }
            }
          }
        }
        </script>
        <img src="https://photos.zillowstatic.com/fp/b3275c56180cc84001bf5c317e3814be-cc_ft_960.jpg" alt="Property image 1">
        <img src="https://photos.zillowstatic.com/fp/0236b5da0d2dc0d6fee259a81f6931fd-cc_ft_576.jpg" alt="Property image 2">
      </body>
      </html>
    HTML
    
    begin
      # Test 1: Create RealtyScrapedItem with mock data
      puts "\n1. Testing RealtyScrapedItem creation with mock data..."
      realty_scraped_item = RealtyScrapedItem.find_or_create_for_hpg('https://www.zillow.com/homedetails/90-Prospect-Dr-Chappaqua-NY-10514/33066770_zpid/')
      realty_scraped_item.update!(
        full_content_before_js: mock_zillow_html,
        scrape_is_zillow: true,
        scraped_content_column_name: 'full_content_before_js'
      )
      
      puts "✓ RealtyScrapedItem created with ID: #{realty_scraped_item.id}"
      puts "  - URL: #{realty_scraped_item.scrapable_url}"
      puts "  - Content length: #{realty_scraped_item.full_content_before_js&.length || 0}"
      
      # Test 2: Test pasarela processing
      puts "\n2. Testing ZillowPasarela processing..."
      pasarela = Pasarelas::ZillowPasarela.new(realty_scraped_item)
      pasarela.call
      
      realty_scraped_item.reload
      puts "✓ Pasarela processing completed"
      puts "  - Asset data present: #{realty_scraped_item.extracted_asset_data.present?}"
      puts "  - Listing data present: #{realty_scraped_item.extracted_listing_data.present?}"
      puts "  - Image URLs count: #{realty_scraped_item.extracted_image_urls&.length || 0}"
      
      if realty_scraped_item.extracted_asset_data.present?
        asset_data = realty_scraped_item.extracted_asset_data
        puts "  - Title: #{asset_data['title']}"
        puts "  - City: #{asset_data['city']}"
        puts "  - Bedrooms: #{asset_data['count_bedrooms']}"
        puts "  - Bathrooms: #{asset_data['count_bathrooms']}"
        puts "  - Area: #{asset_data['constructed_area']}"
        puts "  - Property type: #{asset_data['prop_type']}"
        puts "  - Year built: #{asset_data['year_construction']}"
      end
      
      if realty_scraped_item.extracted_listing_data.present?
        listing_data = realty_scraped_item.extracted_listing_data
        puts "  - Price (cents): #{listing_data['price_sale_current_cents']}"
        puts "  - Currency: #{listing_data['price_sale_current_currency']}"
        puts "  - Reference: #{listing_data['property_reference']}"
      end
      
      # Test 3: Test ScrapeItemFromZillow property_hash_from_scrape_item
      puts "\n3. Testing ScrapeItemFromZillow property extraction..."
      scrape_item = ScrapeItemFromZillow.find_or_create_for_h2c_zillow('https://www.zillow.com/homedetails/90-Prospect-Dr-Chappaqua-NY-10514/33066770_zpid/')
      scrape_item.update!(full_content_before_js: mock_zillow_html)
      
      property_hash = scrape_item.property_hash_from_scrape_item
      if property_hash
        puts "✓ Property hash extracted successfully"
        puts "  - Title: #{property_hash['title']}"
        puts "  - Price: #{property_hash['price']}"
        puts "  - Bedrooms: #{property_hash['bedrooms']}"
        puts "  - Area: #{property_hash['area']}"
        puts "  - Property type: #{property_hash['property_type']}"
      else
        puts "✗ Failed to extract property hash"
      end
      
    rescue StandardError => e
      puts "✗ Error during testing: #{e.message}"
      puts e.backtrace.join("\n")
    end
    
    puts "\nTesting completed!"
  end

  # rake h2c:test_zillow_scraping
  desc 'Test Zillow Scraping Components (with real scraping - may encounter bot detection)'
  task test_zillow_scraping: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    
    property_zpid = '33066770'
    retrieval_end_point = "https://www.zillow.com/homedetails/90-Prospect-Dr-Chappaqua-NY-10514/#{property_zpid}_zpid/"
    
    puts "Testing Zillow scraping components..."
    puts "URL: #{retrieval_end_point}"
    
    begin
      # Test 1: Create scrape item
      puts "\n1. Testing ScrapeItemFromZillow creation..."
      scrape_item = ScrapeItemFromZillow.find_or_create_for_h2c_zillow(retrieval_end_point)
      puts "✓ Scrape item created with ID: #{scrape_item.id}"
      puts "  - URL: #{scrape_item.scrapable_url}"
      puts "  - Host: #{scrape_item.scrape_uri_host}"
      puts "  - Content length: #{scrape_item.full_content_before_js&.length || 0}"
      
      # Test 2: Test pasarela processing with RealtyScrapedItem
      if scrape_item.full_content_before_js.present?
        puts "\n2. Testing ZillowPasarela processing..."
        
        # Create a RealtyScrapedItem for pasarela testing
        realty_scraped_item = RealtyScrapedItem.find_or_create_for_hpg(retrieval_end_point)
        realty_scraped_item.update!(
          full_content_before_js: scrape_item.full_content_before_js,
          scrape_is_zillow: true,
          scraped_content_column_name: 'full_content_before_js'
        )
        
        pasarela = Pasarelas::ZillowPasarela.new(realty_scraped_item)
        pasarela.call
        
        realty_scraped_item.reload
        puts "✓ Pasarela processing completed"
        puts "  - Asset data present: #{realty_scraped_item.extracted_asset_data.present?}"
        puts "  - Listing data present: #{realty_scraped_item.extracted_listing_data.present?}"
        puts "  - Image URLs count: #{realty_scraped_item.extracted_image_urls&.length || 0}"
        
        if realty_scraped_item.extracted_asset_data.present?
          asset_data = realty_scraped_item.extracted_asset_data
          puts "  - Title: #{asset_data['title']}"
          puts "  - City: #{asset_data['city']}"
          puts "  - Bedrooms: #{asset_data['count_bedrooms']}"
          puts "  - Bathrooms: #{asset_data['count_bathrooms']}"
          puts "  - Area: #{asset_data['constructed_area']}"
        end
        
        if realty_scraped_item.extracted_listing_data.present?
          listing_data = realty_scraped_item.extracted_listing_data
          puts "  - Price (cents): #{listing_data['price_sale_current_cents']}"
          puts "  - Currency: #{listing_data['price_sale_current_currency']}"
          puts "  - Reference: #{listing_data['property_reference']}"
        end
      else
        puts "✗ No content scraped - cannot test pasarela"
      end
      
      # Test 3: Test full dossier creation
      puts "\n3. Testing full dossier creation..."
      dossier = RealtyDossier.dossier_from_zillow_url(retrieval_end_point)
      
      if dossier
        puts "✓ Dossier created successfully"
        puts "  - Dossier ID: #{dossier.id}"
        puts "  - UUID: #{dossier.uuid}"
        puts "  - Title: #{dossier.dossier_display_title}"
        puts "  - Primary asset: #{dossier.primary_realty_asset&.id}"
        puts "  - Primary listing: #{dossier.primary_sale_listing&.id}"
        
        if dossier.primary_sale_listing
          listing = dossier.primary_sale_listing
          puts "  - Listing title: #{listing.title}"
          puts "  - Price: #{listing.price_sale_current}"
          puts "  - Bedrooms: #{listing.count_bedrooms}"
          puts "  - Bathrooms: #{listing.count_bathrooms}"
        end
      else
        puts "✗ Failed to create dossier"
      end
      
    rescue StandardError => e
      puts "✗ Error during testing: #{e.message}"
      puts e.backtrace.join("\n")
    end
    
    puts "\nTesting completed!"
  end
end
