namespace :participant_analytics do
  desc 'Sync all participants from <PERSON>oy visits'
  task sync_all: :environment do
    puts "Starting full participant sync..."
    start_time = Time.current
    
    ParticipantSyncJob.perform_now
    
    end_time = Time.current
    duration = (end_time - start_time).round(2)
    
    puts "Participant sync completed in #{duration} seconds"
    puts "Total participants: #{Participant.count}"
  end

  desc 'Sync participants in background job'
  task sync_background: :environment do
    puts "Enqueuing participant sync job..."
    ParticipantSyncJob.perform_later
    puts "Job enqueued successfully"
  end

  desc 'Sync specific participant by visitor token'
  task :sync_participant, [:visitor_token] => :environment do |t, args|
    visitor_token = args[:visitor_token]
    
    if visitor_token.blank?
      puts "Usage: rake participant_analytics:sync_participant[VISITOR_TOKEN]"
      exit 1
    end
    
    puts "Syncing participant: #{visitor_token}"
    ParticipantSyncJob.perform_now(visitor_token: visitor_token)
    puts "Sync completed"
  end

  desc 'Clear participant analytics cache'
  task clear_cache: :environment do
    puts "Clearing participant analytics cache..."
    
    cache_patterns = [
      'participant_overview_*',
      'participants_over_time_*',
      'visit_distribution_*',
      'engagement_scores_*',
      'behavior_categories_*',
      'device_types_*',
      'traffic_sources_*',
      'geographic_distribution_*',
      'visit_frequency_*',
      'session_duration_*',
      'top_participants_*',
      'cohort_analysis_*'
    ]
    
    cleared_count = 0
    cache_patterns.each do |pattern|
      cleared_count += Rails.cache.delete_matched(pattern)
    end
    
    puts "Cleared #{cleared_count} cache entries"
  end

  desc 'Generate sample participant data for testing'
  task generate_sample_data: :environment do
    puts "Generating sample participant data..."
    
    # Create sample visitors with different behavior patterns
    sample_visitors = [
      { token: 'explorer_001', visits: 15, events: 45, category: 'explorer' },
      { token: 'engaged_001', visits: 8, events: 35, category: 'engaged' },
      { token: 'regular_001', visits: 5, events: 12, category: 'regular' },
      { token: 'casual_001', visits: 2, events: 3, category: 'casual' },
      { token: 'inactive_001', visits: 1, events: 1, category: 'inactive' }
    ]
    
    sample_visitors.each do |visitor_data|
      # Create Ahoy visits
      visitor_data[:visits].times do |i|
        visit = Ahoy::Visit.create!(
          visitor_token: visitor_data[:token],
          visit_token: SecureRandom.uuid,
          started_at: rand(30.days).seconds.ago,
          ip: "192.168.1.#{rand(255)}",
          user_agent: 'Sample User Agent',
          referrer: ['https://google.com', 'https://facebook.com', nil].sample,
          landing_page: "/page#{rand(10)}",
          country: ['United States', 'United Kingdom', 'Canada'].sample,
          city: ['New York', 'London', 'Toronto'].sample,
          device_type: ['Desktop', 'Mobile', 'Tablet'].sample,
          browser: ['Chrome', 'Firefox', 'Safari'].sample,
          os: ['Windows', 'macOS', 'iOS', 'Android'].sample
        )
        
        # Create events for this visit
        events_per_visit = visitor_data[:events] / visitor_data[:visits]
        events_per_visit.times do |j|
          Ahoy::Event.create!(
            visit: visit,
            name: ['$view', 'click', 'scroll', 'form_submit'].sample,
            properties: { page: "/page#{rand(10)}", action: "action_#{j}" },
            time: visit.started_at + rand(30.minutes)
          )
        end
      end
      
      puts "Created sample data for #{visitor_data[:token]} (#{visitor_data[:category]})"
    end
    
    # Sync the sample data
    puts "Syncing sample participant data..."
    ParticipantSyncJob.perform_now
    
    puts "Sample data generation completed!"
    puts "Total participants: #{Participant.count}"
  end

  desc 'Show participant analytics summary'
  task summary: :environment do
    puts "\n=== Participant Analytics Summary ==="
    puts "Total Participants: #{Participant.count}"
    puts "Active Participants (last 30 days): #{Participant.active_in_period(30.days.ago, Time.current).count}"
    puts "New Participants (last 30 days): #{Participant.first_visit_in_period(30.days.ago, Time.current).count}"
    puts "Returning Participants: #{Participant.returning.count}"
    puts "High Engagement Participants: #{Participant.high_engagement.count}"
    
    puts "\n=== Behavior Categories ==="
    categories = Participant.all.group_by(&:behavior_category)
    categories.each do |category, participants|
      puts "#{category.capitalize}: #{participants.count}"
    end
    
    puts "\n=== Top 5 Most Engaged Participants ==="
    top_participants = Participant.all.sort_by(&:engagement_score).reverse.first(5)
    top_participants.each_with_index do |participant, index|
      puts "#{index + 1}. #{participant.visitor_token[0..8]}... (Score: #{participant.engagement_score}, Visits: #{participant.total_visits})"
    end
    
    puts "\n=== Recent Activity ==="
    recent_participants = Participant.where('last_visit_at > ?', 7.days.ago).order(last_visit_at: :desc).limit(5)
    recent_participants.each do |participant|
      puts "#{participant.visitor_token[0..8]}... - Last visit: #{participant.last_visit_at.strftime('%Y-%m-%d %H:%M')}"
    end
  end

  desc 'Validate participant data integrity'
  task validate: :environment do
    puts "Validating participant data integrity..."
    
    issues = []
    
    # Check for participants without visitor tokens
    participants_without_tokens = Participant.where(visitor_token: [nil, ''])
    if participants_without_tokens.any?
      issues << "Found #{participants_without_tokens.count} participants without visitor tokens"
    end
    
    # Check for duplicate visitor tokens
    duplicate_tokens = Participant.group(:visitor_token).having('COUNT(*) > 1').count
    if duplicate_tokens.any?
      issues << "Found #{duplicate_tokens.count} duplicate visitor tokens"
    end
    
    # Check for participants with invalid engagement metrics
    invalid_engagement = Participant.where('total_visits < 0 OR total_events < 0')
    if invalid_engagement.any?
      issues << "Found #{invalid_engagement.count} participants with invalid engagement metrics"
    end
    
    # Check for orphaned participants (no corresponding Ahoy visits)
    orphaned_count = 0
    Participant.find_each do |participant|
      unless Ahoy::Visit.exists?(visitor_token: participant.visitor_token)
        orphaned_count += 1
      end
    end
    
    if orphaned_count > 0
      issues << "Found #{orphaned_count} orphaned participants (no corresponding Ahoy visits)"
    end
    
    if issues.empty?
      puts "✅ All participant data is valid!"
    else
      puts "❌ Found the following issues:"
      issues.each { |issue| puts "  - #{issue}" }
    end
  end

  desc 'Setup periodic sync job (requires whenever gem or similar)'
  task setup_periodic_sync: :environment do
    puts "To set up periodic participant sync, add this to your cron jobs:"
    puts "0 2 * * * cd #{Rails.root} && #{RbConfig.ruby} bin/rails participant_analytics:sync_background RAILS_ENV=#{Rails.env}"
    puts ""
    puts "Or if using the whenever gem, add this to config/schedule.rb:"
    puts "every 1.day, at: '2:00 am' do"
    puts "  rake 'participant_analytics:sync_background'"
    puts "end"
  end
end
