require 'httparty'
require 'json'

namespace :geojson do
  desc 'Fetch map.js from costadelhome.com and extract GeoJSO<PERSON> from boundary points'
  task extract_from_map_js_old: :environment do
    # URL of the JavaScript file
    url = 'https://www.costadelhome.com/properties/560343/map.js'
    # above corresponds to:
    # https://www.costadelhome.com/villa-with-sea-views-for-sale-in-la-quinta-560343
    # https://www.buenavistahomes.eu/en/s/properties/R560343
    # file:///Users/<USER>/dev/sites-2024-may/pwb-pro-be/db/external_sites/jitty-com/polygon-2-benahavis.html
    begin
      # Fetch the JavaScript file content
      response = HTTParty.get(url)
      raise "Failed to fetch #{url}: #{response.code} #{response.message}" unless response.success?

      @html_snippet = response.body

      # Extract the boundary points string using regex
      # Looks for the value attribute in the boundary_points input field
      # match = @html_snippet.match(/<input[^>]*id=["']boundary_points["'][^>]*value=["']([^"']+)["']/)
      # raise 'No boundary points found in the JavaScript content' unless match

      match = @html_snippet.match(/<input[^>]*id=\\?["']boundary_points\\?["'][^>]*value=\\?["']([^"']+)\\?["']/)

      points_string = match[1]

      # Split the string into individual coordinates and parse to floats
      coords = points_string.split(',').each_slice(2).map do |lat, lon|
        [lon.to_f, lat.to_f] # GeoJSON uses [longitude, latitude] order
      end

      # Ensure the polygon is closed (first and last points match)
      coords << coords.first unless coords.first == coords.last

      # Construct GeoJSON Polygon
      geojson = {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [coords] # Single array of coords for a simple polygon
        },
        properties: {
          source: 'Extracted from https://www.costadelhome.com/properties/560343/map.js',
          extracted_at: Time.now.utc.to_s
        }
      }

      # Output the GeoJSON (you can modify this to save to a file or database)
      puts 'Extracted GeoJSON:'
      puts JSON.pretty_generate(geojson)

      # below would work too
      # geojson_2 = extract_geojson

      # # Optional: Save to a file
      # File.open(Rails.root.join('tmp', 'boundary_geojson.json'), 'w') do |file|
      #   file.write(JSON.pretty_generate(geojson))
      # end
      # puts 'GeoJSON saved to tmp/boundary_geojson.json'
    rescue StandardError => e
      puts "Error: #{e.message}"
    end
  end

  def extract_geojson
    raw_coords = extract_coordinates_string
    return nil unless raw_coords

    coordinates = raw_coords
                  .split(',')
                  .each_slice(2)
                  .map { |lat, lng| [lng.to_f, lat.to_f] }

    # Ensure the polygon is closed
    coordinates << coordinates.first unless coordinates.first == coordinates.last

    {
      type: 'Feature',
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates]
      },
      properties: {}
    }
  end

  def extract_coordinates_string
    match = @html_snippet.match(/id=\\?"boundary_points\\?".*?value=\\?"([^\\"]+)\\?/)
    return match[1] if match

    # fallback: try using a greedy match
    match = @html_snippet.match(/id=\\?"boundary_points\\?".*?value=\\?"(.*?)\\?"/)
    match ? match[1] : nil
  end
end
