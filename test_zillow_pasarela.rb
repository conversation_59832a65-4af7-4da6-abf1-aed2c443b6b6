#!/usr/bin/env ruby

require_relative 'config/environment'

ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

html = File.read("manual_html/listing.html")
url = "https://www.zillow.com/homedetails/7404-Verna-Bethany-Rd-Myakka-City-FL-34251/99678108_zpid/"

puts "=== TESTING ZILLOW PASARELA ==="
puts "HTML length: #{html.length}"

# Create RealtyScrapedItem
rsi = RealtyScrapedItem.find_or_create_for_hpg(url)
rsi.update!(web_scraper_name: "ManualPaste", scraped_content_column_name: "full_content_before_js")
rsi.update_column("full_content_before_js", html)

puts "RSI ID: #{rsi.id}"

# Test the pasarela
begin
  pasarela = Pasarelas::ZillowPasarela.new(rsi)
  pasarela.call
  rsi.reload

  puts "=== RESULTS ==="
  puts "Asset data present: #{rsi.extracted_asset_data.present?}"
  puts "Listing data present: #{rsi.extracted_listing_data.present?}"
  puts "Images count: #{rsi.extracted_image_urls&.length || 0}"

  if rsi.extracted_asset_data.present?
    asset = rsi.extracted_asset_data
    puts "=== ASSET DATA ==="
    puts "Title: #{asset["title"]}"
    puts "City: #{asset["city"]}"
    puts "Bedrooms: #{asset["count_bedrooms"]}"
    puts "Bathrooms: #{asset["count_bathrooms"]}"
    puts "Area: #{asset["constructed_area"]}"
    puts "Property type: #{asset["prop_type"]}"
    puts "Year built: #{asset["year_construction"]}"
  end

  if rsi.extracted_listing_data.present?
    listing = rsi.extracted_listing_data
    puts "=== LISTING DATA ==="
    puts "Title: #{listing["title"]}"
    puts "Price (cents): #{listing["price_sale_current_cents"]}"
    puts "Currency: #{listing["price_sale_current_currency"]}"
    puts "Reference: #{listing["property_reference"]}"
    puts "Description: #{listing["description"]&.first(100)}..."
  end

rescue => e
  puts "ERROR: #{e.message}"
  puts e.backtrace.first(5)
end
