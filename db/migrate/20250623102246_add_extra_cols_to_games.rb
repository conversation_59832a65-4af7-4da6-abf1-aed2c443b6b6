class AddExtraColsToGames < ActiveRecord::Migration[7.2]
  def change
    add_column :game_sessions, :llm_interaction_uuid, :uuid
    add_column :game_sessions, :one_off_price_guesses_count, :integer, default: 0

    # This is a bit of an experiment to see the way each
    # of the different array types work:
    add_column :scoots, :landing_page_game_ids, :string, array: true, default: []
    add_column :scoots, :valid_game_ids, :text, array: true, default: []
    add_column :scoots, :single_guess_game_ids, :jsonb, array: true, default: []

    add_column :realty_games, :one_off_mgmt_code, :string, default: ''
    add_column :realty_games, :is_one_off_game, :boolean, default: false
    add_column :realty_games, :is_paid_game, :boolean, default: false

    add_column :realty_games, :video_url_for_game, :string # , default: ''
    add_column :realty_games, :is_public_listed_game, :boolean, default: false

    # This is the most important!!
    add_column :realty_games, :game_global_slug, :string, default: ''
    # I currently have a global_game_slug method which was returning game_default_locale
    # as a chapuzo
    add_column :realty_games, :available_game_listings_count, :integer, default: 0

    # If I end up using position_in_game to order listings, below will not be needed:
    add_column :realty_games, :ordered_listing_ids, :string, array: true, default: []
    # I need to be careful with below.  Will re-add if and when it
    # is clearer it won't impact the current store_attribute implementation:
    # add_column :realty_games, :realty_game_leaderboard, :jsonb, default: {}

    # Below to control visibility of a listing in a game (currently using visible
    # prop of a listing which is wrong)
    add_column :realty_game_listings, :visible_in_game, :boolean, default: true
    # listing.define_singleton_method(:listing_position_in_game)
    # an LLM added above - before position_in_game below was added
    add_column :realty_game_listings, :position_in_game, :integer, default: 0

    # currently if I hide a photo I hide it directly on the photo so will affect
    # any listing instance in any game using it.
    # Below will allow me to work around that:
    add_column :realty_game_listings, :ordered_photo_uuids, :text, array: true, default: []
    add_column :realty_game_listings, :visible_photo_uuids, :text, array: true, default: []

    add_column :realty_game_listings, :photo_in_game_comments, :jsonb, default: {}

    # not entirely sure about below:
    # add_column :game_sessions, :performance_summary, :jsonb, default: {}
  end
end
