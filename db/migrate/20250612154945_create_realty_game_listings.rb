class CreateRealtyGameListings < ActiveRecord::Migration[7.2]
  def change
    create_table :realty_game_listings do |t|
      t.uuid :uuid, default: -> { 'gen_random_uuid()' }, index: true
      t.uuid :realty_game_uuid, index: true # , :required: true
      t.uuid :listing_uuid, index: true # , :required: true
      t.uuid :realty_asset_uuid, index: false
      t.uuid :scoot_uuid, index: false
      t.boolean :is_sale_listing, default: true
      t.boolean :is_rental_listing, default: false
      t.integer :guessed_prices_count, default: 0 # , null: false
      t.integer :game_sessions_count, default: 0 # , null: false
      # t.integer :price_estimates_count, default: 0 # , null: false
      # might include last estimate calculations below
      # and perhaps relevant_sold_transactions too
      t.jsonb :realty_game_listing_details, default: {}
      t.integer :realty_game_listing_flags, default: 0, null: false, index: true
      t.jsonb :translations, default: {}
      t.datetime :discarded_at, index: true
      t.bigint :average_guess_cents
      t.bigint :highest_guess_cents
      t.bigint :lowest_guess_cents
      t.integer :total_guesses_count, default: 0
      t.datetime :statistics_updated_at
      t.timestamps
    end

    add_index :realty_game_listings, :statistics_updated_at
    add_index :realty_game_listings, :average_guess_cents
  end
end
