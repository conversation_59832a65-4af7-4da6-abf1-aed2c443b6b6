# class CreateScrapeItems < ActiveRecord::Migration[7.1]
# this is very much based on above
class CreateRealtyScrapedItems < ActiveRecord::Migration[7.2]
  def change
    create_table :realty_scraped_items do |t|
      # 03 july 2025: this is a new table to replace scrape_items
      # as scrape_items was doing too much.
      # Initially this will be used when I scrape a listing
      # locally and then post it to the server
      # (currently using RealtyPuntCreator to post to
      # realty_games_mgmt controller)
      t.uuid :uuid, default: -> { 'gen_random_uuid()' }, index: true

      t.string :scraped_content_column_name
      t.jsonb :extracted_image_urls
      t.jsonb :extracted_asset_data
      t.jsonb :extracted_listing_data

      # t.uuid :realty_search_query_uuid, index: true
      # t.boolean :is_realty_search_scrape, default: false
      # t.integer :summary_listings_count

      # t.string :import_url
      t.string :scrape_unique_url
      #  above will have #h2c or #hpg suffix
      t.string :scrapable_url
      #  scrapable_url: where other urls might redirect to
      #  scrape_cannonical_url: what is listed in page meta as canonical
      t.string :scrape_cannonical_url
      # t.string :url_domain
      t.string :scrape_uri_scheme
      t.string :scrape_uri_host
      # below calc from above 2
      #       scrape_base_url = "#{import_uri.scheme}://#{import_uri.host}"
      # for use when filling relative urls
      # t.string :scrape_base_url
      t.jsonb :further_scrapable_urls, default: {}
      t.jsonb :related_realty_scraped_items, default: {}

      #  Will add a relationship to an LLM_interaction
      #  might use the LLM to figure out further_scrapable_urls
      t.uuid :llm_interaction_uuid

      t.string :title
      t.string :description
      t.string :page_locale_code
      # Add requestor ip???
      t.string :user_locale_code
      t.boolean :has_screenshot, default: false
      t.uuid :page_screenshot_uuid
      # t.boolean :is_trustworthy, default: false
      t.boolean :is_paid_scrape, default: false
      t.integer :confidence_score, default: 50 # number up to 100 reflecting how well scraping went
      # t.integer :version # a count of the number of scraped_pages for this url

      # below should probably have been called is_successful_scrape
      # as that is what I use it for:
      t.boolean :is_valid_scrape, default: false
      t.string :scrape_failure_message
      t.boolean :is_active_listing, default: false
      t.string :listing_state

      t.integer :realty_scraped_item_flags, default: 0, null: false, index: true
      t.string :response_code
      t.text :body
      # t.text :request_object
      # t.text :response_object
      t.jsonb :request_object, default: {}
      t.jsonb :response_object, default: {}
      t.text :nokogiri_object

      t.string :scraper_connector_name
      # below replaced with above
      # t.string :scraping_source #To distinguise b/n scrapingbee, pasted html or regular net call
      t.string :web_scraper_name
      t.string :scraper_mapping_name
      # Scraper mappings might evolve over time:
      t.string :scraper_mapping_version
      t.jsonb :scraper_mapping_json, default: {}

      t.text :client_provided_html

      t.boolean :content_is_html, default: false
      t.boolean :content_is_xml, default: false
      t.boolean :content_is_json, default: false
      t.boolean :content_is_pdf, default: false
      t.boolean :content_is_binary, default: false
      # t.text :full_html_before_js
      # t.text :full_html_after_js
      # previously use full_html but as item could be json or pdf etc
      # I'll use full_content
      t.text :full_content_before_js
      t.text :full_content_after_js
      t.integer :full_content_before_js_length, default: 0
      t.integer :full_content_after_js_length, default: 0
      t.jsonb :selectors_and_values, default: {} # css/xpath etc selectors and their values
      t.jsonb :all_page_images, default: {}
      t.integer :all_page_images_length, default: 0
      t.jsonb :script_json, default: {}

      t.uuid :scraper_host_uuid
      t.uuid :scraped_page_uuid
      t.uuid :sale_listing_uuid
      t.uuid :rental_listing_uuid
      t.uuid :realty_asset_uuid
      # t.bigint :psq_visit_id
      # t.string :site_visitor_token
      # t.uuid :agency_uuid, index: true # , :required: true
      t.uuid :agency_tenant_uuid, index: true # , :required: true
      # t.uuid :guest_uuid # , index: true
      # t.uuid :user_uuid # , index: true #, :required: true

      t.string :currency
      t.monetize :price_sale_current
      t.monetize :price_rental_monthly_standard_season
      t.jsonb :extra_realty_scraped_item_details, default: {}

      t.jsonb :translations, default: {}
      # for discarded gem:
      t.datetime :discarded_at, index: true
      t.timestamps
    end
  end
end
