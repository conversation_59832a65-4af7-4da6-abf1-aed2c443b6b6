class CreatePriceEstimates < ActiveRecord::Migration[7.2]
  def change
    # Option 1: UUID as Primary Key
    # create_table :price_estimates, id: :uuid, default: -> { 'gen_random_uuid()' } do |t|
    # Option 2: Integer PK, separate UUID column (current approach refined)
    create_table :price_estimates do |t|
      t.uuid :uuid, default: -> { 'gen_random_uuid()' }, null: false, index: { unique: true }

      # Foreign Keys - consider t.references or explicit foreign_key options
      t.uuid    :agency_tenant_uuid, index: true # nullability depends on requirements
      t.uuid    :realty_dossier_uuid, index: true # nullability depends on requirements
      t.uuid    :listing_uuid, index: true # Add index, nullability depends on requirements

      # Consider a single 'listing_type' column if mutually exclusive
      # t.string  :listing_type, null: false, index: true # e.g., 'sale', 'rental'
      # OR keep booleans if they can coexist or be independent
      t.boolean :is_for_sale_listing, default: true, null: false
      t.boolean :is_for_rental_listing, default: false, null: false

      t.boolean :is_ai_estimate, default: false, null: false
      t.boolean :is_protected, default: false, null: false
      t.integer :count_sold_transactions_shown, default: 0, null: false

      t.uuid    :user_uuid, index: true # Add index, nullability depends
      t.uuid    :scoot_uuid, index: true # Consider renaming, add index, nullability depends
      t.uuid    :extra_uuid, index: false # Consider renaming, add index, nullability depends

      t.string  :game_session_id
      t.string  :estimator_name # , null: false # e.g., name of an LLM or human estimator
      t.string  :estimate_title # , null: false
      t.text    :estimate_text

      t.bigint  :estimated_price_cents, default: 0, null: false
      t.bigint  :price_at_time_of_estimate_cents, default: 0, null: false # Renamed slightly for consistency
      t.integer :percentage_above_or_below, default: 0, null: false # Or t.decimal for precision
      t.string :estimate_currency, default: 'GBP', null: false

      t.string :estimate_vicinity # , null: false
      t.string  :estimate_postal_code # , null: false
      t.float   :estimate_latitude_center
      t.float   :estimate_longitude_center

      t.jsonb   :estimate_details, default: {} # , null: false
      t.integer :estimate_flags, default: 0, null: false, index: true # For bitmask flags

      t.datetime :discarded_at, index: true
      t.timestamps #  null: false is default for created_at, updated_at
    end

    # Add foreign key constraints outside the block for clarity, or use inline options
    # Example: Assuming agency_tenants table has a primary key `id` of type UUID
    # add_foreign_key :price_estimates, :agency_tenants, column: :agency_tenant_uuid, primary_key: :id
    # If agency_tenants table's primary key is also named `uuid`:
    # add_foreign_key :price_estimates, :agency_tenants, column: :agency_tenant_uuid, primary_key: :uuid

    # Repeat for realty_dossier_uuid, listing_uuid, user_uuid etc.
    # e.g., add_foreign_key :price_estimates, :listings, column: :listing_uuid, primary_key: :id # (or :uuid)
    # e.g., add_foreign_key :price_estimates, :users, column: :user_uuid, primary_key: :id # (or :uuid)
  end
end
