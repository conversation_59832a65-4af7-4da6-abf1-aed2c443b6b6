class CreateParticipants < ActiveRecord::Migration[7.2]
  def change
    create_table :participants do |t|
      t.string :visitor_token, null: false
      t.datetime :first_visit_at
      t.datetime :last_visit_at
      t.integer :total_visits, default: 0
      t.integer :total_events, default: 0
      t.integer :total_page_views, default: 0
      t.decimal :average_session_duration, precision: 10, scale: 2
      t.integer :unique_pages_visited, default: 0
      t.boolean :returning_visitor, default: false
      t.text :first_referrer
      t.text :first_landing_page
      t.string :first_utm_source
      t.string :first_utm_medium
      t.string :first_utm_campaign
      t.string :first_country
      t.string :first_city
      t.string :first_device_type
      t.string :first_browser
      t.string :first_os
      t.jsonb :engagement_metrics, default: {}
      t.jsonb :behavior_patterns, default: {}

      t.timestamps
    end

    add_index :participants, :visitor_token, unique: true
    add_index :participants, :first_visit_at
    add_index :participants, :last_visit_at
    add_index :participants, :total_visits
    add_index :participants, :returning_visitor
    add_index :participants, :engagement_metrics, using: :gin
    add_index :participants, :behavior_patterns, using: :gin
  end
end
