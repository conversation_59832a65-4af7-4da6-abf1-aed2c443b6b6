class CreateGameSessions < ActiveRecord::Migration[7.2]
  def change
    create_table :game_sessions do |t|
      t.uuid :uuid, default: -> { 'gen_random_uuid()' }, null: false, index: { unique: true }

      # Foreign Keys - consider t.references or explicit foreign_key options
      t.uuid    :agency_tenant_uuid, index: true # nullability depends on requirements

      t.uuid    :main_scoot_uuid, index: true # Add index, nullability depends on requirements
      t.uuid    :main_realty_game_uuid, index: true # Add index, nullability depends on requirements

      t.integer :guessed_prices_count, default: 0 # , null: false
      # t.integer :price_estimates_count, default: 0 # , null: false

      t.boolean :is_protected, default: false, null: false
      # t.integer :count_sold_transactions_shown, default: 0, null: false

      t.string :session_guest_name # , null: false # e.g., name of an LLM or human estimator
      t.string :session_guest_title # , null: false

      t.jsonb :game_session_details, default: {} # , null: false
      t.integer :game_session_flags, default: 0, null: false, index: true # For bitmask flags

      t.datetime :discarded_at, index: true

      t.integer :total_score
      t.integer :max_possible_score
      t.decimal :performance_percentage, precision: 5, scale: 2
      t.string :performance_rating_text
      t.string :performance_rating_icon
      t.string :performance_rating_color
      t.datetime :results_calculated_at

      t.string :session_preferred_currency, default: 'GBP', null: false
      t.string :site_visitor_token, index: true

      t.timestamps
    end

    add_index :game_sessions, :results_calculated_at
    add_index :game_sessions, :total_score
  end
end
