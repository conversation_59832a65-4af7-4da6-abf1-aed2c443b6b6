class CreateGuessedPrices < ActiveRecord::Migration[7.2]
  def change
    create_table :guessed_prices do |t|
      t.uuid :uuid, default: -> { 'gen_random_uuid()' }, null: false, index: { unique: true }

      # Foreign Keys - consider t.references or explicit foreign_key options
      t.uuid    :agency_tenant_uuid, index: true # nullability depends on requirements
      t.uuid    :listing_uuid, index: false # Add index, nullability depends on requirements
      t.uuid    :realty_game_listing_uuid, index: true # Add index, nullability depends on requirements
      t.uuid    :realty_game_uuid, index: true # Add index, nullability depends on requirements

      # t.boolean :is_for_sale_listing, default: true, null: false
      # t.boolean :is_for_rental_listing, default: false, null: false

      t.boolean :is_ai_estimate, default: false, null: false
      t.boolean :is_protected, default: false, null: false
      # t.integer :count_sold_transactions_shown, default: 0, null: false

      t.uuid    :user_uuid, index: true # Add index, nullability depends
      # t.uuid    :scoot_uuid, index: true # Consider renaming, add index, nullability depends
      t.uuid    :extra_uuid, index: false # Consider renaming, add index, nullability depends

      t.uuid :game_session_uuid
      t.string  :game_session_id
      t.string  :game_session_string

      t.string  :estimator_name # , null: false # e.g., name of an LLM or human estimator
      t.string  :estimate_title # , null: false
      t.text    :notes_on_guess

      # t.bigint  :guessed_price_in_cents, default: 0, null: false
      # initially used above but was too confusing
      t.bigint  :guessed_price_amount_cents, default: 0, null: false
      t.bigint  :price_at_time_of_estimate_cents, default: 0, null: false # Renamed slightly for consistency
      t.bigint  :guessed_price_in_ui_currency_cents, default: 0, null: false

      t.integer :score_for_guess, default: 0, null: false # Or t.decimal for precision
      t.integer :percentage_above_or_below, default: 0, null: false # Or t.decimal for precision
      t.string :guessed_price_currency, default: 'GBP', null: false
      t.string :ui_currency, default: 'GBP', null: false
      t.string :source_currency, default: 'GBP', null: false

      # t.string :estimate_vicinity # , null: false
      # t.string  :estimate_postal_code # , null: false
      # t.float   :estimate_latitude_center
      # t.float   :estimate_longitude_center

      t.jsonb   :guessed_price_details, default: {} # , null: false
      t.integer :guessed_price_flags, default: 0, null: false, index: true # For bitmask flags

      t.datetime :discarded_at, index: true
      t.timestamps #  null: false is default for created_at, updated_at
    end
  end
end
