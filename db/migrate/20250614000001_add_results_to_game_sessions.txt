6 july 2025: these migrations were suggested by augment and got merged in a few weeks ago
class AddResultsToGameSessions < ActiveRecord::Migration[7.2]
  def change
    add_column :game_sessions, :total_score, :integer
    add_column :game_sessions, :max_possible_score, :integer
    add_column :game_sessions, :performance_percentage, :decimal, precision: 5, scale: 2
    add_column :game_sessions, :performance_rating_text, :string
    add_column :game_sessions, :performance_rating_icon, :string
    add_column :game_sessions, :performance_rating_color, :string
    add_column :game_sessions, :results_calculated_at, :datetime

    add_index :game_sessions, :results_calculated_at
    add_index :game_sessions, :total_score
  end
end
