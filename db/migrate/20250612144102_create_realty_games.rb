class CreateRealtyGames < ActiveRecord::Migration[7.2]
  def change
    create_table :realty_games do |t|
      # ...
      # TODO: - add access_code????

      t.uuid :uuid, default: -> { 'gen_random_uuid()' }, index: true
      t.uuid :agency_tenant_uuid, null: false # Acts as Tenant

      t.uuid :scoot_uuid, index: true
      t.string :realty_game_slug, index: true

      t.integer :guessed_prices_count, default: 0 # , null: false
      t.integer :game_sessions_count, default: 0 # , null: false
      # t.integer :price_estimates_count, default: 0 # , null: false
      t.integer :game_jots_count, default: 0 # , null: false
      t.integer :game_listings_count, default: 0 # , null: false

      t.datetime :game_start_at, index: false
      t.datetime :game_end_at, index: false

      t.integer :realty_game_flags, default: 0, null: false, index: true
      # might use below to decide if game is sale or rental etc..
      t.integer :game_type_flags, default: 0, null: false, index: true
      # might use below to decide if game is public, private, etc...
      t.integer :game_settings_flags, default: 0, null: false, index: true

      #       "should_show_out_links": true,
      # "guessed_price_validation": {
      # "max_percentage_above": 900,
      # "min_percentage_below": 90,
      # "messages": {
      # "too_high": "Guess is way way too high",
      # "too_low": "Guess is way too low",
      # "positive_number": "Please enter a positive number"
      # }
      # },
      # below for mobility:
      t.jsonb :translations, default: {}
      t.string :game_starting_url
      t.string :game_bg_image_url
      t.string :game_title
      t.string :game_description

      t.string :game_default_currency, default: 'GBP', null: false
      t.string :game_default_country
      t.string :game_default_locale

      # For aa_state_machine gem
      t.string :realty_game_aasm_state
      # and discarded gem:
      t.datetime :discarded_at, index: true

      # might include last estimate calculations below
      t.jsonb :realty_game_details, default: {}
      t.jsonb :game_area_details, default: {}
      t.jsonb :game_rules, default: {}
      # t.jsonb :game_checklists, default: {}
      # t.jsonb :game_significant_dates, default: {}
      # t.jsonb :game_related_urls, default: {}

      # currency_conversions??
      t.jsonb :game_notes, default: {}

      # TODO: - implement an enum with below
      t.integer :game_source_portal, default: 0, null: false

      # # t.monetize :service_charge_yearly
      # t.jsonb :params_for_similar_properties, default: {}

      # t.string :site_visitor_token
      t.uuid :game_primary_user_uuid # , index: true #, :required: true

      t.timestamps
    end
  end
end
