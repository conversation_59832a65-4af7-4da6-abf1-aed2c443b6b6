class CreateContextualRecords < ActiveRecord::Migration[7.2]
  def change
    create_table :contextual_records do |t|
      t.uuid :uuid, default: -> { 'gen_random_uuid()' }, index: true
      # t.bigint :notification_id
      # t.boolean :success, default: false

      t.string :aasm_state

      t.string :record_source_url
      t.string :contxt_postcode
      t.string :contxt_outcode
      t.uuid :latest_scrape_item_uuid

      t.integer :contxt_flags, default: 0, index: true, null: false
      t.integer :contxt_type, default: 0, index: true, null: false

      t.text :raw_contxt

      # below for mobility:
      t.jsonb :translations, default: {}
      t.jsonb :extra_contxt_details, default: {}

      t.uuid :agency_tenant_uuid, index: true # , :required: true
      t.datetime :discarded_at, index: true

      t.timestamps
    end
  end
end

# I don't have realty_dossier_uuid here because there is a join table via dossier_asset_uuid:
# dossier_asset_contextual_records
# Can get realty_dossier_uuid via dossier_asset_uuid
