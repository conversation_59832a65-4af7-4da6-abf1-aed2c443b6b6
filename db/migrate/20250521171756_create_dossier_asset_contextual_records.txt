class CreateDossierAssetContextualRecords < ActiveRecord::Migration[7.2]
  def change
    create_table :dossier_asset_contextual_records do |t|
      t.uuid :uuid, default: -> { 'gen_random_uuid()' }, index: true

      t.uuid :contextual_record_uuid
      t.uuid :dossier_asset_uuid

      t.uuid :agency_tenant_uuid, index: true # , :required: true
      t.datetime :discarded_at, index: true

      t.timestamps
    end
  end
end

# wonder if worth having realty_dossier_uuid above ???
