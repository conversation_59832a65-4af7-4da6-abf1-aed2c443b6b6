<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, initial-scale=1.0">
  <title>GeoJSON Polygon Map</title>
  <link rel="stylesheet"
        href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <style>
    #map {
      height: 600px;
      width: 100%;
    }
  </style>
</head>

<body>
  <div id="map"></div>
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script>
    // Initialize the map and set its view to the approximate center of the polygon
    var map = L.map('map').setView([36.611, -4.519], 15);

    // Add OpenStreetMap tiles as the base layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Your GeoJSON polygon data
    var geojsonData = {
      "type": "Polygon",
      "coordinates": [
        [
          [-4.6405, 36.5980],
          [-4.6380, 36.6000],
          [-4.6350, 36.5995],
          [-4.6330, 36.5975],
          [-4.6345, 36.5955],
          [-4.6375, 36.5945],
          [-4.6400, 36.5950],
          [-4.6405, 36.5980]
        ]
      ]
    };

    // Add the GeoJSON polygon to the map
    L.geoJSON(geojsonData, {
      style: {
        color: '#3388ff', // Border color
        weight: 2,        // Border thickness
        fillColor: '#3388ff', // Fill color
        fillOpacity: 0.5  // Fill opacity
      }
    }).addTo(map);

    // Fit the map view to the bounds of the polygon
    var polygonLayer = L.geoJSON(geojsonData).addTo(map);
    map.fitBounds(polygonLayer.getBounds());
  </script>
</body>

</html>