<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeoJSON Polygon Map</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #map { height: 600px; width: 100%; }
    </style>
</head>
<body>
    <div id="map"></div>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize the map and set its view to the approximate center of the polygon
        var map = L.map('map').setView([36.611, -4.519], 15);

        // Add OpenStreetMap tiles as the base layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Your GeoJSON polygon data
        var geojsonData = {
            "type": "Polygon",
            "coordinates": [
                [
                    [-4.5174048, 36.6093241],
                    [-4.5174202, 36.6092932],
                    [-4.5174231, 36.609273],
                    [-4.5174158, 36.6092365],
                    [-4.5173606, 36.6091773],
                    [-4.5172411, 36.6090783],
                    [-4.5169418, 36.6088848],
                    [-4.516777, 36.6088284],
                    [-4.5164337, 36.6088103],
                    [-4.516275, 36.6088816],
                    [-4.5161158, 36.6090158],
                    [-4.515851, 36.6092571],
                    [-4.5156851, 36.6094182],
                    [-4.5156146, 36.6095218],
                    [-4.5155883, 36.609647],
                    [-4.5155877, 36.6098055],
                    [-4.5156288, 36.6099281],
                    [-4.5157034, 36.6100402],
                    [-4.5157553, 36.6100812],
                    [-4.5158655, 36.6101468],
                    [-4.515964, 36.6101849],
                    [-4.5160253, 36.610199],
                    [-4.5162031, 36.6102117],
                    [-4.516381, 36.6101937],
                    [-4.516649, 36.6101224],
                    [-4.5167623, 36.6100789],
                    [-4.5168269, 36.6100494],
                    [-4.5171662, 36.6098595],
                    [-4.5172639, 36.6098443],
                    [-4.5175466, 36.6099265],
                    [-4.517653, 36.6099945],
                    [-4.5177097, 36.6100874],
                    [-4.5177681, 36.6102561],
                    [-4.5178297, 36.6104411],
                    [-4.5178484, 36.6105049],
                    [-4.5178862, 36.6106019],
                    [-4.517915, 36.6106597],
                    [-4.5180557, 36.6108257],
                    [-4.5181711, 36.6109407],
                    [-4.5184052, 36.6111606],
                    [-4.5185563, 36.6112616],
                    [-4.5187074, 36.6113535],
                    [-4.5190158, 36.6115195],
                    [-4.5192857, 36.6116457],
                    [-4.5194542, 36.6117196],
                    [-4.5195782, 36.6117891],
                    [-4.519829, 36.6119426],
                    [-4.5201316, 36.6121388],
                    [-4.5202418, 36.612215],
                    [-4.5203607, 36.6122934],
                    [-4.5206421, 36.6124766],
                    [-4.5209176, 36.612666],
                    [-4.5211885, 36.6128716],
                    [-4.5214163, 36.6130632],
                    [-4.5216441, 36.6132547],
                    [-4.5218328, 36.6134023],
                    [-4.5220405, 36.6135614],
                    [-4.5222529, 36.6137183],
                    [-4.5224582, 36.6138687],
                    [-4.5226634, 36.6140191],
                    [-4.5228587, 36.6141586],
                    [-4.5230059, 36.6139782],
                    [-4.5223476, 36.6145111],
                    [-4.5216615, 36.6141416],
                    [-4.5212412, 36.6136544],
                    [-4.5208841, 36.6130213],
                    [-4.5202493, 36.6124101],
                    [-4.5195632, 36.6116776],
                    [-4.5188858, 36.6107227],
                    [-4.5184055, 36.6100417],
                    [-4.5179621, 36.6096591],
                    [-4.5175065, 36.6093945],
                    [-4.5174048, 36.6093241]
                ]
            ]
        };

        // Add the GeoJSON polygon to the map
        L.geoJSON(geojsonData, {
            style: {
                color: '#3388ff', // Border color
                weight: 2,        // Border thickness
                fillColor: '#3388ff', // Fill color
                fillOpacity: 0.5  // Fill opacity
            }
        }).addTo(map);

        // Fit the map view to the bounds of the polygon
        var polygonLayer = L.geoJSON(geojsonData).addTo(map);
        map.fitBounds(polygonLayer.getBounds());
    </script>
</body>
</html>