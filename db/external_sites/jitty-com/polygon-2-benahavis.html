<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, initial-scale=1.0">
  <title>GeoJSON Polygon Map</title>
  <link rel="stylesheet"
        href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <style>
    #map {
      height: 600px;
      width: 100%;
    }
  </style>
</head>

<body>
  <div id="map"></div>
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script>
    // Initialize the map and set its view to the approximate center of the polygon
    var map = L.map('map').setView([36.611, -4.519], 15);

    // Add OpenStreetMap tiles as the base layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Your GeoJSON polygon data
    var geojsonData = {
      "type": "Polygon",
      "coordinates": [
        [[-5.038433, 36.536812],
        [-5.03603, 36.539709],
        [-5.033283, 36.541847],
        [-5.031481, 36.543916],
        [-5.027876, 36.54557],
        [-5.027618, 36.548811],
        [-5.024357, 36.553017],
        [-5.022641, 36.550155],
        [-5.020409, 36.549363],
        [-5.020967, 36.546708],
        [-5.019722, 36.54557],
        [-5.016117, 36.544398],
        [-5.013799, 36.543157],
        [-5.012169, 36.542053],
        [-5.009766, 36.541226],
        [-5.007534, 36.540674],
        [-5.003071, 36.539847],
        [-5.001698, 36.537778],
        [-5.000153, 36.535157],
        [-4.998264, 36.531433],
        [-4.998951, 36.528536],
        [-4.999638, 36.526743],
        [-4.994831, 36.52426],
        [-4.993114, 36.521225],
        [-4.991913, 36.517224],
        [-4.990711, 36.515672],
        [-4.990261, 36.514396],
        [-4.988479, 36.51419],
        [-4.991999, 36.510154],
        [-4.992964, 36.508429],
        [-4.994273, 36.506325],
        [-4.995646, 36.504583],
        [-4.997663, 36.50291],
        [-5.000281, 36.501392],
        [-5.003242, 36.500391],
        [-5.005989, 36.499805],
        [-5.008564, 36.499563],
        [-5.013199, 36.499011],
        [-5.015774, 36.497493],
        [-5.018349, 36.496528],
        [-5.019894, 36.49432],
        [-5.021095, 36.491973],
        [-5.022469, 36.49087],
        [-5.023499, 36.489352],
        [-5.026417, 36.487282],
        [-5.028906, 36.484728],
        [-5.032511, 36.481968],
        [-5.036029, 36.479138],
        [-5.038948, 36.476998],
        [-5.041609, 36.47548],
        [-5.043583, 36.474376],
        [-5.046243, 36.472719],
        [-5.048561, 36.471684],
        [-5.050879, 36.470994],
        [-5.052767, 36.471546],
        [-5.05281, 36.472995],
        [-5.054226, 36.473478],
        [-5.055169, 36.473478],
        [-5.054998, 36.472029],
        [-5.056972, 36.472926],
        [-5.060578, 36.473616],
        [-5.064697, 36.474306],
        [-5.068818, 36.475134],
        [-5.071563, 36.477758],
        [-5.075512, 36.484935],
        [-5.078258, 36.489386],
        [-5.081863, 36.493698],
        [-5.08255, 36.495397],
        [-5.08461, 36.497096],
        [-5.085124, 36.500011],
        [-5.086669, 36.501012],
        [-5.084953, 36.502599],
        [-5.082807, 36.504117],
        [-5.082893, 36.503841],
        [-5.079803, 36.509912],
        [-5.077744, 36.516259],
        [-5.07534, 36.518328],
        [-5.073109, 36.519708],
        [-5.070019, 36.51819],
        [-5.065727, 36.520949],
        [-5.063152, 36.522467],
        [-5.062294, 36.52288],
        [-5.060921, 36.523019],
        [-5.056457, 36.525916],
        [-5.040836, 36.534743],
        [-5.040665, 36.534882],
        [-5.042638, 36.533571],
        [-5.038433, 36.536812]
      ]
      ]
    };

    // Add the GeoJSON polygon to the map
    L.geoJSON(geojsonData, {
      style: {
        color: '#3388ff', // Border color
        weight: 2,        // Border thickness
        fillColor: '#3388ff', // Fill color
        fillOpacity: 0.5  // Fill opacity
      }
    }).addTo(map);

    // Fit the map view to the bounds of the polygon
    var polygonLayer = L.geoJSON(geojsonData).addTo(map);
    map.fitBounds(polygonLayer.getBounds());
  </script>
</body>

</html>