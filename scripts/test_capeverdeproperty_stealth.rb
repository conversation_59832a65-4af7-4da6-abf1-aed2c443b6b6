#!/usr/bin/env ruby

# Simple test script for capeverdeproperty scraping with stealth mode
# Usage: ruby scripts/test_capeverdeproperty_stealth.rb

require_relative '../config/environment'

def test_capeverdeproperty_stealth
  puts "=== Testing CapeVerdeProperty with <PERSON>han<PERSON> LocalPlaywright (Stealth Mode) ==="
  
  # The URL from the error log
  test_url = 'https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms'
  
  begin
    # Create a scraped item for testing
    scraped_item = RealtyScrapedItem.new(
      scrapable_url: test_url,
      scraper_connector_name: 'ScraperConnectors::LocalPlaywright',
      scraped_content_column_name: 'full_content_before_js'
    )
    
    puts "Created test scraped item for: #{test_url}"
    
    # Test with enhanced LocalPlaywright using stealth mode
    puts "\n--- Testing with Enhanced LocalPlaywright (Stealth Mode) ---"
    
    content = scraped_item.retrieve_and_set_rsi_content(
      'ScraperConnectors::LocalPlaywright',
      include_trailing_slash: false,
      force_retrieval: true,
      stealth_mode: true
    )
    
    if content&.length && content.length > 1000
      puts "✅ SUCCESS: Retrieved #{content.length} characters"
      puts "Content preview: #{content[0..200]}..."
      
      # Check for Cloudflare indicators
      if content.include?('Just a moment') || content.include?('Checking your browser')
        puts "⚠️  WARNING: Cloudflare challenge detected in content"
      else
        puts "✅ No Cloudflare challenge detected"
      end
      
      # Check for expected content
      if content.include?('capeverdeproperty') || content.include?('property')
        puts "✅ Expected content keywords found"
      else
        puts "⚠️  WARNING: Expected content keywords not found"
      end
    else
      puts "❌ FAILED: Content too short or empty (#{content&.length} chars)"
    end
    
  rescue StandardError => e
    puts "❌ ERROR: #{e.class} - #{e.message}"
    puts e.backtrace.first(3).join("\n")
  end
  
  puts "\n=== Test Complete ==="
end

def test_regular_vs_stealth
  puts "=== Comparing Regular vs Stealth Mode ==="
  
  test_url = 'https://www.capeverdeproperty.co.uk'
  
  ['Regular Mode', 'Stealth Mode'].each_with_index do |mode, index|
    stealth_enabled = index == 1
    
    puts "\n--- Testing #{mode} ---"
    
    begin
      connector = ScraperConnectors::LocalPlaywright.new(headless: true, stealth_mode: stealth_enabled)
      puts "✅ #{mode} connector initialized"
      
      result = connector.retrieve_data_from_connector(test_url)
      
      if result[:error]
        puts "❌ #{mode} failed: #{result[:error]}"
      else
        content_length = result[:returned_content]&.length || 0
        puts "✅ #{mode} success: #{content_length} characters"
        
        if result[:returned_content]&.include?('cloudflare') || result[:returned_content]&.include?('Just a moment')
          puts "⚠️  #{mode}: Cloudflare challenge detected"
        else
          puts "✅ #{mode}: No Cloudflare challenge"
        end
      end
      
      connector.cleanup
      
    rescue StandardError => e
      puts "❌ #{mode} error: #{e.class} - #{e.message}"
    end
  end
end

# Run the tests
if __FILE__ == $PROGRAM_NAME
  puts "Starting CapeVerdeProperty Cloudflare tests..."
  puts "Date: #{Time.now}"
  puts
  
  test_capeverdeproperty_stealth
  puts "\n" + "="*60 + "\n"
  test_regular_vs_stealth
end
