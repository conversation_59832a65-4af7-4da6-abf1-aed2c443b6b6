
#!/usr/bin/env ruby

# Demo script showing how to use the enhanced Cloudflare handling capabilities
# Run this script to see both approaches in action

require_relative '../app/services/scraper_connectors/local_playwright'
require_relative '../app/services/scraper_connectors/cloudflare_playwright'

puts "=== Cloudflare Challenge Handling Demo ==="
puts

# Test URLs - replace these with actual Cloudflare-protected sites for real testing
test_urls = [
  'https://example.com',
  'https://httpbin.org/user-agent'  # This will show us the user agent being used
]

def test_approach(connector_class, name, test_urls, **options)
  puts "--- Testing #{name} ---"
  connector = nil
  
  begin
    connector = connector_class.new(**options)
    puts "✓ #{name} initialized successfully"
    
    test_urls.each_with_index do |url, index|
      puts "\n  Test #{index + 1}: #{url}"
      
      begin
        result = connector.retrieve_data_from_connector(url)
        
        if result[:error]
          puts "  ✗ Error: #{result[:error]}"
        else
          content_length = result[:returned_content]&.length || 0
          puts "  ✓ Success! Content length: #{content_length} characters"
          
          # Show user agent if this is the httpbin test
          if url.include?('httpbin.org/user-agent') && result[:returned_content]
            puts "  📝 Response preview: #{result[:returned_content][0..200]}..."
          end
        end
      rescue StandardError => e
        puts "  ✗ Exception: #{e.class} - #{e.message}"
      end
    end
    
  rescue StandardError => e
    puts "✗ Failed to initialize #{name}: #{e.class} - #{e.message}"
  ensure
    if connector
      connector.cleanup
      puts "✓ #{name} cleaned up"
    end
  end
  
  puts
end

# Test 1: Original LocalPlaywright (no stealth)
test_approach(
  ScraperConnectors::LocalPlaywright,
  "LocalPlaywright (Original)",
  test_urls,
  headless: true,
  stealth_mode: false
)

# Test 2: Enhanced LocalPlaywright with stealth mode
test_approach(
  ScraperConnectors::LocalPlaywright,
  "LocalPlaywright (Enhanced with Stealth)",
  test_urls,
  headless: true,
  stealth_mode: true
)

# Test 3: Dedicated CloudflarePlaywright
test_approach(
  ScraperConnectors::CloudflarePlaywright,
  "CloudflarePlaywright (Dedicated)",
  test_urls,
  headless: true
)

puts "=== Demo Complete ==="
puts
puts "Summary:"
puts "1. LocalPlaywright (Original) - Basic browser automation"
puts "2. LocalPlaywright (Enhanced) - Adds stealth mode for basic Cloudflare bypass"
puts "3. CloudflarePlaywright - Specialized class for advanced Cloudflare protection"
puts
puts "Choose the approach that best fits your needs:"
puts "- Use Enhanced LocalPlaywright for backward compatibility with stealth features"
puts "- Use CloudflarePlaywright for sites with advanced protection"
puts
puts "For visual debugging, set headless: false in the options above."
