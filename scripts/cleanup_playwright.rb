#!/usr/bin/env ruby

# Utility script to clean up Playwright user data directories and resolve lock issues
# Usage: ruby scripts/cleanup_playwright.rb

require_relative '../app/services/scraper_connectors/local_playwright'

puts "=== Playwright Cleanup Utility ==="

# Force clean all user data directories
ScraperConnectors::LocalPlaywright.force_cleanup_all_user_data

# Also kill any stray Chrome/Chromium processes that might be running
puts "\n=== Checking for stray browser processes ==="

begin
  # Check for Chrome/Chromium processes
  chrome_processes = `ps aux | grep -i chromium | grep -v grep`.strip
  if chrome_processes.empty?
    puts "[INFO] No Chromium processes found"
  else
    puts "[INFO] Found Chromium processes:"
    puts chrome_processes
    
    # Extract PIDs and kill them
    pids = chrome_processes.split("\n").map do |line|
      line.split[1] # PID is the second column
    end.compact
    
    pids.each do |pid|
      begin
        puts "[INFO] Killing process #{pid}"
        Process.kill('TERM', pid.to_i)
        sleep(1)
        # If still running, force kill
        Process.kill('KILL', pid.to_i)
      rescue Errno::<PERSON><PERSON><PERSON>
        puts "[INFO] Process #{pid} already terminated"
      rescue StandardError => e
        puts "[WARN] Could not kill process #{pid}: #{e.message}"
      end
    end
  end
rescue StandardError => e
  puts "[WARN] Error checking for browser processes: #{e.message}"
end

puts "\n=== Cleanup completed ==="
puts "You can now safely run your Playwright-based tasks again."
