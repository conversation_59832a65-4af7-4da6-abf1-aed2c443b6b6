# Playwright Local Service - Improvements

## Changes Made

### 1. Unique User Data Directories
- **Problem**: The original implementation used a single `playwright_user_data` directory, which could get locked if a previous browser instance didn't shut down properly.
- **Solution**: Each `LocalPlaywright` instance now creates a unique user data directory using timestamp and random ID (e.g., `playwright_user_data/session_20250721_143022_a1b2c3d4`).

### 2. Automatic Cleanup
- **Old sessions cleanup**: Automatically removes old session directories, keeping only the 5 most recent ones to prevent disk bloat.
- **Cleanup on exit**: User data directory is automatically cleaned up when the instance is destroyed.
- **Cleanup on error**: If browser setup fails, the user data directory is cleaned up to prevent accumulation of failed attempts.

### 3. Lock File Handling
- **Lock file removal**: Before starting a browser, the service attempts to remove any stale lock files that might prevent startup.
- **Retry logic**: If a SingletonLock error occurs, the service will retry up to 3 times with a fresh user data directory.

### 4. Force Cleanup Utility
- **Class method**: `ScraperConnectors::LocalPlaywright.force_cleanup_all_user_data` can be called to forcefully clean all user data directories.
- **Cleanup script**: `scripts/cleanup_playwright.rb` provides a standalone utility to clean up directories and kill stray browser processes.

## Usage

### Normal Usage
No changes needed - the improvements are automatic:

```ruby
connector = ScraperConnectors::LocalPlaywright.new
# ... use connector
connector.cleanup  # This now includes user data cleanup
```

### Manual Cleanup (if needed)
If you encounter lock issues, you can manually clean up:

```ruby
# From Ruby code
ScraperConnectors::LocalPlaywright.force_cleanup_all_user_data

# Or from command line
ruby scripts/cleanup_playwright.rb
```

### Running Your Rake Task
Your rake task should now work without the SingletonLock error:

```bash
rake h2c:start_realty_game_from_url_generic_v3
```

## Benefits

1. **No more SingletonLock errors**: Each instance uses its own user data directory
2. **Automatic cleanup**: No manual intervention needed for cleanup
3. **Disk space management**: Old sessions are automatically removed
4. **Recovery from errors**: Retry logic helps recover from transient issues
5. **Emergency cleanup**: Utility script available for manual cleanup if needed

## Directory Structure
```
playwright_user_data/
├── session_20250721_143022_a1b2c3d4/  # Current session
├── session_20250721_142015_e5f6g7h8/  # Previous session
└── ...  # (keeps only 5 most recent)
```

## Troubleshooting

If you still encounter issues:

1. Run the cleanup script: `ruby scripts/cleanup_playwright.rb`
2. Check for Chrome processes: `ps aux | grep -i chrome`
3. Manually remove the entire directory: `rm -rf playwright_user_data`
4. Check system resources (disk space, memory)
