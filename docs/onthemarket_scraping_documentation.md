# OnTheMarket Scraping Logic and Process Documentation

## Overview

The OnTheMarket scraping system is designed to extract property listing data from OnTheMarket.com using a multi-layered architecture that combines HTML parsing, JSON extraction, and robust error handling.

## Architecture Components

### 1. Models

#### ScrapeItemFromOtm (`app/models/scrape_item_from_otm.rb`)
- **Purpose**: Specialized model for OnTheMarket property page scraping
- **Inheritance**: Extends `ScrapeItem` base class
- **Key Features**:
  - Default scope filters for OnTheMarket-specific items
  - Custom property hash extraction from HTML/JSON
  - Data mapping to standardized asset and listing schemas

#### ScrapeItemFromOtmSearch (`app/models/scrape_item_from_otm_search.rb`)
- **Purpose**: Handles OnTheMarket search results pages
- **Key Features**:
  - Processes JSON data from search result pages
  - Extracts summary listing information
  - Maps search results to property summary format

### 2. Services

#### ScraperConnectors::Regular (`app/services/scraper_connectors/regular.rb`)
- **Purpose**: HTTP client for fetching HTML content from OnTheMarket
- **Key Features**:
  - Configurable HTTP headers and user agents
  - Error handling for redirects, HTTP errors, and network issues
  - Content validation and length checks
  - Metadata extraction (title, description, images)

#### RealtyParsers::ParseOnthemarketListingsJson (`app/services/realty_parsers/parse_onthemarket_listings_json.rb`)
- **Purpose**: Parses OnTheMarket JSON data embedded in HTML pages
- **Key Features**:
  - Extracts `__NEXT_DATA__` JSON from HTML script tags
  - Handles both old and new OnTheMarket data structures
  - Maps property data to standardized schemas

### 3. Shared Helpers

#### RealtyScrapers::SharedScraperHelpers
- **Purpose**: Common functionality across all scrapers
- **Key Features**:
  - URL validation and canonicalization
  - Image extraction from HTML/CSS selectors
  - Text cleaning and normalization utilities
  - Content mapping and transformation

## Scraping Process Flow

### 1. URL Processing
```ruby
# Example: Creating a scrape item for OnTheMarket
scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(url)
```

**Steps:**
1. Parse and validate the input URL
2. Generate unique scrape identifier
3. Set OnTheMarket-specific flags
4. Initialize scraper connector

### 2. Content Retrieval
```ruby
# Via ScraperConnectors::Regular
content = scraper_connector.retrieve_data_from_connector(uri)
```

**Process:**
1. Configure HTTP request headers
2. Handle OnTheMarket-specific requirements (trailing slash, user agent)
3. Make HTTP request with error handling
4. Validate content length and structure
5. Extract metadata and save to database

### 3. Data Extraction
```ruby
# Extract property data from HTML/JSON
property_hash = scrape_item.property_hash_from_scrape_item
```

**Key Steps:**
1. Parse HTML using Nokogiri
2. Extract `__NEXT_DATA__` JSON script tag
3. Handle multiple data structure versions:
   - New: `props.initialReduxState.property`
   - Legacy: `props.pageProps.property`
4. Extract additional data from `dataLayer` script
5. Parse images from multiple sources (JSON data, CSS selectors)

### 4. Data Mapping
The extracted data is mapped to two standardized schemas:

#### Asset Schema Mapping
- Property characteristics (bedrooms, bathrooms, area)
- Location data (coordinates, address components)
- Property features and categories
- Media counts and references

#### Listing Schema Mapping  
- Pricing information (current, original, currency)
- Listing details (description, features, tags)
- Publication and visibility settings
- Media and video content

## Data Structure Evolution

OnTheMarket has updated its data structure over time. The scraper handles both:

### Legacy Structure (Pre-2025)
```json
{
  "props": {
    "pageProps": {
      "property": { ... }
    }
  }
}
```

### Current Structure (2025+)
```json
{
  "props": {
    "initialReduxState": {
      "property": { ... }
    }
  }
}
```

## Key Features

### 1. Robust Error Handling
- HTTP error detection and recovery
- Content validation (minimum length checks)
- JSON parsing error handling
- Graceful degradation with fallback selectors

### 2. Multiple Data Sources
- Primary: JSON data from `__NEXT_DATA__`
- Secondary: HTML CSS selectors
- Supplementary: `dataLayer` analytics data

### 3. Image Extraction
- Priority order: JSON image arrays → CSS selectors → fallback patterns
- Support for various image formats and sizes
- Deduplication of image URLs

### 4. Content Validation
- Minimum content length validation (> 1000 characters)
- HTML structure validation
- JSON parsing validation
- Property data completeness checks

## Configuration

### Portal Configuration
```ruby
# In RealtyScrapedItem.portal_config
'onthemarket' => {
  connector: 'ScraperConnectors::Regular',
  method: :find_or_create_for_h2c_onthemarket,
  include_trailing_slash: true,
  scraped_content_column_name: 'full_content_before_js'
}
```

### HTTP Headers
- User-Agent: Modern browser simulation
- Accept: HTML/XHTML content types
- Cache-Control: No-cache for fresh content
- Accept-Language: English preference

## Database Schema

### ScrapeItem Fields (OnTheMarket-specific)
- `scrape_is_onthemarket`: Boolean flag
- `scrapable_url`: Original property URL
- `scrape_unique_url`: Canonicalized unique identifier
- `full_content_before_js`: Raw HTML content
- `scraper_connector_name`: Always 'ScraperConnectors::Regular'
- `all_page_images`: Extracted image URLs array
- `is_valid_scrape`: Success/failure indicator

## Testing Strategy

The scraping system should be tested at multiple levels:

### Unit Tests
- Individual method functionality
- Data mapping accuracy  
- Error handling scenarios
- Edge case validation

### Integration Tests
- End-to-end scraping workflow
- Database persistence
- Content validation
- Schema mapping verification

### Mock/VCR Tests
- HTTP interaction recording
- Consistent test data
- Performance testing
- Failure scenario simulation

## Monitoring and Maintenance

### Key Metrics to Monitor
- Scrape success rates
- Content validation failures
- JSON structure changes
- HTTP error patterns
- Image extraction success rates

### Maintenance Tasks
- User agent updates
- Data structure adaptation
- Error pattern analysis
- Performance optimization

## Example Usage

```ruby
# Basic scraping
url = "https://www.onthemarket.com/details/12345678/"
scrape_item = ScrapeItemFromOtm.find_or_create_for_h2c_onthemarket(url)
scrape_item.retrieve_and_set_content_object('ScraperConnectors::Regular')

# Extract property data
property_data = scrape_item.property_hash_from_scrape_item
asset_data = property_data[:asset_data]
listing_data = property_data[:listing_data]
images = property_data[:listing_image_urls]
```

This documentation provides a comprehensive overview of the OnTheMarket scraping logic and process, enabling developers to understand, maintain, and extend the system effectively.
