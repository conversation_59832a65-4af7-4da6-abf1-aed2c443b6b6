# GameSession Integration with PriceEstimate

## Overview

This document describes the integration between `GameSession` and `PriceEstimate` models, ensuring that proper GameSession instances are created and used throughout the price estimation workflow.

## Changes Made

### 1. Enhanced PriceEstimate Model

**File:** `app/models/price_estimate.rb`

- Added `game_session` method that intelligently finds the associated GameSession
- Supports both direct UUID lookup and backward compatibility with `estimate_details`
- Maintains existing `game_session_id` string field for compatibility

```ruby
def game_session
  return nil if game_session_id.blank?
  
  # First try to find by UUID (proper GameSession)
  session = GameSession.find_by(uuid: game_session_id)
  return session if session
  
  # Fallback: try to find by game_session_id stored in estimate_details
  session_id_from_details = estimate_details&.dig('game_session_id')
  return nil if session_id_from_details.blank?
  
  GameSession.find_by(uuid: session_id_from_details)
end
```

### 2. Enhanced GameSession Model

**File:** `app/models/game_session.rb`

- Added `price_estimates` method that finds estimates linked to this session
- Added helper methods for session management
- Supports both direct and estimate_details-based relationships

```ruby
def price_estimates
  # Find estimates where game_session_id matches this session's UUID
  direct_estimates = PriceEstimate.where(game_session_id: uuid)
  
  # Also find estimates where estimate_details contains this session's UUID
  details_estimates = PriceEstimate.where("estimate_details->>'game_session_id' = ?", uuid)
  
  # Combine and return unique estimates
  PriceEstimate.where(id: (direct_estimates.pluck(:id) + details_estimates.pluck(:id)).uniq)
end
```

### 3. Updated Controllers

**Files:** 
- `app/controllers/api_public/v4/realty_price_games_controller.rb`
- `app/controllers/api_public/v4/price_guesses_controller.rb`

#### Enhanced `add_price_estimate` / `create` methods:

- Automatically create or find GameSession instances
- Set both `game_session_id` field and `estimate_details` for compatibility
- Return GameSession information in API responses

#### New `find_or_create_game_session` method:

```ruby
def find_or_create_game_session
  session_id = params[:game_session_id] || params.dig(:price_estimate, :game_session_id)
  guest_name = params[:session_guest_name] || params.dig(:price_estimate, :session_guest_name) || 'Anonymous Player'
  guest_title = params[:session_guest_title] || params.dig(:price_estimate, :session_guest_title)
  
  # If session_id is provided, try to find existing session
  if session_id.present?
    existing_session = GameSession.find_by(uuid: session_id)
    return existing_session if existing_session
  end
  
  # Create new GameSession with metadata
  GameSession.create!(
    uuid: session_id.presence || SecureRandom.uuid,
    agency_tenant_uuid: @scoot.agency_tenant_uuid,
    main_scoot_uuid: @scoot.uuid,
    session_guest_name: guest_name,
    session_guest_title: guest_title,
    game_session_details: {
      created_via: 'price_estimate_api',
      user_agent: request.user_agent,
      ip_address: request.remote_ip,
      created_at: Time.current
    }
  )
end
```

### 4. Enhanced GameSessionResultsCalculator

**File:** `app/services/realty_punts/game_session_results_calculator.rb`

- Now uses proper GameSession instances instead of just string IDs
- Improved error handling and data structure
- Added GameSession information to results

#### Key improvements:

```ruby
def initialize(game_session_id)
  @game_session_id = game_session_id
  @game_session = find_game_session
  @player_estimates = load_player_estimates
end

def call
  return Result.new(success?: false, error_message: 'Game session not found.') unless @game_session
  return Result.new(success?: false, error_message: 'Game session has no estimates.') if @player_estimates.empty?

  # ... calculation logic ...
  
  Result.new(
    success?: true,
    data: {
      player_results: player_results,
      comparison_summary: comparison_summary,
      game_session: @game_session.as_json(
        only: %w[uuid session_guest_name session_guest_title created_at],
        methods: %w[total_estimates_count]
      )
    }
  )
end
```

## API Changes

### Request Parameters

The API now accepts additional parameters for GameSession creation:

```json
{
  "price_estimate": {
    "estimated_price_cents": 50000000,
    "estimate_currency": "GBP",
    "estimate_title": "Property Valuation",
    "game_session_id": "optional-uuid",
    "session_guest_name": "Player Name",
    "session_guest_title": "Property Expert"
  }
}
```

### Response Format

API responses now include GameSession information:

```json
{
  "price_estimate": {
    "uuid": "estimate-uuid",
    "game_session_id": "session-uuid",
    "estimated_price_cents": 50000000,
    // ... other estimate fields
  },
  "game_session": {
    "uuid": "session-uuid",
    "session_guest_name": "Player Name",
    "session_guest_title": "Property Expert",
    "created_at": "2025-01-15T10:30:00Z"
  },
  "message": "Price estimate created successfully"
}
```

## Backward Compatibility

The implementation maintains full backward compatibility:

1. **Existing `game_session_id` string field** continues to work
2. **`estimate_details` JSON field** is still populated for legacy support
3. **Old API calls** without GameSession parameters still work
4. **Existing data** can be accessed through both old and new methods

## Testing

A comprehensive test suite is available:

```bash
# Test the GameSession integration
bundle exec rake test:game_session_integration
```

This test validates:
- GameSession creation and relationships
- PriceEstimate linking to GameSession
- GameSessionResultsCalculator functionality
- Backward compatibility with old data
- API parameter handling

## Benefits

1. **Proper Data Modeling**: Uses actual model relationships instead of string references
2. **Enhanced Tracking**: Better session management and player tracking
3. **Improved Analytics**: Richer data for game session analysis
4. **Maintainability**: Cleaner code with proper ActiveRecord relationships
5. **Extensibility**: Easy to add new GameSession features and relationships

## Migration Path

No database migration is required as this uses existing fields and adds new functionality on top of the current schema. The integration is designed to work seamlessly with existing data while providing enhanced functionality for new sessions.
