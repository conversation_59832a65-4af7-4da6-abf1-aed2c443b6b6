# Cloudflare Challenge Handling for ScraperConnectors

This document explains how to handle Cloudflare challenges using the enhanced scraper connectors in the PWB Pro system.

## Overview

Two approaches are now available for handling Cloudflare-protected websites:

1. **Enhanced LocalPlaywright with Stealth Mode** - Adds basic stealth capabilities to the existing LocalPlaywright class
2. **CloudflarePlaywright** - A dedicated class specifically designed for bypassing Cloudflare protection

## Option 1: Enhanced LocalPlaywright with Stealth Mode

The existing `ScraperConnectors::LocalPlaywright` class now supports an optional stealth mode that can help bypass basic Cloudflare challenges.

### Usage

```ruby
# Initialize with stealth mode enabled
connector = ScraperConnectors::LocalPlaywright.new(
  headless: false,    # Set to false to see browser behavior
  stealth_mode: true  # Enable stealth features
)

# Use normally
data = connector.retrieve_data_from_connector('https://cloudflare-protected-site.com')
connector.cleanup
```

### Features Added

- Enhanced browser arguments for stealth mode
- Basic webdriver property removal
- Chrome runtime mocking
- Plugin spoofing
- Basic Cloudflare challenge detection
- Automatic wait for challenges to resolve

### When to Use

- Sites with basic Cloudflare protection
- When you need backward compatibility with existing code
- For simple challenge types

## Option 2: CloudflarePlaywright (Dedicated Class)

The new `ScraperConnectors::CloudflarePlaywright` class is specifically designed for handling complex Cloudflare challenges.

### Usage

```ruby
# Initialize CloudflarePlaywright
connector = ScraperConnectors::CloudflarePlaywright.new(headless: false)

# Use normally
data = connector.retrieve_data_from_connector('https://heavily-protected-site.com')
connector.cleanup
```

### Advanced Features

- **Comprehensive stealth browser arguments** - Extensive list of Chrome flags to avoid detection
- **Advanced stealth scripts** - Multiple JavaScript injections to mask automation
- **Intelligent challenge detection** - Detects various Cloudflare challenge patterns
- **Automatic challenge waiting** - Waits for challenges to complete with timeout handling
- **Enhanced headers** - Realistic HTTP headers that mimic real browsers
- **Block detection** - Detects if still being blocked after challenge attempts

### Browser Arguments Used

```ruby
CLOUDFLARE_BROWSER_ARGS = [
  '--disable-blink-features=AutomationControlled',
  '--no-first-run',
  '--no-service-autorun',
  '--password-store=basic',
  '--use-mock-keychain',
  '--disable-background-networking',
  '--disable-background-timer-throttling',
  '--disable-backgrounding-occluded-windows',
  '--disable-renderer-backgrounding',
  '--disable-features=TranslateUI',
  '--disable-ipc-flooding-protection',
  '--disable-extensions',
  '--disable-default-apps',
  '--disable-component-extensions-with-background-pages',
  '--disable-background-downloads',
  '--disable-add-to-shelf',
  '--disable-client-side-phishing-detection',
  '--disable-datasaver-prompt',
  '--disable-desktop-notifications',
  '--disable-domain-reliability',
  '--disable-features=VizDisplayCompositor',
  '--disable-hang-monitor',
  '--disable-prompt-on-repost',
  '--disable-sync',
  '--disable-web-security',
  '--metrics-recording-only',
  '--no-default-browser-check',
  '--no-pings',
  '--use-gl=swiftshader',
  '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
]
```

### JavaScript Stealth Modifications

The CloudflarePlaywright class injects several scripts to avoid detection:

- Removes `navigator.webdriver` property
- Spoofs `navigator.plugins`
- Mocks Chrome runtime
- Overrides permission queries
- Sets realistic language preferences

### When to Use

- Sites with advanced Cloudflare protection
- When basic stealth mode fails
- For production scraping of protected content
- When you need maximum success rate against Cloudflare

## Detection and Challenge Handling

Both classes can detect common Cloudflare challenge indicators:

- "Just a moment..."
- "Checking your browser"
- "Please wait while we check your browser"
- "DDoS protection by Cloudflare"
- "cf-browser-verification"
- "cf-challenge-running"

## Best Practices

### 1. Start with LocalPlaywright + Stealth Mode

```ruby
# Try the enhanced LocalPlaywright first
connector = ScraperConnectors::LocalPlaywright.new(stealth_mode: true)
```

### 2. Escalate to CloudflarePlaywright if Needed

```ruby
# If stealth mode fails, use the dedicated class
connector = ScraperConnectors::CloudflarePlaywright.new
```

### 3. Use Non-Headless Mode for Debugging

```ruby
# See what's happening in the browser
connector = ScraperConnectors::CloudflarePlaywright.new(headless: false)
```

### 4. Handle Timeouts Appropriately

```ruby
begin
  data = connector.retrieve_data_from_connector(url)
rescue StandardError => e
  puts "Scraping failed: #{e.message}"
  # Implement retry logic or fallback
end
```

### 5. Always Clean Up

```ruby
begin
  # ... scraping logic
ensure
  connector&.cleanup
end
```

## Testing

Both classes include test scripts that can be run directly:

```bash
# Test enhanced LocalPlaywright
ruby app/services/scraper_connectors/local_playwright.rb

# Test CloudflarePlaywright
ruby app/services/scraper_connectors/cloudflare_playwright.rb
```

## Environment Requirements

- Playwright must be installed: `npm install playwright`
- Chrome/Chromium browser
- Sufficient system resources for browser automation

## Troubleshooting

### Common Issues

1. **"SingletonLock" errors**: The classes include retry logic and automatic cleanup
2. **Timeout errors**: Increase timeout values or check network connectivity
3. **Still blocked**: Try running in non-headless mode to see what's happening
4. **Memory issues**: Ensure cleanup is called and old user data directories are removed

### Cleanup Utilities

Both classes provide force cleanup methods:

```ruby
# Clean all LocalPlaywright data
ScraperConnectors::LocalPlaywright.force_cleanup_all_user_data

# Clean all CloudflarePlaywright data
ScraperConnectors::CloudflarePlaywright.force_cleanup_all_user_data
```

## Migration Guide

If you're currently using the basic `LocalPlaywright` class:

### Before
```ruby
connector = ScraperConnectors::LocalPlaywright.new
```

### After (Enhanced)
```ruby
# Drop-in replacement with stealth mode
connector = ScraperConnectors::LocalPlaywright.new(stealth_mode: true)
```

### After (Dedicated Cloudflare Class)
```ruby
# Use the specialized class for better success rates
connector = ScraperConnectors::CloudflarePlaywright.new
```

The API remains the same, so no other code changes are required.
