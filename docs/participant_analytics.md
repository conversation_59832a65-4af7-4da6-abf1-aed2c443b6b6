# Participant Analytics System

A comprehensive analytics system that extends the Ahoy gem to track unique visitors and provide detailed insights into participant behavior.

## Overview

The Participant Analytics system creates a `Participant` model that aggregates data from Ahoy visits and events to provide meaningful insights into user behavior patterns. It includes:

- **Participant Model**: Tracks unique visitors based on <PERSON><PERSON>'s visitor_token
- **Analytics Service**: Calculates metrics and generates chart data
- **Dashboard Interface**: Multiple dashboard views with interactive charts
- **Admin Interface**: Administrate integration for participant management
- **Background Jobs**: Automated data synchronization
- **Rake Tasks**: Management and maintenance tools

## Features

### Core Metrics
- Total participants and active participants
- Visit distribution and frequency analysis
- Engagement scoring and behavior categorization
- Session duration analysis
- Geographic and device distribution
- Traffic source analysis
- Cohort analysis and retention metrics

### Dashboard Views
1. **Overview Dashboard**: High-level metrics and key charts
2. **Engagement Dashboard**: Detailed engagement analysis
3. **Traffic Dashboard**: Traffic sources and geographic data
4. **Behavior Dashboard**: User behavior patterns and categories

### Behavior Categories
- **Explorer**: Users who visit many different pages (>10 unique pages)
- **Engaged**: Users with high event activity (>20 events)
- **Regular**: Returning visitors with consistent activity (>3 visits)
- **Casual**: Users with minimal engagement (≤3 visits)
- **Inactive**: Users with no recorded activity

## Installation and Setup

### 1. Run the Migration
```bash
rails db:migrate
```

### 2. Sync Existing Data
```bash
# Sync all existing Ahoy data
rake participant_analytics:sync_all

# Or run in background
rake participant_analytics:sync_background
```

### 3. Generate Sample Data (Development)
```bash
rake participant_analytics:generate_sample_data
```

## Usage

### Accessing Dashboards

#### Public Analytics Dashboards
- Main Dashboard: `/participant_analytics`
- Engagement: `/participant_analytics/engagement`
- Traffic: `/participant_analytics/traffic`
- Behavior: `/participant_analytics/behavior`

#### Admin Interface
- Participant Management: `/superwiser/participants`
- Individual Analytics: `/superwiser/participants/:id/analytics`

### API Endpoints

All chart data is available via JSON API:

```ruby
# Overview metrics
GET /participant_analytics/api/overview

# Chart data endpoints
GET /participant_analytics/api/participants_over_time
GET /participant_analytics/api/visit_distribution
GET /participant_analytics/api/engagement_scores
GET /participant_analytics/api/behavior_categories
GET /participant_analytics/api/device_types
GET /participant_analytics/api/traffic_sources
GET /participant_analytics/api/geographic_distribution
GET /participant_analytics/api/visit_frequency
GET /participant_analytics/api/session_duration
GET /participant_analytics/api/cohort_analysis
GET /participant_analytics/api/top_participants
```

### Programmatic Access

```ruby
# Create analytics service
service = ParticipantAnalyticsService.new(date_range: 30.days.ago..Time.current)

# Get overview metrics
metrics = service.overview_metrics

# Get chart data
chart_data = service.participants_over_time_data

# Get top participants
top_users = service.top_participants(10)
```

## Data Model

### Participant Attributes

```ruby
# Core identification
visitor_token           # Unique identifier from Ahoy
first_visit_at         # Timestamp of first visit
last_visit_at          # Timestamp of most recent visit

# Aggregated metrics
total_visits           # Number of visits
total_events           # Number of events
total_page_views       # Number of page views
unique_pages_visited   # Count of unique pages
average_session_duration # Average time per session (minutes)

# Visitor classification
returning_visitor      # Boolean flag
behavior_category      # Calculated category (explorer, engaged, etc.)
engagement_score       # Calculated engagement score (0-200+)

# First visit context
first_referrer         # Initial referrer URL
first_landing_page     # Initial landing page
first_utm_source       # UTM source parameter
first_utm_medium       # UTM medium parameter
first_utm_campaign     # UTM campaign parameter
first_country          # Geographic location
first_city             # City location
first_device_type      # Device type (Desktop, Mobile, Tablet)
first_browser          # Browser name
first_os               # Operating system

# JSON storage
engagement_metrics     # Calculated engagement data
behavior_patterns      # Behavioral analysis data
```

### Engagement Score Calculation

The engagement score is calculated using multiple factors:

```ruby
base_score = [total_visits * 10, 100].min        # Max 100 points
event_bonus = [total_events * 2, 50].min         # Max 50 points
page_bonus = [unique_pages_visited * 5, 30].min  # Max 30 points
returning_bonus = returning_visitor? ? 20 : 0     # 20 points if returning

total_score = base_score + event_bonus + page_bonus + returning_bonus
```

## Background Processing

### Automatic Sync
Ahoy visits automatically trigger participant updates via callbacks:

```ruby
# In Ahoy::Visit model
after_create :sync_participant_data
after_update :sync_participant_data, if: :saved_change_to_visitor_token?
```

### Manual Sync
Use the background job for bulk operations:

```ruby
# Sync all participants
ParticipantSyncJob.perform_later

# Sync specific participant
ParticipantSyncJob.perform_later(visitor_token: 'abc123')

# Sync with custom batch size
ParticipantSyncJob.perform_later(batch_size: 500)
```

## Rake Tasks

### Data Management
```bash
# Sync all participants from Ahoy data
rake participant_analytics:sync_all

# Sync in background job
rake participant_analytics:sync_background

# Sync specific participant
rake participant_analytics:sync_participant[VISITOR_TOKEN]

# Clear analytics cache
rake participant_analytics:clear_cache
```

### Development and Testing
```bash
# Generate sample data for testing
rake participant_analytics:generate_sample_data

# Show analytics summary
rake participant_analytics:summary

# Validate data integrity
rake participant_analytics:validate

# Setup periodic sync instructions
rake participant_analytics:setup_periodic_sync
```

## Caching Strategy

The system uses Rails cache with 1-hour expiration for performance:

- All analytics queries are cached with date-range-specific keys
- Cache is automatically cleared after sync operations
- Manual cache clearing available via rake task

## Performance Considerations

### Database Indexes
The migration includes optimized indexes for:
- Visitor token lookups (unique)
- Date range queries (first_visit_at, last_visit_at)
- Filtering queries (total_visits, returning_visitor)
- JSON queries (engagement_metrics, behavior_patterns with GIN indexes)

### Batch Processing
- Sync operations process data in configurable batches (default: 1000)
- Error handling with individual fallback processing
- Progress logging for large datasets

### Query Optimization
- Uses `includes` for association loading
- Implements efficient grouping and aggregation
- Leverages database-level calculations where possible

## Customization

### Adding Custom Metrics
Extend the `ParticipantAnalyticsService` to add new metrics:

```ruby
class CustomParticipantAnalyticsService < ParticipantAnalyticsService
  def custom_metric_data
    # Your custom calculation
  end
end
```

### Custom Behavior Categories
Override the `behavior_category` method in the Participant model:

```ruby
def behavior_category
  return 'power_user' if total_visits > 50
  # ... existing logic
end
```

### Additional Chart Types
Add new chart endpoints to the controller and corresponding service methods.

## Troubleshooting

### Common Issues

1. **Missing Data**: Run `rake participant_analytics:sync_all`
2. **Performance Issues**: Check database indexes and consider batch size adjustment
3. **Cache Issues**: Clear cache with `rake participant_analytics:clear_cache`
4. **Data Integrity**: Run `rake participant_analytics:validate`

### Monitoring
- Check background job status in Mission Control
- Monitor cache hit rates
- Review sync job logs for errors

## Security Considerations

- Visitor tokens are anonymized identifiers
- No personally identifiable information is stored
- Admin access requires authentication
- API endpoints respect application authentication

## Future Enhancements

Potential areas for expansion:
- Real-time analytics updates
- Advanced segmentation features
- Predictive analytics
- A/B testing integration
- Export functionality
- Custom dashboard builder
