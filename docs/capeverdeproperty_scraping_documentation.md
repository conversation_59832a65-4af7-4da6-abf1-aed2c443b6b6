# Cape Verde Property Scraping Logic and Process Documentation

## Overview

The Cape Verde Property scraping system is designed to extract property listing data from capeverdeproperty.co.uk using a robust HTML parsing architecture that handles various page structures and content formats. The system follows the same architectural patterns established for OnTheMarket scraping.

## Architecture Components

### 1. Models

#### ScrapeItemFromCapeverdeproperty (`app/models/scrape_item_from_capeverdeproperty.rb`)
- **Purpose**: Specialized model for Cape Verde Property property page scraping
- **Inheritance**: Extends `ScrapeItem` base class
- **Key Features**:
  - Default scope filters for Cape Verde Property-specific items
  - HTML parsing and property data extraction
  - Data mapping to standardized asset and listing schemas
  - Image URL extraction and processing
  - Location coordinate extraction from Google Maps

#### ScrapeItemFromCapeverdepropertySearch (`app/models/scrape_item_from_capeverdeproperty_search.rb`)
- **Purpose**: Handles Cape Verde Property search results pages
- **Key Features**:
  - Processes HTML data from search result pages
  - Extracts summary listing information
  - Maps search results to property summary format
  - Handles multiple property listings per page

### 2. Services

#### ScraperConnectors::Regular (`app/services/scraper_connectors/regular.rb`)
- **Purpose**: HTTP client for fetching HTML content from Cape Verde Property
- **Key Features**:
  - Standard HTTP requests without special headers
  - Error handling for redirects, HTTP errors, and network issues
  - Content validation and length checks
  - Suitable for static HTML content

#### RealtyParsers::ParseCapeverdepropertyListingsHtml (`app/services/realty_parsers/parse_capeverdeproperty_listings_html.rb`)
- **Purpose**: Parses Cape Verde Property HTML content
- **Key Features**:
  - Extracts property information from HTML structure
  - Handles missing or incomplete data gracefully
  - Supports multiple property types (apartment, villa, plot, house)
  - Extracts features, images, and contact information

#### Pasarelas::CapeverdepropertyPasarela (`app/services/pasarelas/capeverdeproperty_pasarela.rb`)
- **Purpose**: Data transformation gateway
- **Key Features**:
  - Converts parsed HTML data to standardized schemas
  - Maps property data to listing and asset formats
  - Handles feature categorization
  - Provides data validation and defaults

### 3. Shared Helpers

#### RealtyScrapers::SharedScraperHelpers
- **Purpose**: Common functionality across all scrapers (inherited from existing system)
- **Key Features**:
  - URL validation and canonicalization
  - Image extraction utilities
  - Text cleaning and normalization
  - Content mapping helpers

## Scraping Process Flow

### 1. URL Processing
```ruby
# Example: Creating a scrape item for Cape Verde Property
scrape_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(url)
```

**Steps:**
1. Parse and validate the input URL
2. Generate unique scrape identifier
3. Set Cape Verde Property-specific flags
4. Initialize scraper connector

### 2. Content Retrieval
```ruby
# Via ScraperConnectors::Regular
content = scraper_connector.retrieve_data_from_connector(uri)
```

**Process:**
1. Make standard HTTP request
2. Handle Cape Verde Property page structure
3. Validate content length and structure
4. Extract metadata and save to database

### 3. Data Extraction
```ruby
# Extract structured property data
property_data = scrape_item.property_hash_from_scrape_item
```

**Process:**
1. Parse HTML with Nokogiri
2. Extract title, price, and basic information
3. Extract location data and coordinates
4. Extract features and amenities
5. Extract images and agent contact details

### 4. Data Mapping
```ruby
# Map to standardized schemas
listing_data = map_property_to_listing_schema(property)
asset_data = map_property_to_asset_schema(property)
```

**Process:**
1. Convert to listing schema (price, features, description)
2. Convert to asset schema (location, bedrooms, bathrooms)
3. Handle missing data with appropriate defaults
4. Validate required fields

## Data Structure and Extraction

### Property Information Extraction

#### Basic Information
- **Title**: Extracted from `<h1>` element
- **Price**: Found in `<h2>` elements containing "€"
- **Property Type**: Inferred from title (apartment, villa, plot, house)
- **Bedrooms/Bathrooms**: Extracted from title and feature lists

#### Location Information
- **City/Region**: Parsed from title (e.g., "SANTA MARIA, SAL")
- **Country**: Default "Cape Verde"
- **Coordinates**: Extracted from Google Maps static image URLs
- **Address**: Constructed from available location information

#### Features and Amenities
- **Main Features**: Extracted from `<li>` elements and feature sections
- **Furnished Status**: Detected from description and features
- **Sea View**: Detected from text content
- **Condition**: Extracted from feature lists

#### Agent Information
- **Company**: Default "Cape Verde Property"
- **Contact Details**: Extracted from "OFFICE DETAILS" section
- **Phone Numbers**: Multiple phone numbers supported
- **Email**: Extracted from mailto links

#### Images
- **Source**: Images from wdcdn.co domain
- **Quality**: Converted from WebP to JPG format
- **Resolution**: Uses high-quality `/l/` path when available

### Search Results Processing

#### Property Listings
- **Property Cards**: Extracted from `.property-item` or similar selectors
- **Basic Info**: Title, price, bedrooms, bathrooms
- **Links**: Full property page URLs
- **Images**: Thumbnail images when available

## Key Features

### Robust Data Extraction
- **Multiple Selectors**: Tries various CSS selectors for reliability
- **Fallback Logic**: Graceful handling of missing elements
- **Text Cleaning**: Removes unwanted characters and normalizes content
- **Type Detection**: Automatic property type classification

### Error Handling
- **Content Validation**: Minimum content length checks
- **Malformed HTML**: Continues processing with partial data
- **Missing Data**: Provides sensible defaults
- **Network Issues**: Proper error propagation

### Image Processing
- **Format Conversion**: WebP to JPG for compatibility
- **Quality Enhancement**: Prefers high-resolution versions
- **Deduplication**: Removes duplicate image URLs
- **Validation**: Ensures images are from expected domains

### Location Processing
- **Coordinate Extraction**: From Google Maps static images
- **Address Parsing**: Splits location into city/region components
- **Geocoding**: Coordinates when available from maps
- **Standardization**: Consistent location formatting

## Configuration

### Portal Configuration
```ruby
'capeverdeproperty' => {
  pasarela: 'Pasarelas::CapeverdepropertyPasarela',
  scrape_class: 'ScrapeItemFromCapeverdeproperty',
  connector: 'ScraperConnectors::Regular',
  method: :find_or_create_for_h2c_capeverdeproperty,
  scraped_content_column_name: 'full_content_before_js',
  include_trailing_slash: false
}
```

### Flag Configuration
- **Flag Position**: 14 in the flag system
- **Flag Name**: `:scrape_is_capeverdeproperty`
- **Column**: `realty_scraped_item_flags`

### HTTP Configuration
- **Connector**: ScraperConnectors::Regular
- **Trailing Slash**: Not required
- **Content Type**: HTML in `full_content_before_js`
- **User Agent**: Standard browser user agent

## Database Schema

### RealtyScrapedItem Extensions
- **New Flag**: `scrape_is_capeverdeproperty` (position 14)
- **Content Storage**: `full_content_before_js` column
- **Image Storage**: `all_page_images` JSONB column
- **Metadata**: Standard scrape item fields

### Related Models
- **SaleListing**: Created from listing data
- **RealtyAsset**: Created from asset data
- **SummaryListing**: Created from search results
- **RealtySearchQuery**: Links search operations

## Testing Strategy

### Unit Tests
- **Model Tests**: `spec/models/scrape_item_from_capeverdeproperty_spec.rb`
- **Search Tests**: `spec/models/scrape_item_from_capeverdeproperty_search_spec.rb`
- **Parser Tests**: `spec/services/realty_parsers/parse_capeverdeproperty_listings_html_spec.rb`
- **Pasarela Tests**: `spec/services/pasarelas/capeverdeproperty_pasarela_spec.rb`

### Integration Tests
- **End-to-End**: `spec/integration/capeverdeproperty_integration_spec.rb`
- **Workflow Tests**: Complete scraping workflow validation
- **Error Handling**: Malformed content and network error scenarios

### Test Data
- **Sample HTML**: `spec/fixtures/files/capeverdeproperty_sample.html`
- **Mock Data**: Realistic property information
- **Edge Cases**: Missing data, malformed HTML, empty content

### Rake Task
```bash
bundle exec rake h2c:test:test_capeverdeproperty_scraping
```

## Monitoring and Maintenance

### Key Metrics
- **Success Rate**: Percentage of successful scrapes
- **Content Quality**: Average content length and completeness
- **Data Extraction**: Success rate of property data extraction
- **Image Processing**: Number of images successfully extracted

### Error Monitoring
- **Content Validation**: Track scrapes with insufficient content
- **Parsing Errors**: Monitor HTML parsing failures
- **Data Mapping**: Track schema mapping issues
- **Network Issues**: HTTP request failures

### Maintenance Tasks
- **HTML Structure Changes**: Monitor for Cape Verde Property site updates
- **Selector Updates**: Update CSS selectors if page structure changes
- **Data Validation**: Regular validation of extracted data quality
- **Performance Optimization**: Monitor processing times

## Example Usage

### Basic Property Scraping
```ruby
# Create scrape item
scrape_item = ScrapeItemFromCapeverdeproperty.find_or_create_for_h2c_capeverdeproperty(
  'https://www.capeverdeproperty.co.uk/property/cv353caoceanocvp4/cv/sal/santa-maria/ca-oceano-2-bedroom-apartment/apartment/2-bedrooms'
)

# Retrieve content (would normally be done by scraper)
scrape_item.retrieve_and_set_rsi_content('ScraperConnectors::Regular')

# Extract property data
property_data = scrape_item.property_hash_from_scrape_item

# Access structured data
listing_data = property_data['listing_data']
asset_data = property_data['asset_data']
images = property_data['listing_image_urls']
```

### Search Results Processing
```ruby
# Create search scrape item
search_scrape = ScrapeItemFromCapeverdepropertySearch.find_or_create_for_capeverdeproperty_search(
  'https://www.capeverdeproperty.co.uk/buy/property-for-sale/'
)

# Process search results
search_scrape.summary_sale_listings_from_scrape_item
```

### Using Parser Directly
```ruby
# Parse HTML content directly
html_content = File.read('sample.html')
property_data = RealtyParsers::ParseCapeverdepropertyListingsHtml.property_hash_from_html(html_content)
```

### Using Pasarela for Data Transformation
```ruby
# Transform scrape item data
result = Pasarelas::CapeverdepropertyPasarela.extract_listing_and_asset_from_scrape_item(scrape_item)

listing_data = result[:listing_data]
asset_data = result[:asset_data]
images = result[:images]
```

## Comparison with OnTheMarket Implementation

### Similarities
- **Architecture**: Same model/service/helper structure
- **Base Classes**: Inherits from ScrapeItem
- **Data Schemas**: Uses same listing and asset schemas
- **Error Handling**: Similar error handling patterns
- **Testing**: Same testing approach and structure

### Differences
- **Data Source**: HTML parsing vs JSON extraction
- **Connector**: Regular HTTP vs specialized connectors
- **Complexity**: Simpler HTML structure vs complex JavaScript data
- **Features**: Different feature extraction patterns
- **Location**: Cape Verde vs UK market focus

### Advantages
- **Simplicity**: Easier to maintain HTML parsing
- **Reliability**: Less dependent on JavaScript execution
- **Performance**: Faster processing without JavaScript rendering
- **Robustness**: HTML parsing more tolerant of changes

### Considerations
- **Data Richness**: May have less structured data than JSON
- **Maintenance**: Requires monitoring for HTML changes
- **Extraction Quality**: Dependent on HTML structure consistency
- **Feature Detection**: More heuristic-based feature extraction
