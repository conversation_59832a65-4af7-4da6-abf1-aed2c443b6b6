# Realty Game Creation with Pre-Scraped Content

This document explains how to use the new pre-scraped content functionality for creating realty games, which allows you to scrape content locally and send it to the API server, avoiding the need for the remote server to perform scraping operations.

## Overview

The new functionality provides an alternative to the existing `create_realty_game` method by introducing:

1. **Local scraping**: Content is scraped on the client side
2. **Data serialization**: Scraped content is serialized for transmission
3. **Remote processing**: The API server processes the pre-scraped content without additional scraping

## Benefits

- **Reduced server load**: No scraping operations on the API server
- **Better error handling**: Scraping errors are handled locally
- **Faster processing**: No waiting for remote scraping operations
- **Network efficiency**: Only processed data is transmitted

## Usage

### 1. Using RealtyPuntCreator

```ruby
# Create an instance of the creator
creator = RealtyPunts::RealtyPuntCreator.new

# Use the new method with pre-scraped content
realty_game_id = creator.create_realty_game_with_pre_scraped_content('db/realty_punts/latest.json')
```

### 2. Input File Format

The input JSON file remains the same:

```json
{
  "api_prefix": "https://be-medo.propertywebbuilder.com/api_mgmt/v4",
  "scoot_subdomain": "your-subdomain",
  "vendor_name": "buenavista",
  "retrieval_portal": "buenavista",
  "property_refs": ["12345", "67890", "11111"],
  "realty_game_slug": "regular-game"
}
```

### 3. API Endpoints

#### Create Game with Pre-Scraped Listing

```
POST /api_mgmt/v4/realty_games_mgmt/init_game_with_pre_scraped_listing
```

**Parameters:**
- `vendor_name`: Portal vendor name
- `scoot_subdomain`: Tenant subdomain
- `retrieval_portal`: Portal identifier
- `retrieval_end_point`: Original URL
- `realty_game_slug`: Game identifier
- `scrape_item_data`: Serialized scrape item data

#### Add Pre-Scraped Listing to Game

```
POST /api_mgmt/v4/realty_games_mgmt/add_pre_scraped_listing_to_game
```

**Parameters:**
- `realty_game_id`: Existing game ID
- `retrieval_portal`: Portal identifier
- `retrieval_end_point`: Original URL
- `scrape_item_data`: Serialized scrape item data

## Implementation Details

### Scrape Item Serialization

The system serializes the following scrape item attributes:

```ruby
{
  scrape_class: 'ScrapeItemFromBuenavista',
  scrapable_url: 'https://example.com/property/123',
  scrape_unique_url: 'https://example.com/property/123#h2c',
  full_content_before_js: '{"property": {"Price": 250000}}',
  full_content_after_js: nil,
  title: 'Property Title',
  description: 'Property Description',
  page_locale_code: 'en',
  is_valid_scrape: true,
  content_is_html: false,
  content_is_json: true,
  content_is_xml: false,
  all_page_images: [],
  script_json: {},
  # Portal-specific flags
  scrape_is_buenavista: true,
  scrape_is_onthemarket: false,
  # ... other portal flags
}
```

### Supported Portals

The system supports the same portals as the original implementation:

- **Buenavista**: JSON API scraping
- **OnTheMarket**: HTML scraping with regular connector
- **Zoopla**: JavaScript scraping with Playwright
- **Rightmove**: JavaScript scraping with Playwright
- **Purplebricks**: HTML scraping

### Error Handling

The system includes comprehensive error handling:

- **Local scraping errors**: Handled during the scraping phase
- **Serialization errors**: Caught during data preparation
- **API errors**: Returned from the remote server
- **Validation errors**: Portal and URL validation

## Process Flow

1. **Read Configuration**: Parse input JSON file
2. **Local Scraping**: Create scrape items locally for each property
3. **Data Serialization**: Convert scrape items to transmittable format
4. **API Calls**: Send pre-scraped data to remote endpoints
5. **Remote Processing**: Server creates listings from pre-scraped content
6. **Game Association**: Link listings to the realty game

## Testing

Run the test suite to verify functionality:

```bash
# Test the service class
rspec spec/services/realty_punts/realty_punt_creator_spec.rb

# Test the API endpoints
rspec spec/requests/api_mgmt/v4/realty_games_mgmt_pre_scraped_spec.rb
```

## Migration from Original Method

To migrate from the original `create_realty_game` method:

1. Replace method calls:
   ```ruby
   # Old method
   creator.create_realty_game('input.json')
   
   # New method
   creator.create_realty_game_with_pre_scraped_content('input.json')
   ```

2. No changes needed to input files or configuration
3. The API endpoints are additive - original endpoints remain functional

## Performance Considerations

- **Local scraping**: May take longer initially due to local processing
- **Network transfer**: Larger payloads due to scraped content
- **Server processing**: Faster due to no scraping operations
- **Overall**: Generally faster for multiple properties

## Troubleshooting

### Common Issues

1. **Scraping failures**: Check portal availability and URL format
2. **Serialization errors**: Verify scrape item data completeness
3. **API errors**: Check server logs for detailed error messages
4. **Portal detection**: Ensure URLs match supported portal patterns

### Debug Mode

Enable debug logging to troubleshoot issues:

```ruby
creator = RealtyPunts::RealtyPuntCreator.new(nil, Logger.new(STDOUT, level: Logger::DEBUG))
```
