# # frozen_string_literal: true

# # https://github.com/huacnlee/rails-settings-cached
# # TODO - investigate using above instead of below
module HpgConfig
  class General
    CACHE_EXPIRY_LOW = 5.minutes
    CACHE_EXPIRY_MEDIUM = 50.minutes
    CACHE_EXPIRY_HIGH = 6.hours
  end
end
# https://github.com/etewiah/pwb-premium/tree/main/config/initializers
# module PwbConfig
#   class General
#     PromotionsSearchPageSlug = 'search_promotions'
#     SalesSearchPageSlug = 'search_sales'
#     # ListingsBackend is used to decide which query object to use
#     # TODO - make it configurable

#     # In dev .env.local file is used to set this:
#     ListingsBackend = if ENV['LISTINGS_BACKEND'] == 'resales'
#                         'resales'
#                       else
#                         'default_backend'
#                       end

#     EnableFavourites = true
#     EnableUserSubmissions = true
#     EnableCloudinary = false
#     # EnableCurrencyConversions = true
#   end

#   # class Agency
#   #   DisplayName = ""
#   # end

#   class Resales
#     # http://www-3.resales-online.com/live/KnowledgeBase/DisplayContent.asp P_Lang
#     # 1 = English (default), 2 = Spanish, 3 = German, 4 = French, 5 = Dutch, 6 = Danish, 7 = Russian, 8 = Swedish, 9 = Polish, 10 = Norwegian, 11 = Turkish
#     LangCodes = {
#       en: '1',
#       es: '2',
#       fr: '4',
#       nl: '5'
#     }.freeze
#     CodesToLang = {
#       "1": 'en',
#       "2": 'es',
#       "4": 'fr',
#       "5": 'nl'
#     }.freeze
#   end

#   class Urls
#     # Can use in rails router but not vue router as
#     # routes have to be ready before  hitting server
#     SlugLookups = {
#       about_us: {
#         en: 'about-us',
#         es: 'sobre-nosotros',
#         fr: 'about-us',
#         nl: 'about-us',
#         it: 'about-us'
#       },
#       # should find alternative way of looking up about_us as its
#       # a slug that can be edited
#       # - or perhaps I should disable editing of that page's slug..
#       # All for the sake of a link in crappy svg map :(
#       blog_index: {
#         en: 'blog-posts',
#         es: 'blog-posts',
#         fr: 'blog-posts',
#         nl: 'blog-posts',
#         it: 'blog-posts'
#       },
#       fav_index: {
#         en: 'favourites',
#         es: 'favourites',
#         fr: 'favourites',
#         nl: 'favourites',
#         it: 'favourites'
#       }
#     }.freeze
#     # Used in dev/sites-2018/pwb-malaga/app/decorators/listing_decorator.rb
#     # TODO - use in other decorators
#     Preambles = {
#       areas: {
#         es: 'areas',
#         en: 'areas',
#         fr: 'areas',
#         nl: 'areas',
#         it: 'areas'
#       },
#       # province: {
#       #   es: "province",
#       #   en: "province",
#       #   fr: "province",
#       #   nl: "province",
#       #   it: "province",
#       # },
#       # municipality: {
#       #   es: "municipality",
#       #   en: "municipality",
#       #   fr: "municipality",
#       #   nl: "municipality",
#       #   it: "municipality",
#       # },
#       # zone: {
#       #   es: "zone",
#       #   en: "zone",
#       #   fr: "zone",
#       #   nl: "zone",
#       #   it: "zone",
#       # },
#       # quarter: {
#       #   es: "quarter",
#       #   en: "quarter",
#       #   fr: "quarter",
#       #   nl: "quarter",
#       #   it: "quarter",
#       # },
#       # canned: {
#       #   es: "l",
#       #   en: "l",
#       #   fr: "l",
#       #   nl: "l",
#       #   it: "l",
#       # },
#       city: {
#         es: 'ciudad',
#         en: 'city',
#         fr: 'ciudad',
#         nl: 'city',
#         it: 'city'
#       },
#       sale_search: {
#         es: 'propiedades',
#         en: 'properties',
#         fr: 'immeubles',
#         nl: 'properties',
#         it: 'properties'
#       },
#       rental_search: {
#         es: 'propiedades',
#         en: 'properties',
#         fr: 'immeubles',
#         nl: 'properties',
#         it: 'properties'
#       },
#       sale: {
#         es: 'propiedades',
#         en: 'properties',
#         fr: 'immeubles',
#         nl: 'properties',
#         it: 'properties'
#       },
#       # rental: {
#       #   es: 'alquiler',
#       #   en: 'rent'
#       # },
#       promotion: {
#         en: 'developments',
#         es: 'promociones',
#         fr: 'developments',
#         nl: 'developments',
#         it: 'developments'
#         # en: "promotions"
#       }
#     }.freeze
#     Prefixes = {
#       sale: 's',
#       rental: 'r',
#       promotion: 'pr'
#     }.freeze
#   end
# end
