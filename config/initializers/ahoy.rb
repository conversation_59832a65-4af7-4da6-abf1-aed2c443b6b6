class Ahoy::Store < Ahoy::DatabaseStore
end

# set to true for JavaScript tracking
Ahoy.api = true
# seems above adds below to routes file
# mount Ahoy::Engine, at: '/ahoy'

# june 2025 - my understanding was that
# Ahoy.api_only = true would stop visits being automatically created
# Seems it was stopping tokens being created for ahoy visits though.
Ahoy.api_only = false

# seems below was needed for visit tokens too
Ahoy.server_side_visits = :always

# set to true for geocoding (and add the geocoder gem to your Gemfile)
# we recommend configuring local geocoding as well
# see https://github.com/ankane/ahoy#geocoding
Ahoy.geocode = true
